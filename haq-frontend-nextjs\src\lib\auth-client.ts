// Client-side authentication utilities
// This file contains only validation functions that can be safely used on the client side

// Types (re-exported for client use)
export interface User {
  user_id: string;
  username: string;
  email: string;
  role: 'user' | 'admin';
  created_at: string;
  updated_at: string;
}

export interface JWTPayload {
  user_id: string;
  role: 'user' | 'admin';
  iat?: number;
  exp?: number;
}

export interface AuthResult {
  success: boolean;
  user?: User;
  token?: string;
  error?: string;
}

// Input validation utilities (client-safe)
export class ValidationUtils {
  /**
   * Validate email format
   * @param email - Email to validate
   * @returns boolean - True if valid
   */
  static isValidEmail(email: string): boolean {
    const emailRegex = /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;
    return emailRegex.test(email) && email.length <= 255;
  }

  /**
   * Validate username format
   * @param username - Username to validate
   * @returns boolean - True if valid
   */
  static isValidUsername(username: string): boolean {
    // Username: 3-50 characters, alphanumeric and underscores only
    const usernameRegex = /^[a-zA-Z0-9_]{3,50}$/;
    return usernameRegex.test(username);
  }

  /**
   * Validate password strength
   * @param password - Password to validate
   * @returns { isValid: boolean, errors: string[] }
   */
  static validatePassword(password: string): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (password.length < 8) {
      errors.push('Password must be at least 8 characters long');
    }

    if (password.length > 128) {
      errors.push('Password must be less than 128 characters');
    }

    if (!/[a-z]/.test(password)) {
      errors.push('Password must contain at least one lowercase letter');
    }

    if (!/[A-Z]/.test(password)) {
      errors.push('Password must contain at least one uppercase letter');
    }

    if (!/[0-9]/.test(password)) {
      errors.push('Password must contain at least one number');
    }

    if (!/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) {
      errors.push('Password must contain at least one special character');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Sanitize input string to prevent XSS
   * @param input - Input string to sanitize
   * @returns string - Sanitized string
   */
  static sanitizeInput(input: string): string {
    return input
      .trim()
      .replace(/[<>]/g, '') // Remove potential HTML tags
      .substring(0, 1000); // Limit length
  }
}

// Client-side authentication helper functions
export class AuthHelpers {
  /**
   * Check if a token is expired (client-side check only, not cryptographically secure)
   * @param token - JWT token to check
   * @returns boolean - True if token appears expired
   */
  static isTokenExpired(token: string): boolean {
    try {
      // Simple base64 decode of JWT payload (not secure verification)
      const parts = token.split('.');
      if (parts.length !== 3) return true;
      
      const payload = JSON.parse(atob(parts[1]));
      const now = Math.floor(Date.now() / 1000);
      
      return payload.exp && payload.exp < now;
    } catch (error) {
      return true; // If we can't parse it, consider it expired
    }
  }

  /**
   * Extract user info from token (client-side only, not secure)
   * @param token - JWT token
   * @returns Partial user info or null
   */
  static extractUserInfo(token: string): { user_id?: string; role?: string } | null {
    try {
      const parts = token.split('.');
      if (parts.length !== 3) return null;
      
      const payload = JSON.parse(atob(parts[1]));
      return {
        user_id: payload.user_id,
        role: payload.role
      };
    } catch (error) {
      return null;
    }
  }
}

module.exports = {

"[project]/.next-internal/server/app/api/companies/[id]/reviews/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/stream [external] (stream, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("stream", () => require("stream"));

module.exports = mod;
}}),
"[externals]/http [external] (http, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("http", () => require("http"));

module.exports = mod;
}}),
"[externals]/url [external] (url, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("url", () => require("url"));

module.exports = mod;
}}),
"[externals]/punycode [external] (punycode, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("punycode", () => require("punycode"));

module.exports = mod;
}}),
"[externals]/https [external] (https, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("https", () => require("https"));

module.exports = mod;
}}),
"[externals]/zlib [external] (zlib, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("zlib", () => require("zlib"));

module.exports = mod;
}}),
"[externals]/events [external] (events, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("events", () => require("events"));

module.exports = mod;
}}),
"[externals]/net [external] (net, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("net", () => require("net"));

module.exports = mod;
}}),
"[externals]/tls [external] (tls, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("tls", () => require("tls"));

module.exports = mod;
}}),
"[externals]/crypto [external] (crypto, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("crypto", () => require("crypto"));

module.exports = mod;
}}),
"[externals]/buffer [external] (buffer, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("buffer", () => require("buffer"));

module.exports = mod;
}}),
"[project]/src/lib/supabase.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "createClientComponentClient": (()=>createClientComponentClient),
    "createServerClient": (()=>createServerClient),
    "supabase": (()=>supabase)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$supabase$2d$js$2f$dist$2f$module$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@supabase/supabase-js/dist/module/index.js [app-route] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$ssr$2f$dist$2f$module$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@supabase/ssr/dist/module/index.js [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$ssr$2f$dist$2f$module$2f$createBrowserClient$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@supabase/ssr/dist/module/createBrowserClient.js [app-route] (ecmascript)");
;
;
// Supabase configuration
const supabaseUrl = ("TURBOPACK compile-time value", "https://wqbuilazpyxpwyuwuqpi.supabase.co");
const supabaseAnonKey = ("TURBOPACK compile-time value", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6IndxYnVpbGF6cHl4cHd5dXd1cXBpIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTA1NTYyNDMsImV4cCI6MjA2NjEzMjI0M30.GeRI54Rskwdbfm9_lRxy1-7YQ8vA74JWblNF2GRpzrI");
// Singleton pattern to prevent multiple client instances (Context7 best practice)
let browserClientInstance = null;
let serverClientInstance = null;
const createClientComponentClient = ()=>{
    if ("TURBOPACK compile-time truthy", 1) {
        // Server-side: create a new instance each time
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$ssr$2f$dist$2f$module$2f$createBrowserClient$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createBrowserClient"])(supabaseUrl, supabaseAnonKey);
    }
    "TURBOPACK unreachable";
};
const createServerClient = ()=>{
    if (!serverClientInstance) {
        serverClientInstance = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$supabase$2d$js$2f$dist$2f$module$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createClient"])(supabaseUrl, supabaseAnonKey);
    }
    return serverClientInstance;
};
const supabase = createServerClient();
}}),
"[externals]/jsdom [external] (jsdom, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("jsdom", () => require("jsdom"));

module.exports = mod;
}}),
"[project]/src/lib/sanitization.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Input Sanitization Utilities
 * Following HAQ-rules.md RULE-603: INPUT_SANITIZATION
 * 
 * This module provides consistent sanitization across the application
 * to prevent XSS attacks and ensure data integrity.
 */ __turbopack_context__.s({
    "PIIDetection": (()=>PIIDetection),
    "SanitizationConfig": (()=>SanitizationConfig),
    "ValidationUtils": (()=>ValidationUtils),
    "default": (()=>__TURBOPACK__default__export__),
    "sanitizeBasicText": (()=>sanitizeBasicText),
    "sanitizeEmail": (()=>sanitizeEmail),
    "sanitizeObject": (()=>sanitizeObject),
    "sanitizeReviewText": (()=>sanitizeReviewText),
    "sanitizeSearchQuery": (()=>sanitizeSearchQuery),
    "sanitizeUrl": (()=>sanitizeUrl)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$dompurify$2f$dist$2f$purify$2e$es$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/dompurify/dist/purify.es.mjs [app-route] (ecmascript)");
;
// Server-side sanitization (Node.js environment)
let purify;
if ("TURBOPACK compile-time truthy", 1) {
    // Server-side: Use jsdom for DOMPurify
    const { JSDOM } = __turbopack_context__.r("[externals]/jsdom [external] (jsdom, cjs)");
    const window = new JSDOM('').window;
    purify = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$dompurify$2f$dist$2f$purify$2e$es$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])(window);
} else {
    "TURBOPACK unreachable";
}
const SanitizationConfig = {
    // For review text content - no HTML allowed
    REVIEW_TEXT: {
        ALLOWED_TAGS: [],
        ALLOWED_ATTR: [],
        KEEP_CONTENT: true,
        RETURN_DOM: false
    },
    // For search queries - very strict
    SEARCH_QUERY: {
        ALLOWED_TAGS: [],
        ALLOWED_ATTR: [],
        KEEP_CONTENT: true,
        RETURN_DOM: false
    },
    // For company names and basic text
    BASIC_TEXT: {
        ALLOWED_TAGS: [],
        ALLOWED_ATTR: [],
        KEEP_CONTENT: true,
        RETURN_DOM: false
    }
};
function sanitizeReviewText(text) {
    if (!text || typeof text !== 'string') {
        return null;
    }
    const sanitized = purify.sanitize(text.trim(), SanitizationConfig.REVIEW_TEXT);
    return sanitized.trim() || null;
}
function sanitizeSearchQuery(query) {
    if (!query || typeof query !== 'string') {
        return '';
    }
    const sanitized = purify.sanitize(query.trim(), SanitizationConfig.SEARCH_QUERY);
    return sanitized.trim();
}
function sanitizeBasicText(text) {
    if (!text || typeof text !== 'string') {
        return null;
    }
    const sanitized = purify.sanitize(text.trim(), SanitizationConfig.BASIC_TEXT);
    return sanitized.trim() || null;
}
function sanitizeEmail(email) {
    if (!email || typeof email !== 'string') {
        return null;
    }
    // Basic email validation regex
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    const trimmedEmail = email.trim().toLowerCase();
    if (!emailRegex.test(trimmedEmail)) {
        return null;
    }
    // Sanitize to remove any potential XSS
    const sanitized = purify.sanitize(trimmedEmail, SanitizationConfig.BASIC_TEXT);
    return sanitized;
}
function sanitizeUrl(url) {
    if (!url || typeof url !== 'string') {
        return null;
    }
    try {
        // Validate URL format
        const urlObj = new URL(url.trim());
        // Only allow http and https protocols
        if (![
            'http:',
            'https:'
        ].includes(urlObj.protocol)) {
            return null;
        }
        // Sanitize the URL string
        const sanitized = purify.sanitize(urlObj.toString(), SanitizationConfig.BASIC_TEXT);
        return sanitized;
    } catch (error) {
        // Invalid URL format
        return null;
    }
}
function sanitizeObject(obj, sanitizer = sanitizeBasicText) {
    const sanitized = {};
    for (const [key, value] of Object.entries(obj)){
        if (typeof value === 'string') {
            const sanitizedValue = sanitizer(value);
            if (sanitizedValue !== null) {
                sanitized[key] = sanitizedValue;
            }
        } else if (value !== null && value !== undefined) {
            // Keep non-string values as-is
            sanitized[key] = value;
        }
    }
    return sanitized;
}
const PIIDetection = {
    // Common PII keywords to detect
    KEYWORDS: [
        // Names and titles
        'manager',
        'ceo',
        'director',
        'supervisor',
        'boss',
        'lead',
        'president',
        'vice president',
        'vp',
        'senior',
        'junior',
        'head of',
        'chief',
        // Common names (basic set)
        'john',
        'jane',
        'smith',
        'johnson',
        'williams',
        'brown',
        'jones',
        'garcia',
        'miller',
        'davis',
        'rodriguez',
        'martinez',
        'hernandez',
        // Contact information
        'email',
        'phone',
        'address',
        'linkedin',
        'facebook',
        'twitter',
        'instagram',
        'whatsapp',
        'telegram',
        'skype',
        // Personal identifiers
        'my name',
        'i am',
        'called me',
        'told me personally',
        'my manager',
        'my boss',
        'spoke to me',
        'said to me',
        'personally told',
        // Location specifics that might identify
        'my office',
        'my desk',
        'my team',
        'my department',
        'my floor'
    ],
    /**
   * Detect potential PII in text content
   */ detectPII (text) {
        if (!text || typeof text !== 'string') {
            return [];
        }
        const lowerText = text.toLowerCase();
        const detectedKeywords = [];
        for (const keyword of this.KEYWORDS){
            if (lowerText.includes(keyword)) {
                detectedKeywords.push(keyword);
            }
        }
        return detectedKeywords;
    },
    /**
   * Check if text contains potential PII
   */ hasPII (text) {
        return this.detectPII(text).length > 0;
    },
    /**
   * Get PII warnings for review form fields
   */ getReviewWarnings (reviewData) {
        const warnings = [];
        if (reviewData.pros) {
            const keywords = this.detectPII(reviewData.pros);
            if (keywords.length > 0) {
                warnings.push({
                    field: 'pros',
                    keywords
                });
            }
        }
        if (reviewData.cons) {
            const keywords = this.detectPII(reviewData.cons);
            if (keywords.length > 0) {
                warnings.push({
                    field: 'cons',
                    keywords
                });
            }
        }
        if (reviewData.advice_management) {
            const keywords = this.detectPII(reviewData.advice_management);
            if (keywords.length > 0) {
                warnings.push({
                    field: 'advice_management',
                    keywords
                });
            }
        }
        return warnings;
    }
};
const ValidationUtils = {
    /**
   * Validate UUID format
   */ isValidUUID (uuid) {
        const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
        return uuidRegex.test(uuid);
    },
    /**
   * Validate rating value
   */ isValidRating (rating) {
        return Number.isInteger(rating) && rating >= 1 && rating <= 5;
    },
    /**
   * Validate text length
   */ isValidTextLength (text, maxLength = 5000) {
        return typeof text === 'string' && text.length <= maxLength;
    }
};
const __TURBOPACK__default__export__ = {
    sanitizeReviewText,
    sanitizeSearchQuery,
    sanitizeBasicText,
    sanitizeEmail,
    sanitizeUrl,
    sanitizeObject,
    PIIDetection,
    ValidationUtils,
    SanitizationConfig
};
}}),
"[project]/src/app/api/companies/[id]/reviews/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "DELETE": (()=>DELETE),
    "GET": (()=>GET),
    "PATCH": (()=>PATCH),
    "POST": (()=>POST),
    "PUT": (()=>PUT)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/supabase.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$sanitization$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/sanitization.ts [app-route] (ecmascript)");
;
;
;
async function GET(request, { params }) {
    try {
        const companyId = params.id;
        const { searchParams } = new URL(request.url);
        // Validate UUID format
        if (!__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$sanitization$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ValidationUtils"].isValidUUID(companyId)) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                message: 'Invalid company ID format',
                error: 'Company ID must be a valid UUID'
            }, {
                status: 400
            });
        }
        // Parse and validate query parameters
        const page = Math.max(1, parseInt(searchParams.get('page') || '1'));
        const limit = Math.min(50, Math.max(1, parseInt(searchParams.get('limit') || '10')));
        const sort = searchParams.get('sort') || 'newest';
        // Validate sort parameter
        const validSortOptions = [
            'newest',
            'oldest',
            'highest_rated',
            'lowest_rated'
        ];
        if (!validSortOptions.includes(sort)) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                message: 'Invalid sort parameter',
                error: `Sort must be one of: ${validSortOptions.join(', ')}`
            }, {
                status: 400
            });
        }
        // Calculate offset for pagination
        const offset = (page - 1) * limit;
        // Verify company exists
        const { data: company, error: companyError } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["supabase"].from('companies').select('company_id, name').eq('company_id', companyId).single();
        if (companyError || !company) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                message: 'Company not found',
                error: 'The requested company does not exist'
            }, {
                status: 404
            });
        }
        // Build query for approved reviews
        // CRITICAL: author_id is NEVER selected to maintain anonymity (RULE-601)
        let query = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["supabase"].from('reviews').select(`
        review_id,
        overall_rating,
        pros,
        cons,
        advice_management,
        created_at
      `) // NOTICE: author_id is DELIBERATELY EXCLUDED
        .eq('company_id', companyId).eq('status', 'approved'); // Only approved reviews
        // Apply sorting
        switch(sort){
            case 'newest':
                query = query.order('created_at', {
                    ascending: false
                });
                break;
            case 'oldest':
                query = query.order('created_at', {
                    ascending: true
                });
                break;
            case 'highest_rated':
                query = query.order('overall_rating', {
                    ascending: false
                }).order('created_at', {
                    ascending: false
                });
                break;
            case 'lowest_rated':
                query = query.order('overall_rating', {
                    ascending: true
                }).order('created_at', {
                    ascending: false
                });
                break;
        }
        // Apply pagination
        query = query.range(offset, offset + limit - 1);
        const { data: reviews, error: reviewsError } = await query;
        if (reviewsError) {
            console.error('Error fetching reviews:', reviewsError);
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                message: 'Failed to fetch reviews',
                error: 'Database query failed'
            }, {
                status: 500
            });
        }
        // Get total count of approved reviews for pagination metadata
        const { count: totalCount, error: countError } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["supabase"].from('reviews').select('*', {
            count: 'exact',
            head: true
        }).eq('company_id', companyId).eq('status', 'approved');
        if (countError) {
            console.error('Error counting reviews:', countError);
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                message: 'Failed to count reviews',
                error: 'Database query failed'
            }, {
                status: 500
            });
        }
        // Format reviews for response (additional anonymity protection)
        const formattedReviews = (reviews || []).map((review)=>({
                review_id: review.review_id,
                overall_rating: review.overall_rating,
                pros: review.pros,
                cons: review.cons,
                advice_management: review.advice_management,
                created_at: review.created_at
            }));
        // Return paginated response
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: true,
            data: {
                company: {
                    company_id: company.company_id,
                    name: company.name
                },
                reviews: formattedReviews,
                pagination: {
                    page,
                    limit,
                    total: totalCount || 0,
                    totalPages: Math.ceil((totalCount || 0) / limit),
                    hasNext: page * limit < (totalCount || 0),
                    hasPrev: page > 1
                },
                sort,
                filters: {
                    status: 'approved' // Only approved reviews are shown
                }
            }
        }, {
            headers: {
                // CDN_SHORT caching policy as per HAQ-rules.md
                'Cache-Control': 'public, max-age=300, s-maxage=300',
                'Vary': 'Accept-Encoding',
                'X-Content-Type-Options': 'nosniff',
                'X-Frame-Options': 'DENY'
            }
        });
    } catch (error) {
        console.error('Company reviews API error:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: false,
            message: 'Internal server error',
            error: 'Unexpected error occurred'
        }, {
            status: 500
        });
    }
}
async function POST() {
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
        success: false,
        message: 'Method not allowed'
    }, {
        status: 405,
        headers: {
            'Allow': 'GET'
        }
    });
}
async function PUT() {
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
        success: false,
        message: 'Method not allowed'
    }, {
        status: 405,
        headers: {
            'Allow': 'GET'
        }
    });
}
async function DELETE() {
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
        success: false,
        message: 'Method not allowed'
    }, {
        status: 405,
        headers: {
            'Allow': 'GET'
        }
    });
}
async function PATCH() {
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
        success: false,
        message: 'Method not allowed'
    }, {
        status: 405,
        headers: {
            'Allow': 'GET'
        }
    });
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__b2ace83a._.js.map
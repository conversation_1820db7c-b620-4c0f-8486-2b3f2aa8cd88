(()=>{var e={};e.id=859,e.ids=[859],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},13413:(e,r,t)=>{Promise.resolve().then(t.bind(t,49457))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27910:e=>{"use strict";e.exports=require("stream")},28863:(e,r,t)=>{"use strict";t.d(r,{n:()=>s});class s{static isValidEmail(e){return/^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/.test(e)&&e.length<=255}static isValidUsername(e){return/^[a-zA-Z0-9_]{3,50}$/.test(e)}static validatePassword(e){let r=[];return e.length<8&&r.push("Password must be at least 8 characters long"),e.length>128&&r.push("Password must be less than 128 characters"),/[a-z]/.test(e)||r.push("Password must contain at least one lowercase letter"),/[A-Z]/.test(e)||r.push("Password must contain at least one uppercase letter"),/[0-9]/.test(e)||r.push("Password must contain at least one number"),/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(e)||r.push("Password must contain at least one special character"),{isValid:0===r.length,errors:r}}static sanitizeInput(e){return e.trim().replace(/[<>]/g,"").substring(0,1e3)}}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},43108:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>n.a,__next_app__:()=>m,pages:()=>d,routeModule:()=>u,tree:()=>c});var s=t(65239),a=t(48088),i=t(88170),n=t.n(i),o=t(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);t.d(r,l);let c={children:["",{children:["auth",{children:["login",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,81351)),"D:\\Haq website v1\\haq-frontend-nextjs\\src\\app\\auth\\login\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,82893)),"D:\\Haq website v1\\haq-frontend-nextjs\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["D:\\Haq website v1\\haq-frontend-nextjs\\src\\app\\auth\\login\\page.tsx"],m={require:t,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/auth/login/page",pathname:"/auth/login",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},49457:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>d});var s=t(60687),a=t(43210),i=t(27605),n=t(16189),o=t(85814),l=t.n(o),c=t(28863);function d(){let e=(0,n.useRouter)(),[r,t]=(0,a.useState)(!1),[o,d]=(0,a.useState)(null),[m,u]=(0,a.useState)(null),{register:p,handleSubmit:x,formState:{errors:h},setError:f,clearErrors:g}=(0,i.mN)({mode:"onBlur"}),v=async r=>{t(!0),d(null),u(null),g();try{let t=await fetch("/api/auth/login",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:r.email,password:r.password})}),s=await t.json();s.success?(u("Login successful! Redirecting..."),setTimeout(()=>{e.push("/")},1500)):(d(s.message||"Login failed. Please try again."),(s.message.includes("email")||s.message.includes("password"))&&(f("email",{type:"manual",message:"Invalid email or password"}),f("password",{type:"manual",message:"Invalid email or password"})))}catch(e){console.error("Login error:",e),d("Network error. Please check your connection and try again.")}finally{t(!1)}};return(0,s.jsx)("div",{className:"min-h-screen bg-background-primary flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"max-w-md w-full space-y-8",children:[(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"flex justify-center mb-6",children:(0,s.jsx)("div",{className:"w-12 h-12 bg-gradient-to-br from-accent-primary to-accent-secondary rounded-xl flex items-center justify-center",children:(0,s.jsx)("span",{className:"text-white font-bold text-xl",children:"H"})})}),(0,s.jsx)("h2",{className:"text-3xl font-bold text-text-primary",children:"Sign in to your account"}),(0,s.jsx)("p",{className:"mt-2 text-sm text-text-secondary",children:"Welcome back to HAQ"})]}),m&&(0,s.jsx)("div",{className:"bg-green-50 border border-green-200 rounded-medium p-4",children:(0,s.jsxs)("div",{className:"flex",children:[(0,s.jsx)("div",{className:"flex-shrink-0",children:(0,s.jsx)("svg",{className:"h-5 w-5 text-green-400",viewBox:"0 0 20 20",fill:"currentColor",children:(0,s.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",clipRule:"evenodd"})})}),(0,s.jsx)("div",{className:"ml-3",children:(0,s.jsx)("p",{className:"text-sm font-medium text-green-800",children:m})})]})}),o&&(0,s.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-medium p-4",children:(0,s.jsxs)("div",{className:"flex",children:[(0,s.jsx)("div",{className:"flex-shrink-0",children:(0,s.jsx)("svg",{className:"h-5 w-5 text-red-400",viewBox:"0 0 20 20",fill:"currentColor",children:(0,s.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})})}),(0,s.jsx)("div",{className:"ml-3",children:(0,s.jsx)("p",{className:"text-sm font-medium text-red-800",children:o})})]})}),(0,s.jsxs)("form",{className:"mt-8 space-y-6",onSubmit:x(v),children:[(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-text-primary mb-2",children:"Email address"}),(0,s.jsx)("input",{id:"email",type:"email",autoComplete:"email","aria-invalid":h.email?"true":"false",className:`
                  appearance-none relative block w-full px-3 py-3 border rounded-medium placeholder-text-secondary text-text-primary 
                  focus:outline-none focus:ring-2 focus:ring-accent-primary focus:border-accent-primary focus:z-10 sm:text-sm
                  ${h.email?"border-red-300 bg-red-50":"border-border-primary bg-surface-primary hover:border-border-secondary"}
                `,placeholder:"Enter your email",...p("email",{required:"Email is required",maxLength:{value:255,message:"Email must be less than 255 characters"},pattern:{value:/^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/,message:"Please enter a valid email address"},validate:e=>!!c.n.isValidEmail(e)||"Please enter a valid email address"})}),h.email&&(0,s.jsx)("p",{className:"mt-1 text-sm text-red-600",role:"alert",children:h.email.message})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-text-primary mb-2",children:"Password"}),(0,s.jsx)("input",{id:"password",type:"password",autoComplete:"current-password","aria-invalid":h.password?"true":"false",className:`
                  appearance-none relative block w-full px-3 py-3 border rounded-medium placeholder-text-secondary text-text-primary 
                  focus:outline-none focus:ring-2 focus:ring-accent-primary focus:border-accent-primary focus:z-10 sm:text-sm
                  ${h.password?"border-red-300 bg-red-50":"border-border-primary bg-surface-primary hover:border-border-secondary"}
                `,placeholder:"Enter your password",...p("password",{required:"Password is required",minLength:{value:1,message:"Password is required"}})}),h.password&&(0,s.jsx)("p",{className:"mt-1 text-sm text-red-600",role:"alert",children:h.password.message})]})]}),(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("input",{id:"rememberMe",type:"checkbox",className:"h-4 w-4 text-accent-primary focus:ring-accent-primary border-border-primary rounded",...p("rememberMe")}),(0,s.jsx)("label",{htmlFor:"rememberMe",className:"ml-2 block text-sm text-text-secondary",children:"Remember me"})]}),(0,s.jsx)("div",{className:"text-sm",children:(0,s.jsx)(l(),{href:"/auth/forgot-password",className:"font-medium text-accent-primary hover:text-accent-secondary transition-colors",children:"Forgot your password?"})})]}),(0,s.jsx)("div",{children:(0,s.jsx)("button",{type:"submit",disabled:r,className:`
                group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-medium text-white 
                focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-accent-primary
                ${r?"bg-gray-400 cursor-not-allowed":"bg-accent-primary hover:bg-accent-secondary"}
              `,children:r?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("svg",{className:"animate-spin -ml-1 mr-3 h-5 w-5 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,s.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,s.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"Signing in..."]}):"Sign in"})}),(0,s.jsx)("div",{className:"text-center",children:(0,s.jsxs)("p",{className:"text-sm text-text-secondary",children:["Don't have an account?"," ",(0,s.jsx)(l(),{href:"/auth/signup",className:"font-medium text-accent-primary hover:text-accent-secondary transition-colors",children:"Create one now"})]})})]}),(0,s.jsx)("div",{className:"mt-6 text-center",children:(0,s.jsxs)("p",{className:"text-xs text-text-secondary",children:["By signing in, you agree to our"," ",(0,s.jsx)(l(),{href:"/privacy",className:"text-accent-primary hover:text-accent-secondary",children:"Privacy Policy"})," ","and"," ",(0,s.jsx)(l(),{href:"/terms",className:"text-accent-primary hover:text-accent-secondary",children:"Terms of Service"})]})})]})})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70440:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>a});var s=t(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},74075:e=>{"use strict";e.exports=require("zlib")},76965:(e,r,t)=>{Promise.resolve().then(t.bind(t,81351))},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81351:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\Haq website v1\\\\haq-frontend-nextjs\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Haq website v1\\haq-frontend-nextjs\\src\\app\\auth\\login\\page.tsx","default")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[447,942,658,605,647],()=>t(43108));module.exports=s})();
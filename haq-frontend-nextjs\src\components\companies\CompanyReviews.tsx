'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { 
  Star, 
  ThumbsUp, 
  ThumbsDown, 
  Lightbulb, 
  Calendar,
  Filter,
  ChevronLeft,
  ChevronRight
} from 'lucide-react';

interface Review {
  review_id: string;
  overall_rating: number;
  pros?: string;
  cons?: string;
  advice_management?: string;
  created_at: string;
}

interface ReviewsData {
  company: {
    company_id: string;
    name: string;
  };
  reviews: Review[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
  sort: string;
  filters: {
    status: string;
  };
}

interface CompanyReviewsProps {
  companyId: string;
  companyName: string;
  initialReviewsData: ReviewsData | null;
  currentPage: number;
  currentSort: string;
}

const CompanyReviews: React.FC<CompanyReviewsProps> = ({
  companyId,
  companyName,
  initialReviewsData,
  currentPage,
  currentSort
}) => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [reviewsData, setReviewsData] = useState<ReviewsData | null>(initialReviewsData);
  const [isLoading, setIsLoading] = useState(false);

  const sortOptions = [
    { value: 'newest', label: 'Newest First' },
    { value: 'oldest', label: 'Oldest First' },
    { value: 'highest_rated', label: 'Highest Rated' },
    { value: 'lowest_rated', label: 'Lowest Rated' }
  ];

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, index) => (
      <Star
        key={index}
        className={`h-4 w-4 ${
          index < rating
            ? 'text-yellow-400 fill-current'
            : 'text-gray-300'
        }`}
      />
    ));
  };

  const handleSortChange = (newSort: string) => {
    if (newSort === currentSort) return;
    
    const params = new URLSearchParams(searchParams);
    params.set('sort', newSort);
    params.delete('page'); // Reset to page 1 when sorting
    
    router.push(`/companies/${companyId}?${params.toString()}`);
  };

  const handlePageChange = (newPage: number) => {
    if (newPage === currentPage) return;
    
    const params = new URLSearchParams(searchParams);
    params.set('page', newPage.toString());
    
    router.push(`/companies/${companyId}?${params.toString()}`);
  };

  const renderPagination = () => {
    if (!reviewsData?.pagination || reviewsData.pagination.totalPages <= 1) {
      return null;
    }

    const { pagination } = reviewsData;
    const pages = [];
    const maxVisiblePages = 5;
    
    let startPage = Math.max(1, pagination.page - Math.floor(maxVisiblePages / 2));
    let endPage = Math.min(pagination.totalPages, startPage + maxVisiblePages - 1);
    
    if (endPage - startPage + 1 < maxVisiblePages) {
      startPage = Math.max(1, endPage - maxVisiblePages + 1);
    }

    for (let i = startPage; i <= endPage; i++) {
      pages.push(i);
    }

    return (
      <div className="flex items-center justify-between border-t border-gray-200 bg-white px-4 py-3 sm:px-6">
        <div className="flex flex-1 justify-between sm:hidden">
          <button
            onClick={() => handlePageChange(pagination.page - 1)}
            disabled={!pagination.hasPrev}
            className="relative inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Previous
          </button>
          <button
            onClick={() => handlePageChange(pagination.page + 1)}
            disabled={!pagination.hasNext}
            className="relative ml-3 inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Next
          </button>
        </div>
        
        <div className="hidden sm:flex sm:flex-1 sm:items-center sm:justify-between">
          <div>
            <p className="text-sm text-gray-700">
              Showing{' '}
              <span className="font-medium">
                {(pagination.page - 1) * pagination.limit + 1}
              </span>{' '}
              to{' '}
              <span className="font-medium">
                {Math.min(pagination.page * pagination.limit, pagination.total)}
              </span>{' '}
              of{' '}
              <span className="font-medium">{pagination.total}</span> results
            </p>
          </div>
          
          <div>
            <nav className="isolate inline-flex -space-x-px rounded-md shadow-sm" aria-label="Pagination">
              <button
                onClick={() => handlePageChange(pagination.page - 1)}
                disabled={!pagination.hasPrev}
                className="relative inline-flex items-center rounded-l-md px-2 py-2 text-gray-400 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <ChevronLeft className="h-5 w-5" />
              </button>
              
              {pages.map((page) => (
                <button
                  key={page}
                  onClick={() => handlePageChange(page)}
                  className={`relative inline-flex items-center px-4 py-2 text-sm font-semibold ${
                    page === pagination.page
                      ? 'z-10 bg-blue-600 text-white focus:z-20 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-blue-600'
                      : 'text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0'
                  }`}
                >
                  {page}
                </button>
              ))}
              
              <button
                onClick={() => handlePageChange(pagination.page + 1)}
                disabled={!pagination.hasNext}
                className="relative inline-flex items-center rounded-r-md px-2 py-2 text-gray-400 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <ChevronRight className="h-5 w-5" />
              </button>
            </nav>
          </div>
        </div>
      </div>
    );
  };

  if (!reviewsData) {
    return (
      <div className="bg-white rounded-lg shadow-sm p-8 text-center">
        <p className="text-gray-500">Failed to load reviews. Please try again later.</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Reviews Header */}
      <div className="bg-white rounded-lg shadow-sm p-6">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h2 className="text-2xl font-bold text-gray-900">
              Employee Reviews
            </h2>
            <p className="text-gray-600 mt-1">
              {reviewsData.pagination.total} review{reviewsData.pagination.total !== 1 ? 's' : ''} for {companyName}
            </p>
          </div>
          
          {/* Sort Dropdown */}
          <div className="mt-4 sm:mt-0">
            <div className="flex items-center space-x-2">
              <Filter className="h-4 w-4 text-gray-400" />
              <select
                value={currentSort}
                onChange={(e) => handleSortChange(e.target.value)}
                className="block w-full rounded-md border-gray-300 py-2 pl-3 pr-10 text-base focus:border-blue-500 focus:outline-none focus:ring-blue-500 sm:text-sm"
              >
                {sortOptions.map((option) => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </div>
      </div>

      {/* Reviews List */}
      {reviewsData.reviews.length === 0 ? (
        <div className="bg-white rounded-lg shadow-sm p-8 text-center">
          <p className="text-gray-500">No approved reviews yet for this company.</p>
          <a
            href="/review/submit"
            className="inline-flex items-center mt-4 px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
          >
            Be the first to write a review
          </a>
        </div>
      ) : (
        <div className="space-y-6">
          {reviewsData.reviews.map((review) => (
            <div key={review.review_id} className="bg-white rounded-lg shadow-sm p-6">
              {/* Review Header */}
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-3">
                  <div className="flex items-center space-x-1">
                    {renderStars(review.overall_rating)}
                  </div>
                  <span className="text-sm font-medium text-gray-900">
                    {review.overall_rating}/5
                  </span>
                </div>
                <div className="flex items-center space-x-1 text-sm text-gray-500">
                  <Calendar className="h-4 w-4" />
                  <span>{formatDate(review.created_at)}</span>
                </div>
              </div>

              {/* Review Content */}
              <div className="space-y-4">
                {review.pros && (
                  <div>
                    <div className="flex items-center space-x-2 mb-2">
                      <ThumbsUp className="h-4 w-4 text-green-600" />
                      <h4 className="font-medium text-green-800">Pros</h4>
                    </div>
                    <p className="text-gray-700 pl-6">{review.pros}</p>
                  </div>
                )}

                {review.cons && (
                  <div>
                    <div className="flex items-center space-x-2 mb-2">
                      <ThumbsDown className="h-4 w-4 text-red-600" />
                      <h4 className="font-medium text-red-800">Cons</h4>
                    </div>
                    <p className="text-gray-700 pl-6">{review.cons}</p>
                  </div>
                )}

                {review.advice_management && (
                  <div>
                    <div className="flex items-center space-x-2 mb-2">
                      <Lightbulb className="h-4 w-4 text-yellow-600" />
                      <h4 className="font-medium text-yellow-800">Advice to Management</h4>
                    </div>
                    <p className="text-gray-700 pl-6">{review.advice_management}</p>
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Pagination */}
      {renderPagination()}
    </div>
  );
};

export default CompanyReviews;

(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[177],{283:(e,s,t)=>{"use strict";t.d(s,{As:()=>l,AuthProvider:()=>i});var r=t(5155),a=t(2115);let n={user:null,isLoading:!0,isAuthenticated:!1,error:null};function o(e,s){switch(s.type){case"AUTH_START":return{...e,isLoading:!0,error:null};case"AUTH_SUCCESS":return{...e,user:s.payload,isAuthenticated:!0,isLoading:!1,error:null};case"AUTH_FAILURE":return{...e,user:null,isAuthenticated:!1,isLoading:!1,error:s.payload};case"AUTH_LOGOUT":return{...e,user:null,isAuthenticated:!1,isLoading:!1,error:null};case"CLEAR_ERROR":return{...e,error:null};case"SET_LOADING":return{...e,isLoading:s.payload};default:return e}}let c=(0,a.createContext)(null);function i(e){let{children:s}=e,[t,i]=(0,a.useReducer)(o,n);(0,a.useEffect)(()=>{l()},[]);let l=(0,a.useCallback)(async()=>{try{i({type:"SET_LOADING",payload:!0});let e=await fetch("/api/auth/me",{method:"GET",credentials:"include"});if(e.ok){let s=await e.json();s.success&&s.user?(console.log("✅ Auth check: User authenticated",s.user.username),i({type:"AUTH_SUCCESS",payload:s.user})):(console.log("❌ Auth check: No user found"),i({type:"AUTH_LOGOUT"}))}else console.log("❌ Auth check: Response not ok",e.status),i({type:"AUTH_LOGOUT"})}catch(e){console.error("Session check failed:",e),i({type:"AUTH_LOGOUT"})}finally{i({type:"SET_LOADING",payload:!1})}},[]),d=(0,a.useCallback)(async(e,s)=>{try{i({type:"AUTH_START"});let t=await fetch("/api/auth/login",{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({email:e,password:s})}),r=await t.json();if(r.success&&r.user)return i({type:"AUTH_SUCCESS",payload:r.user}),{success:!0,message:r.message};return i({type:"AUTH_FAILURE",payload:r.message}),{success:!1,message:r.message}}catch(s){let e="Network error. Please try again.";return i({type:"AUTH_FAILURE",payload:e}),{success:!1,message:e}}},[]),m=(0,a.useCallback)(async(e,s,t)=>{try{i({type:"AUTH_START"});let r=await fetch("/api/auth/register",{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({username:e,email:s,password:t})}),a=await r.json();if(a.success&&a.user)return i({type:"AUTH_SUCCESS",payload:a.user}),{success:!0,message:a.message};return i({type:"AUTH_FAILURE",payload:a.message}),{success:!1,message:a.message}}catch(s){let e="Network error. Please try again.";return i({type:"AUTH_FAILURE",payload:e}),{success:!1,message:e}}},[]),u=(0,a.useCallback)(async()=>{try{i({type:"SET_LOADING",payload:!0}),await fetch("/api/auth/logout",{method:"POST",credentials:"include"}),i({type:"AUTH_LOGOUT"})}catch(e){console.error("Logout error:",e),i({type:"AUTH_LOGOUT"})}finally{i({type:"SET_LOADING",payload:!1})}},[]),p=(0,a.useCallback)(()=>{i({type:"CLEAR_ERROR"})},[]),x=(0,a.useCallback)(async()=>{await l()},[l]),h=(0,a.useMemo)(()=>({...t,login:d,register:m,logout:u,clearError:p,refreshUser:x}),[t,d,m,u,p,x]);return(0,r.jsx)(c.Provider,{value:h,children:s})}function l(){let e=(0,a.useContext)(c);if(!e)throw Error("useAuth must be used within an AuthProvider");return e}},319:(e,s,t)=>{"use strict";t.d(s,{SWRProvider:()=>p});var r=t(5155),a=t(6072),n=t(4982),o=t(9535);let c="https://wqbuilazpyxpwyuwuqpi.supabase.co",i="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.GeRI54Rskwdbfm9_lRxy1-7YQ8vA74JWblNF2GRpzrI",l=null,d=null,m=()=>(l||(l=(0,o.createBrowserClient)(c,i)),l);d=(0,n.UU)(c,i);let u={fetcher:async e=>{if(m(),e.startsWith("/api/companies"))return[{id:1,company_id:1,name:"TechCorp Solutions",slug:"techcorp-solutions",logo_url:"/placeholder-company.svg",description:"Leading technology solutions provider",location:"Karachi, Pakistan",industry:"Technology",employee_count:"500-1000",haq_score:85,total_reviews:24,redFlags:["High turnover rate","Unpaid overtime"],greenFlags:["Good benefits","Career growth opportunities"]},{id:2,company_id:2,name:"InnovatePak",slug:"innovatepak",logo_url:"/placeholder-company.svg",description:"Innovation-driven software company",location:"Lahore, Pakistan",industry:"Software",employee_count:"100-500",haq_score:78,total_reviews:18,redFlags:["Limited remote work"],greenFlags:["Modern office","Learning opportunities","Flexible hours"]},{id:3,company_id:3,name:"DataFlow Systems",slug:"dataflow-systems",logo_url:"/placeholder-company.svg",description:"Data analytics and business intelligence",location:"Islamabad, Pakistan",industry:"Analytics",employee_count:"50-100",haq_score:72,total_reviews:12,redFlags:["Micromanagement"],greenFlags:["Good work-life balance","Competitive salary"]}];if(e.startsWith("/api/company/")){let s=e.split("/").pop(),t=[{id:1,company_id:1,name:"TechCorp Solutions",slug:"techcorp-solutions",logo_url:"/placeholder-company.svg",description:"Leading technology solutions provider",location:"Karachi, Pakistan",industry:"Technology",employee_count:"500-1000",haq_score:85,total_reviews:24,redFlags:["High turnover rate","Unpaid overtime"],greenFlags:["Good benefits","Career growth opportunities"]}];return t.find(e=>e.slug===s)||t[0]}let s=await fetch(e);if(!s.ok)throw Error("Failed to fetch");return s.json()},revalidateOnFocus:!1,revalidateOnReconnect:!0,refreshInterval:0,errorRetryCount:3,errorRetryInterval:5e3,onError:e=>{console.error("SWR Error:",e)},onSuccess:(e,s)=>{}};function p(e){let{children:s}=e;return(0,r.jsx)(a.BE,{value:u,children:s})}},347:()=>{},5430:(e,s,t)=>{"use strict";t.d(s,{Header:()=>b});var r=t(5155),a=t(2115),n=t(6874),o=t.n(n),c=t(5695),i=t(5525),l=t(5868),d=t(1366),m=t(7580),u=t(1007),p=t(381),x=t(4835),h=t(306),y=t(4416),g=t(4783),f=t(283);let b=()=>{let[e,s]=(0,a.useState)(!1),[t,n]=(0,a.useState)(!1),b=(0,c.usePathname)(),{user:v,isAuthenticated:j,isLoading:N,logout:w}=(0,f.As)(),A=[{name:"Companies",href:"/companies",icon:i.A},{name:"Salaries",href:"/salaries",icon:l.A},{name:"Community",href:"/community",icon:d.A}],_=async()=>{try{await w(),n(!1)}catch(e){console.error("Logout failed:",e)}};return(0,r.jsxs)("header",{className:"bg-background-primary border-b border-border-primary sticky top-0 z-50",children:[(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,r.jsxs)("div",{className:"flex justify-between items-center h-16",children:[(0,r.jsxs)(o(),{href:"/",className:"flex items-center space-x-2 group",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-gradient-to-br from-accent-primary to-accent-secondary rounded-lg flex items-center justify-center group-hover:scale-105 transition-transform duration-200",children:(0,r.jsx)(i.A,{className:"w-5 h-5 text-text-on-accent"})}),(0,r.jsxs)("div",{className:"flex flex-col",children:[(0,r.jsx)("span",{className:"text-xl font-bold text-accent-primary",children:"Haq"}),(0,r.jsx)("span",{className:"text-xs text-text-secondary -mt-1",children:"حق"})]})]}),(0,r.jsx)("nav",{className:"hidden md:flex items-center space-x-8",children:A.map(e=>{let s=e.icon,t=b===e.href;return(0,r.jsxs)(o(),{href:e.href,className:"flex items-center space-x-1 px-3 py-2 rounded-lg text-navigation-link font-medium transition-all duration-200 uppercase tracking-wide ".concat(t?"text-accent-primary bg-surface-secondary":"text-text-secondary hover:text-accent-primary hover:bg-surface-primary"),children:[(0,r.jsx)(s,{className:"w-4 h-4"}),(0,r.jsx)("span",{children:e.name})]},e.name)})}),(0,r.jsxs)("div",{className:"hidden md:flex items-center space-x-4",children:[j&&(0,r.jsxs)(o(),{href:"/review/submit",className:"bg-accent-primary hover:bg-accent-secondary text-text-on-accent px-4 py-2 rounded-lg text-sm font-semibold transition-all duration-200 flex items-center space-x-2 hover:shadow-glow transform hover:-translate-y-0.5",children:[(0,r.jsx)(m.A,{className:"w-4 h-4"}),(0,r.jsx)("span",{children:"Write Review"})]}),N?(0,r.jsx)("div",{className:"w-8 h-8 animate-spin rounded-full border-2 border-accent-primary border-t-transparent"}):j&&v?(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsxs)("button",{onClick:()=>n(!t),className:"flex items-center space-x-2 px-3 py-2 rounded-lg text-text-secondary hover:text-accent-primary hover:bg-surface-primary transition-all duration-200",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-gradient-to-br from-accent-primary to-accent-secondary rounded-full flex items-center justify-center",children:(0,r.jsx)(u.A,{className:"w-4 h-4 text-text-on-accent"})}),(0,r.jsx)("span",{className:"text-sm font-medium",children:v.username}),(0,r.jsx)("svg",{className:"w-4 h-4 transition-transform duration-200 ".concat(t?"rotate-180":""),fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 9l-7 7-7-7"})})]}),t&&(0,r.jsxs)("div",{className:"absolute right-0 mt-2 w-48 bg-surface-primary border border-border-primary rounded-lg shadow-lg py-1 z-50",children:[(0,r.jsxs)("div",{className:"px-4 py-2 border-b border-border-primary",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-text-primary",children:v.username}),(0,r.jsx)("p",{className:"text-xs text-text-secondary",children:v.email}),(0,r.jsx)("span",{className:"inline-block px-2 py-1 text-xs rounded-full mt-1 ".concat("admin"===v.role?"bg-accent-primary text-text-on-accent":"bg-surface-secondary text-text-secondary"),children:v.role})]}),(0,r.jsxs)(o(),{href:"/profile",onClick:()=>n(!1),className:"flex items-center space-x-2 px-4 py-2 text-sm text-text-secondary hover:text-accent-primary hover:bg-surface-secondary transition-colors duration-200",children:[(0,r.jsx)(p.A,{className:"w-4 h-4"}),(0,r.jsx)("span",{children:"Profile Settings"})]}),(0,r.jsxs)("button",{onClick:_,className:"w-full flex items-center space-x-2 px-4 py-2 text-sm text-text-secondary hover:text-red-600 hover:bg-surface-secondary transition-colors duration-200",children:[(0,r.jsx)(x.A,{className:"w-4 h-4"}),(0,r.jsx)("span",{children:"Sign Out"})]})]})]}):(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsxs)(o(),{href:"/auth/login",className:"flex items-center space-x-1 px-4 py-2 text-sm font-medium text-text-secondary hover:text-accent-primary transition-colors duration-200",children:[(0,r.jsx)(h.A,{className:"w-4 h-4"}),(0,r.jsx)("span",{children:"Sign In"})]}),(0,r.jsxs)(o(),{href:"/auth/signup",className:"bg-accent-primary hover:bg-accent-secondary text-text-on-accent px-4 py-2 rounded-lg text-sm font-semibold transition-all duration-200 flex items-center space-x-1 hover:shadow-glow transform hover:-translate-y-0.5",children:[(0,r.jsx)(u.A,{className:"w-4 h-4"}),(0,r.jsx)("span",{children:"Sign Up"})]})]})]}),(0,r.jsx)("button",{onClick:()=>s(!e),className:"md:hidden p-2 rounded-lg text-text-secondary hover:text-accent-primary hover:bg-surface-primary transition-colors duration-200",children:e?(0,r.jsx)(y.A,{className:"w-6 h-6"}):(0,r.jsx)(g.A,{className:"w-6 h-6"})})]})}),e&&(0,r.jsx)("div",{className:"md:hidden bg-surface-primary border-t border-border-primary",children:(0,r.jsxs)("div",{className:"px-4 py-3 space-y-3",children:[A.map(e=>{let t=e.icon,a=b===e.href;return(0,r.jsxs)(o(),{href:e.href,onClick:()=>s(!1),className:"flex items-center space-x-3 px-3 py-2 rounded-lg text-sm font-medium transition-colors duration-200 ".concat(a?"text-accent-primary bg-surface-secondary":"text-text-secondary hover:text-accent-primary hover:bg-surface-secondary"),children:[(0,r.jsx)(t,{className:"w-5 h-5"}),(0,r.jsx)("span",{children:e.name})]},e.name)}),j?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)(o(),{href:"/review/submit",onClick:()=>s(!1),className:"flex items-center space-x-3 bg-accent-primary hover:bg-accent-secondary text-text-on-accent px-3 py-2 rounded-lg text-sm font-medium transition-colors duration-200",children:[(0,r.jsx)(m.A,{className:"w-5 h-5"}),(0,r.jsx)("span",{children:"Write Review"})]}),(0,r.jsxs)("div",{className:"border-t border-border-primary pt-3 mt-3",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3 px-3 py-2 mb-2",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-gradient-to-br from-accent-primary to-accent-secondary rounded-full flex items-center justify-center",children:(0,r.jsx)(u.A,{className:"w-4 h-4 text-text-on-accent"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-text-primary",children:null==v?void 0:v.username}),(0,r.jsx)("p",{className:"text-xs text-text-secondary",children:null==v?void 0:v.email})]})]}),(0,r.jsxs)(o(),{href:"/profile",onClick:()=>s(!1),className:"flex items-center space-x-3 px-3 py-2 rounded-lg text-sm font-medium text-text-secondary hover:text-accent-primary hover:bg-surface-secondary transition-colors duration-200",children:[(0,r.jsx)(p.A,{className:"w-5 h-5"}),(0,r.jsx)("span",{children:"Profile Settings"})]}),(0,r.jsxs)("button",{onClick:()=>{_(),s(!1)},className:"w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-sm font-medium text-text-secondary hover:text-red-600 hover:bg-surface-secondary transition-colors duration-200",children:[(0,r.jsx)(x.A,{className:"w-5 h-5"}),(0,r.jsx)("span",{children:"Sign Out"})]})]})]}):(0,r.jsxs)("div",{className:"border-t border-border-primary pt-3 mt-3 space-y-2",children:[(0,r.jsxs)(o(),{href:"/auth/login",onClick:()=>s(!1),className:"flex items-center space-x-3 px-3 py-2 rounded-lg text-sm font-medium text-text-secondary hover:text-accent-primary hover:bg-surface-secondary transition-colors duration-200",children:[(0,r.jsx)(h.A,{className:"w-5 h-5"}),(0,r.jsx)("span",{children:"Sign In"})]}),(0,r.jsxs)(o(),{href:"/auth/signup",onClick:()=>s(!1),className:"flex items-center space-x-3 bg-accent-primary hover:bg-accent-secondary text-text-on-accent px-3 py-2 rounded-lg text-sm font-medium transition-colors duration-200",children:[(0,r.jsx)(u.A,{className:"w-5 h-5"}),(0,r.jsx)("span",{children:"Sign Up"})]})]})]})})]})}},7999:(e,s,t)=>{Promise.resolve().then(t.t.bind(t,6874,23)),Promise.resolve().then(t.t.bind(t,8524,23)),Promise.resolve().then(t.t.bind(t,347,23)),Promise.resolve().then(t.bind(t,5430)),Promise.resolve().then(t.bind(t,283)),Promise.resolve().then(t.bind(t,319))}},e=>{var s=s=>e(e.s=s);e.O(0,[80,244,683,354,441,684,358],()=>s(7999)),_N_E=e.O()}]);
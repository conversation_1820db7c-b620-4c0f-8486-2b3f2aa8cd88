'use client';

import React, { useState, useEffect } from 'react';
import { useForm, Controller } from 'react-hook-form';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { useAuth } from '@/contexts/AuthContext';
import { 
  Search, 
  Star, 
  AlertTriangle, 
  CheckCircle, 
  ArrowRight, 
  ArrowLeft,
  Building2,
  MessageSquare,
  ThumbsUp,
  ThumbsDown,
  Lightbulb
} from 'lucide-react';

// Form validation schema
const reviewFormSchema = z.object({
  company_id: z.string().uuid('Please select a company'),
  overall_rating: z.number().min(1, 'Please provide a rating').max(5, 'Rating must be between 1 and 5'),
  pros: z.string().optional(),
  cons: z.string().optional(),
  advice_management: z.string().optional(),
});

type ReviewFormData = z.infer<typeof reviewFormSchema>;

interface Company {
  company_id: string;
  name: string;
  industry?: string;
  location?: string;
  logo_url?: string;
  haq_score: number;
  total_reviews: number;
}

interface PIIWarning {
  field: string;
  keywords: string[];
}

const ReviewForm: React.FC = () => {
  const { user, isAuthenticated, isLoading } = useAuth();
  const [currentStep, setCurrentStep] = useState(1);
  const [companies, setCompanies] = useState<Company[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [isLoadingCompanies, setIsLoadingCompanies] = useState(false);
  const [selectedCompany, setSelectedCompany] = useState<Company | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitSuccess, setSubmitSuccess] = useState(false);
  const [submitError, setSubmitError] = useState('');
  const [piiWarnings, setPiiWarnings] = useState<PIIWarning[]>([]);

  const {
    control,
    handleSubmit,
    watch,
    setValue,
    formState: { errors, isValid },
    reset
  } = useForm<ReviewFormData>({
    resolver: zodResolver(reviewFormSchema),
    mode: 'onChange',
    defaultValues: {
      company_id: '',
      overall_rating: 0,
      pros: '',
      cons: '',
      advice_management: ''
    }
  });

  const watchedValues = watch();

  // Fetch companies based on search query
  const fetchCompanies = async (query: string = '') => {
    setIsLoadingCompanies(true);
    try {
      const params = new URLSearchParams();
      if (query.trim()) {
        params.append('q', query.trim());
      }
      params.append('limit', '20');

      const response = await fetch(`/api/companies?${params}`);
      const result = await response.json();

      if (result.success) {
        setCompanies(result.data.companies);
      } else {
        console.error('Failed to fetch companies:', result.message);
        setCompanies([]);
      }
    } catch (error) {
      console.error('Error fetching companies:', error);
      setCompanies([]);
    } finally {
      setIsLoadingCompanies(false);
    }
  };

  // Load companies on component mount
  useEffect(() => {
    fetchCompanies();
  }, []);

  // Handle search with debouncing
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      fetchCompanies(searchQuery);
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [searchQuery]);

  // Check for PII in text content
  const checkForPII = (text: string): string[] => {
    const piiKeywords = [
      'manager', 'ceo', 'director', 'supervisor', 'boss', 'lead',
      'john', 'jane', 'smith', 'johnson', 'williams', 'brown', 'jones',
      'email', 'phone', 'address', 'linkedin', 'facebook', 'twitter',
      'my name', 'i am', 'called me', 'told me personally'
    ];
    
    const lowerText = text.toLowerCase();
    return piiKeywords.filter(keyword => lowerText.includes(keyword));
  };

  // Monitor text fields for PII
  useEffect(() => {
    const warnings: PIIWarning[] = [];
    
    if (watchedValues.pros) {
      const prosKeywords = checkForPII(watchedValues.pros);
      if (prosKeywords.length > 0) {
        warnings.push({ field: 'pros', keywords: prosKeywords });
      }
    }
    
    if (watchedValues.cons) {
      const consKeywords = checkForPII(watchedValues.cons);
      if (consKeywords.length > 0) {
        warnings.push({ field: 'cons', keywords: consKeywords });
      }
    }
    
    if (watchedValues.advice_management) {
      const adviceKeywords = checkForPII(watchedValues.advice_management);
      if (adviceKeywords.length > 0) {
        warnings.push({ field: 'advice_management', keywords: adviceKeywords });
      }
    }
    
    setPiiWarnings(warnings);
  }, [watchedValues.pros, watchedValues.cons, watchedValues.advice_management]);

  const handleCompanySelect = (company: Company) => {
    setSelectedCompany(company);
    setValue('company_id', company.company_id);
    setCurrentStep(2);
  };

  const handleRatingSelect = (rating: number) => {
    setValue('overall_rating', rating);
  };

  const onSubmit = async (data: ReviewFormData) => {
    if (!isAuthenticated) {
      setSubmitError('You must be logged in to submit a review');
      return;
    }

    setIsSubmitting(true);
    setSubmitError('');

    try {
      const response = await fetch('/api/reviews', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify(data)
      });

      const result = await response.json();

      if (result.success) {
        setSubmitSuccess(true);
        setCurrentStep(5); // Success step
        reset();
        setSelectedCompany(null);
        setPiiWarnings([]);
      } else {
        setSubmitError(result.message || 'Failed to submit review');
      }
    } catch (error) {
      console.error('Review submission error:', error);
      setSubmitError('An unexpected error occurred. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const nextStep = () => {
    if (currentStep < 4) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const canProceedToStep = (step: number): boolean => {
    switch (step) {
      case 2:
        return !!watchedValues.company_id;
      case 3:
        return !!watchedValues.company_id && watchedValues.overall_rating > 0;
      case 4:
        return !!watchedValues.company_id && watchedValues.overall_rating > 0;
      default:
        return true;
    }
  };

  // Show loading state while checking authentication
  if (isLoading) {
    return (
      <div className="max-w-2xl mx-auto p-6 bg-white rounded-lg shadow-lg">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Loading...</h2>
          <p className="text-gray-600">
            Checking your authentication status...
          </p>
        </div>
      </div>
    );
  }

  // Show login required if not authenticated
  if (!isAuthenticated) {
    return (
      <div className="max-w-2xl mx-auto p-6 bg-white rounded-lg shadow-lg">
        <div className="text-center">
          <MessageSquare className="mx-auto h-12 w-12 text-gray-400 mb-4" />
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Login Required</h2>
          <p className="text-gray-600 mb-6">
            You must be logged in to submit a company review.
          </p>
          <a
            href="/auth/login"
            className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
          >
            Login to Continue
          </a>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto p-6">
      {/* Progress Indicator */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          {[1, 2, 3, 4].map((step) => (
            <div key={step} className="flex items-center">
              <div
                className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                  step <= currentStep
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-200 text-gray-600'
                }`}
              >
                {step}
              </div>
              {step < 4 && (
                <div
                  className={`w-16 h-1 mx-2 ${
                    step < currentStep ? 'bg-blue-600' : 'bg-gray-200'
                  }`}
                />
              )}
            </div>
          ))}
        </div>
        <div className="flex justify-between mt-2 text-sm text-gray-600">
          <span>Select Company</span>
          <span>Rate Experience</span>
          <span>Write Review</span>
          <span>Submit</span>
        </div>
      </div>

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        {/* Step 1: Company Selection */}
        {currentStep === 1 && (
          <div className="bg-white rounded-lg shadow-lg p-6">
            <div className="flex items-center mb-6">
              <Building2 className="h-6 w-6 text-blue-600 mr-2" />
              <h2 className="text-2xl font-bold text-gray-900">Select Company</h2>
            </div>

            <div className="mb-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search for a company..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            </div>

            {isLoadingCompanies ? (
              <div className="text-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                <p className="mt-2 text-gray-600">Loading companies...</p>
              </div>
            ) : (
              <div className="space-y-2 max-h-96 overflow-y-auto">
                {companies.length === 0 ? (
                  <p className="text-center text-gray-500 py-8">
                    {searchQuery ? 'No companies found matching your search.' : 'No companies available.'}
                  </p>
                ) : (
                  companies.map((company) => (
                    <div
                      key={company.company_id}
                      onClick={() => handleCompanySelect(company)}
                      className="p-4 border border-gray-200 rounded-lg hover:border-blue-500 hover:bg-blue-50 cursor-pointer transition-colors"
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center">
                          {company.logo_url ? (
                            <img
                              src={company.logo_url}
                              alt={company.name}
                              className="w-12 h-12 rounded-lg object-cover mr-4"
                            />
                          ) : (
                            <div className="w-12 h-12 rounded-lg bg-gray-200 flex items-center justify-center mr-4">
                              <Building2 className="h-6 w-6 text-gray-400" />
                            </div>
                          )}
                          <div>
                            <h3 className="font-semibold text-gray-900">{company.name}</h3>
                            <p className="text-sm text-gray-600">
                              {company.industry && company.location
                                ? `${company.industry} • ${company.location}`
                                : company.industry || company.location || 'Company'}
                            </p>
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="text-sm font-medium text-gray-900">
                            HAQ Score: {company.haq_score}
                          </div>
                          <div className="text-sm text-gray-600">
                            {company.total_reviews} reviews
                          </div>
                        </div>
                      </div>
                    </div>
                  ))
                )}
              </div>
            )}

            {errors.company_id && (
              <p className="mt-2 text-sm text-red-600">{errors.company_id.message}</p>
            )}
          </div>
        )}

        {/* Step 2: Rating */}
        {currentStep === 2 && selectedCompany && (
          <div className="bg-white rounded-lg shadow-lg p-6">
            <div className="flex items-center mb-6">
              <Star className="h-6 w-6 text-blue-600 mr-2" />
              <h2 className="text-2xl font-bold text-gray-900">Rate Your Experience</h2>
            </div>

            <div className="mb-6 p-4 bg-gray-50 rounded-lg">
              <div className="flex items-center">
                {selectedCompany.logo_url ? (
                  <img
                    src={selectedCompany.logo_url}
                    alt={selectedCompany.name}
                    className="w-16 h-16 rounded-lg object-cover mr-4"
                  />
                ) : (
                  <div className="w-16 h-16 rounded-lg bg-gray-200 flex items-center justify-center mr-4">
                    <Building2 className="h-8 w-8 text-gray-400" />
                  </div>
                )}
                <div>
                  <h3 className="text-xl font-semibold text-gray-900">{selectedCompany.name}</h3>
                  <p className="text-gray-600">
                    {selectedCompany.industry && selectedCompany.location
                      ? `${selectedCompany.industry} • ${selectedCompany.location}`
                      : selectedCompany.industry || selectedCompany.location || 'Company'}
                  </p>
                </div>
              </div>
            </div>

            <div className="text-center">
              <p className="text-lg text-gray-700 mb-6">
                How would you rate your overall experience working at {selectedCompany.name}?
              </p>
              
              <Controller
                name="overall_rating"
                control={control}
                render={({ field }) => (
                  <div className="flex justify-center space-x-2 mb-6">
                    {[1, 2, 3, 4, 5].map((rating) => (
                      <button
                        key={rating}
                        type="button"
                        onClick={() => {
                          field.onChange(rating);
                          handleRatingSelect(rating);
                        }}
                        className={`p-2 rounded-lg transition-colors ${
                          field.value >= rating
                            ? 'text-yellow-500 hover:text-yellow-600'
                            : 'text-gray-300 hover:text-yellow-400'
                        }`}
                      >
                        <Star className="h-12 w-12 fill-current" />
                      </button>
                    ))}
                  </div>
                )}
              />

              {watchedValues.overall_rating > 0 && (
                <div className="text-center">
                  <p className="text-lg font-medium text-gray-900">
                    {watchedValues.overall_rating === 1 && "Very Poor"}
                    {watchedValues.overall_rating === 2 && "Poor"}
                    {watchedValues.overall_rating === 3 && "Average"}
                    {watchedValues.overall_rating === 4 && "Good"}
                    {watchedValues.overall_rating === 5 && "Excellent"}
                  </p>
                  <p className="text-gray-600">
                    {watchedValues.overall_rating} out of 5 stars
                  </p>
                </div>
              )}
            </div>

            {errors.overall_rating && (
              <p className="mt-4 text-sm text-red-600 text-center">{errors.overall_rating.message}</p>
            )}

            <div className="flex justify-between mt-8">
              <button
                type="button"
                onClick={prevStep}
                className="flex items-center px-4 py-2 text-gray-600 hover:text-gray-800"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back
              </button>
              <button
                type="button"
                onClick={nextStep}
                disabled={!canProceedToStep(3)}
                className="flex items-center px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Continue
                <ArrowRight className="h-4 w-4 ml-2" />
              </button>
            </div>
          </div>
        )}

        {/* Step 3: Write Review */}
        {currentStep === 3 && selectedCompany && (
          <div className="bg-white rounded-lg shadow-lg p-6">
            <div className="flex items-center mb-6">
              <MessageSquare className="h-6 w-6 text-blue-600 mr-2" />
              <h2 className="text-2xl font-bold text-gray-900">Write Your Review</h2>
            </div>

            <div className="mb-6 p-4 bg-gray-50 rounded-lg">
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <h3 className="text-lg font-semibold text-gray-900">{selectedCompany.name}</h3>
                  <div className="ml-4 flex items-center">
                    {[1, 2, 3, 4, 5].map((star) => (
                      <Star
                        key={star}
                        className={`h-5 w-5 ${
                          star <= watchedValues.overall_rating
                            ? 'text-yellow-500 fill-current'
                            : 'text-gray-300'
                        }`}
                      />
                    ))}
                    <span className="ml-2 text-gray-600">({watchedValues.overall_rating}/5)</span>
                  </div>
                </div>
              </div>
            </div>

            {/* PII Warnings */}
            {piiWarnings.length > 0 && (
              <div className="mb-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                <div className="flex items-start">
                  <AlertTriangle className="h-5 w-5 text-yellow-600 mt-0.5 mr-3 flex-shrink-0" />
                  <div>
                    <h4 className="text-sm font-medium text-yellow-800">
                      Potential Personal Information Detected
                    </h4>
                    <p className="text-sm text-yellow-700 mt-1">
                      We detected words that might identify specific people or contain personal information.
                      Please review your text to ensure anonymity.
                    </p>
                    <div className="mt-2">
                      {piiWarnings.map((warning, index) => (
                        <div key={index} className="text-sm text-yellow-700">
                          <strong>{warning.field}:</strong> {warning.keywords.join(', ')}
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            )}

            <div className="space-y-6">
              {/* Pros */}
              <div>
                <label className="flex items-center text-sm font-medium text-gray-700 mb-2">
                  <ThumbsUp className="h-4 w-4 text-green-600 mr-2" />
                  What did you like about working here? (Optional)
                </label>
                <Controller
                  name="pros"
                  control={control}
                  render={({ field }) => (
                    <textarea
                      {...field}
                      rows={4}
                      placeholder="Share the positive aspects of your experience..."
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
                    />
                  )}
                />
              </div>

              {/* Cons */}
              <div>
                <label className="flex items-center text-sm font-medium text-gray-700 mb-2">
                  <ThumbsDown className="h-4 w-4 text-red-600 mr-2" />
                  What could be improved? (Optional)
                </label>
                <Controller
                  name="cons"
                  control={control}
                  render={({ field }) => (
                    <textarea
                      {...field}
                      rows={4}
                      placeholder="Share areas where the company could improve..."
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
                    />
                  )}
                />
              </div>

              {/* Advice to Management */}
              <div>
                <label className="flex items-center text-sm font-medium text-gray-700 mb-2">
                  <Lightbulb className="h-4 w-4 text-yellow-600 mr-2" />
                  Advice to Management (Optional)
                </label>
                <Controller
                  name="advice_management"
                  control={control}
                  render={({ field }) => (
                    <textarea
                      {...field}
                      rows={4}
                      placeholder="What advice would you give to management?"
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
                    />
                  )}
                />
              </div>
            </div>

            <div className="flex justify-between mt-8">
              <button
                type="button"
                onClick={prevStep}
                className="flex items-center px-4 py-2 text-gray-600 hover:text-gray-800"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back
              </button>
              <button
                type="button"
                onClick={nextStep}
                className="flex items-center px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
              >
                Review & Submit
                <ArrowRight className="h-4 w-4 ml-2" />
              </button>
            </div>
          </div>
        )}

        {/* Step 4: Review & Submit */}
        {currentStep === 4 && selectedCompany && (
          <div className="bg-white rounded-lg shadow-lg p-6">
            <div className="flex items-center mb-6">
              <CheckCircle className="h-6 w-6 text-blue-600 mr-2" />
              <h2 className="text-2xl font-bold text-gray-900">Review & Submit</h2>
            </div>

            <div className="space-y-6">
              {/* Company & Rating Summary */}
              <div className="p-4 bg-gray-50 rounded-lg">
                <h3 className="font-semibold text-gray-900 mb-2">Company & Rating</h3>
                <div className="flex items-center justify-between">
                  <span className="text-gray-700">{selectedCompany.name}</span>
                  <div className="flex items-center">
                    {[1, 2, 3, 4, 5].map((star) => (
                      <Star
                        key={star}
                        className={`h-4 w-4 ${
                          star <= watchedValues.overall_rating
                            ? 'text-yellow-500 fill-current'
                            : 'text-gray-300'
                        }`}
                      />
                    ))}
                    <span className="ml-2 text-gray-600">({watchedValues.overall_rating}/5)</span>
                  </div>
                </div>
              </div>

              {/* Review Content */}
              {watchedValues.pros && (
                <div className="p-4 bg-green-50 rounded-lg">
                  <h4 className="flex items-center font-medium text-green-800 mb-2">
                    <ThumbsUp className="h-4 w-4 mr-2" />
                    Pros
                  </h4>
                  <p className="text-green-700">{watchedValues.pros}</p>
                </div>
              )}

              {watchedValues.cons && (
                <div className="p-4 bg-red-50 rounded-lg">
                  <h4 className="flex items-center font-medium text-red-800 mb-2">
                    <ThumbsDown className="h-4 w-4 mr-2" />
                    Cons
                  </h4>
                  <p className="text-red-700">{watchedValues.cons}</p>
                </div>
              )}

              {watchedValues.advice_management && (
                <div className="p-4 bg-yellow-50 rounded-lg">
                  <h4 className="flex items-center font-medium text-yellow-800 mb-2">
                    <Lightbulb className="h-4 w-4 mr-2" />
                    Advice to Management
                  </h4>
                  <p className="text-yellow-700">{watchedValues.advice_management}</p>
                </div>
              )}

              {/* Final PII Warning */}
              {piiWarnings.length > 0 && (
                <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                  <div className="flex items-start">
                    <AlertTriangle className="h-5 w-5 text-yellow-600 mt-0.5 mr-3 flex-shrink-0" />
                    <div>
                      <h4 className="text-sm font-medium text-yellow-800">
                        Privacy Notice
                      </h4>
                      <p className="text-sm text-yellow-700 mt-1">
                        Your review contains words that might identify specific people.
                        Please ensure your review maintains anonymity before submitting.
                      </p>
                    </div>
                  </div>
                </div>
              )}

              {/* Terms */}
              <div className="p-4 bg-blue-50 rounded-lg">
                <p className="text-sm text-blue-700">
                  By submitting this review, you confirm that it's based on your genuine experience
                  and doesn't contain false information. Your review will be moderated before publication.
                </p>
              </div>
            </div>

            {submitError && (
              <div className="mt-4 p-4 bg-red-50 border border-red-200 rounded-lg">
                <p className="text-red-700">{submitError}</p>
              </div>
            )}

            <div className="flex justify-between mt-8">
              <button
                type="button"
                onClick={prevStep}
                disabled={isSubmitting}
                className="flex items-center px-4 py-2 text-gray-600 hover:text-gray-800 disabled:opacity-50"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back
              </button>
              <button
                type="submit"
                disabled={isSubmitting}
                className="flex items-center px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isSubmitting ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Submitting...
                  </>
                ) : (
                  <>
                    Submit Review
                    <CheckCircle className="h-4 w-4 ml-2" />
                  </>
                )}
              </button>
            </div>
          </div>
        )}

        {/* Step 5: Success */}
        {currentStep === 5 && submitSuccess && (
          <div className="bg-white rounded-lg shadow-lg p-6 text-center">
            <CheckCircle className="mx-auto h-16 w-16 text-green-500 mb-4" />
            <h2 className="text-2xl font-bold text-gray-900 mb-2">Review Submitted Successfully!</h2>
            <p className="text-gray-600 mb-6">
              Thank you for sharing your experience. Your review is now pending moderation
              and will be published once approved.
            </p>
            <div className="space-y-3">
              <button
                type="button"
                onClick={() => {
                  setCurrentStep(1);
                  setSubmitSuccess(false);
                  setSelectedCompany(null);
                  reset();
                }}
                className="w-full px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
              >
                Submit Another Review
              </button>
              <a
                href="/"
                className="block w-full px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50"
              >
                Return to Home
              </a>
            </div>
          </div>
        )}
      </form>
    </div>
  );
};

export default ReviewForm;

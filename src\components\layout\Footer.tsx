import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { Shield, Heart, Globe } from 'lucide-react';

export const Footer: React.FC = () => {
  return (
    <footer className="bg-background-secondary text-text-primary border-t border-border-primary">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Brand */}
          <div className="col-span-1 md:col-span-2">
            <div className="flex items-center space-x-2 mb-4">
              <div className="w-10 h-10 bg-gradient-to-br from-accent-primary to-accent-secondary rounded-lg flex items-center justify-center">
                <Shield className="w-6 h-6 text-text-on-accent" />
              </div>
              <div>
                <h3 className="text-xl font-bold text-accent-primary">Haq</h3>
                <p className="text-sm text-text-secondary">حق - Your Rights Matter</p>
              </div>
            </div>
            <p className="text-text-secondary mb-4 max-w-md">
              Empowering Pakistani employees through transparency, anonymous reviews, and workplace accountability. 
              Together, we build a fairer professional landscape.
            </p>
            <div className="flex items-center space-x-2 text-sm text-text-secondary">
              <Globe className="w-4 h-4" />
              <span>Available in English & Urdu</span>
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h4 className="font-semibold mb-4 text-accent-primary uppercase tracking-wide">Platform</h4>
            <ul className="space-y-2 text-sm text-text-secondary">
              <li><Link to="/companies" className="hover:text-accent-primary transition-colors">Browse Companies</Link></li>
              <li><Link to="/salaries" className="hover:text-accent-primary transition-colors">Salary Insights</Link></li>
              <li><Link to="/community" className="hover:text-accent-primary transition-colors">Community Forum</Link></li>
              <li><Link to="/review/submit" className="hover:text-accent-primary transition-colors">Write Review</Link></li>
            </ul>
          </div>

          {/* Support */}
          <div>
            <h4 className="font-semibold mb-4 text-accent-primary uppercase tracking-wide">Support</h4>
            <ul className="space-y-2 text-sm text-text-secondary">
              <li><a href="#" className="hover:text-accent-primary transition-colors">Privacy Policy</a></li>
              <li><a href="#" className="hover:text-accent-primary transition-colors">Terms of Service</a></li>
              <li><a href="#" className="hover:text-accent-primary transition-colors">Community Guidelines</a></li>
              <li><a href="#" className="hover:text-accent-primary transition-colors">Report Issue</a></li>
            </ul>
          </div>
        </div>

        <div className="border-t border-border-primary mt-8 pt-8 flex flex-col md:flex-row justify-between items-center">
          <p className="text-sm text-text-secondary">
            © 2025 Haq Platform. Made with <Heart className="w-4 h-4 inline text-accent-primary" /> for Pakistani employees.
          </p>
          <div className="mt-4 md:mt-0">
            <p className="text-sm text-text-secondary">
              Anonymous • Secure • Empowering
            </p>
          </div>
        </div>
      </div>
    </footer>
  );
};
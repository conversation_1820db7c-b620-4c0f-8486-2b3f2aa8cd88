// Simple test to verify search API is working
const { supabase } = require('./src/lib/supabase');

async function testSearchAPI() {
  console.log('🧪 Testing Search API...\n');

  try {
    // Test basic query
    console.log('Testing basic companies query...');
    const { data: companies, error, count } = await supabase
      .from('companies')
      .select('company_id, name, industry, location', { count: 'exact' })
      .limit(5);

    if (error) {
      console.error('❌ Error:', error);
      return;
    }

    console.log(`✅ Found ${count} total companies`);
    console.log(`✅ Retrieved ${companies?.length || 0} companies`);
    
    if (companies && companies.length > 0) {
      console.log('Sample companies:');
      companies.forEach((company, index) => {
        console.log(`  ${index + 1}. ${company.name} (${company.industry}) - ${company.location}`);
      });
    }

    // Test search functionality
    console.log('\nTesting search functionality...');
    const { data: searchResults, error: searchError } = await supabase
      .from('companies')
      .select('company_id, name, industry, location')
      .ilike('name', '%tech%')
      .limit(3);

    if (searchError) {
      console.error('❌ Search error:', searchError);
      return;
    }

    console.log(`✅ Search for "tech" found ${searchResults?.length || 0} companies`);
    if (searchResults && searchResults.length > 0) {
      searchResults.forEach((company, index) => {
        console.log(`  ${index + 1}. ${company.name}`);
      });
    }

    console.log('\n🎉 Search API test completed successfully!');

  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

// Run the test
testSearchAPI();

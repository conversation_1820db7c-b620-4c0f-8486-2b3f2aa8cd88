{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 148, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Haq%20website%20v1/haq-frontend-nextjs/src/lib/supabase.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js'\nimport { createBrowserClient } from '@supabase/ssr'\n\n// Supabase configuration\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!\n\n// Singleton pattern to prevent multiple client instances (Context7 best practice)\nlet browserClientInstance: ReturnType<typeof createBrowserClient> | null = null\nlet serverClientInstance: ReturnType<typeof createClient> | null = null\n\n// Client-side Supabase client (for browser components)\nexport const createClientComponentClient = () => {\n  if (typeof window === 'undefined') {\n    // Server-side: create a new instance each time\n    return createBrowserClient(supabaseUrl, supabaseAnonKey)\n  }\n\n  // Client-side: use singleton\n  if (!browserClientInstance) {\n    browserClientInstance = createBrowserClient(supabaseUrl, supabase<PERSON>non<PERSON>ey)\n  }\n  return browserClientInstance\n}\n\n// Server-side Supabase client (for API routes and server components)\nexport const createServerClient = () => {\n  if (!serverClientInstance) {\n    serverClientInstance = createClient(supabaseUrl, supabaseAnonKey)\n  }\n  return serverClientInstance\n}\n\n// Main client for general use (uses public schema by default)\nexport const supabase = createServerClient()\n\n// Database types (will be generated later)\nexport type Database = {\n  haq_users_db: {\n    Tables: {\n      users: {\n        Row: {\n          user_id: string\n          username: string\n          email: string\n          password_hash: string\n          role: 'user' | 'admin'\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          user_id?: string\n          username: string\n          email: string\n          password_hash: string\n          role?: 'user' | 'admin'\n          created_at?: string\n          updated_at?: string\n        }\n        Update: {\n          user_id?: string\n          username?: string\n          email?: string\n          password_hash?: string\n          role?: 'user' | 'admin'\n          created_at?: string\n          updated_at?: string\n        }\n      }\n    }\n  }\n  haq_content_db: {\n    Tables: {\n      companies: {\n        Row: {\n          company_id: string\n          name: string\n          slug: string\n          industry: string | null\n          location: string | null\n          description: string | null\n          website_url: string | null\n          logo_url: string | null\n          employee_count_range: string | null\n          founded_year: number | null\n          haq_score: number\n          total_reviews: number\n          is_verified: boolean\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          company_id?: string\n          name: string\n          slug: string\n          industry?: string | null\n          location?: string | null\n          description?: string | null\n          website_url?: string | null\n          logo_url?: string | null\n          employee_count_range?: string | null\n          founded_year?: number | null\n          haq_score?: number\n          total_reviews?: number\n          is_verified?: boolean\n          created_at?: string\n          updated_at?: string\n        }\n        Update: {\n          company_id?: string\n          name?: string\n          slug?: string\n          industry?: string | null\n          location?: string | null\n          description?: string | null\n          website_url?: string | null\n          logo_url?: string | null\n          employee_count_range?: string | null\n          founded_year?: number | null\n          haq_score?: number\n          total_reviews?: number\n          is_verified?: boolean\n          created_at?: string\n          updated_at?: string\n        }\n      }\n      reviews: {\n        Row: {\n          review_id: string\n          company_id: string\n          anonymous_user_hash: string\n          overall_rating: number | null\n          work_life_balance_rating: number | null\n          compensation_rating: number | null\n          management_rating: number | null\n          culture_rating: number | null\n          title: string\n          pros: string | null\n          cons: string | null\n          advice_to_management: string | null\n          job_title: string | null\n          employment_status: 'current' | 'former' | null\n          employment_duration: string | null\n          department: string | null\n          location: string | null\n          is_approved: boolean\n          is_featured: boolean\n          helpful_count: number\n          created_at: string\n          updated_at: string\n        }\n      }\n      salary_reports: {\n        Row: {\n          salary_id: string\n          company_id: string\n          anonymous_user_hash: string\n          job_title: string\n          department: string | null\n          location: string | null\n          experience_level: 'entry' | 'mid' | 'senior' | 'lead' | 'executive' | null\n          base_salary: number | null\n          bonus: number\n          stock_options: number\n          total_compensation: number | null\n          currency: string\n          employment_type: 'full-time' | 'part-time' | 'contract' | 'internship' | null\n          years_of_experience: number | null\n          years_at_company: number | null\n          is_approved: boolean\n          created_at: string\n          updated_at: string\n        }\n      }\n      company_flags: {\n        Row: {\n          flag_id: string\n          company_id: string\n          flag_type: 'red' | 'green' | null\n          flag_text: string\n          flag_count: number\n          created_at: string\n        }\n      }\n    }\n  }\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AAAA;;;AAEA,yBAAyB;AACzB,MAAM;AACN,MAAM;AAEN,kFAAkF;AAClF,IAAI,wBAAuE;AAC3E,IAAI,uBAA+D;AAG5D,MAAM,8BAA8B;IACzC,wCAAmC;QACjC,+CAA+C;QAC/C,OAAO,CAAA,GAAA,4KAAA,CAAA,sBAAmB,AAAD,EAAE,aAAa;IAC1C;;AAOF;AAGO,MAAM,qBAAqB;IAChC,IAAI,CAAC,sBAAsB;QACzB,uBAAuB,CAAA,GAAA,yLAAA,CAAA,eAAY,AAAD,EAAE,aAAa;IACnD;IACA,OAAO;AACT;AAGO,MAAM,WAAW", "debugId": null}}, {"offset": {"line": 184, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Haq%20website%20v1/haq-frontend-nextjs/src/app/api/companies/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { supabase } from '@/lib/supabase';\n\n/**\n * GET /api/companies\n * Public endpoint to list all companies for review form dropdown/search\n * Following HAQ-rules.md PUB-01 specification\n * \n * Query Parameters:\n * - q: Search query for company name (optional)\n * - page: Page number for pagination (default: 1)\n * - limit: Number of results per page (default: 50, max: 100)\n */\nexport async function GET(request: NextRequest) {\n  try {\n    const { searchParams } = new URL(request.url);\n    const searchQuery = searchParams.get('q') || '';\n    const page = Math.max(1, parseInt(searchParams.get('page') || '1'));\n    const limit = Math.min(100, Math.max(1, parseInt(searchParams.get('limit') || '50')));\n    \n    // Calculate offset for pagination\n    const offset = (page - 1) * limit;\n\n    // Build query for companies\n    let query = supabase\n      .from('companies')\n      .select(`\n        company_id,\n        name,\n        slug,\n        industry,\n        location,\n        website_url,\n        logo_url,\n        employee_count_range,\n        founded_year,\n        haq_score,\n        total_reviews,\n        is_verified\n      `)\n      .order('name', { ascending: true });\n\n    // Apply search filter if provided\n    if (searchQuery.trim()) {\n      // Use case-insensitive search on company name\n      query = query.ilike('name', `%${searchQuery.trim()}%`);\n    }\n\n    // Apply pagination\n    query = query.range(offset, offset + limit - 1);\n\n    const { data: companies, error, count } = await query;\n\n    if (error) {\n      console.error('Error fetching companies:', error);\n      return NextResponse.json(\n        { \n          success: false, \n          message: 'Failed to fetch companies',\n          error: 'Database query failed'\n        },\n        { status: 500 }\n      );\n    }\n\n    // Get total count for pagination metadata\n    let totalCount = 0;\n    if (searchQuery.trim()) {\n      const { count: searchCount } = await supabase\n        .from('companies')\n        .select('*', { count: 'exact', head: true })\n        .ilike('name', `%${searchQuery.trim()}%`);\n      totalCount = searchCount || 0;\n    } else {\n      const { count: allCount } = await supabase\n        .from('companies')\n        .select('*', { count: 'exact', head: true });\n      totalCount = allCount || 0;\n    }\n\n    // Return paginated response\n    return NextResponse.json({\n      success: true,\n      data: {\n        companies: companies || [],\n        pagination: {\n          page,\n          limit,\n          total: totalCount,\n          totalPages: Math.ceil(totalCount / limit),\n          hasNext: page * limit < totalCount,\n          hasPrev: page > 1\n        },\n        search: searchQuery || null\n      }\n    }, {\n      headers: {\n        // Add caching headers as per HAQ-rules.md CDN_SHORT policy\n        'Cache-Control': 'public, max-age=300, s-maxage=300', // 5 minutes\n        'Vary': 'Accept-Encoding'\n      }\n    });\n\n  } catch (error) {\n    console.error('Companies API error:', error);\n    return NextResponse.json(\n      { \n        success: false, \n        message: 'Internal server error',\n        error: 'Unexpected error occurred'\n      },\n      { status: 500 }\n    );\n  }\n}\n\n/**\n * Handle unsupported HTTP methods\n */\nexport async function POST() {\n  return NextResponse.json(\n    { success: false, message: 'Method not allowed' },\n    { status: 405, headers: { 'Allow': 'GET' } }\n  );\n}\n\nexport async function PUT() {\n  return NextResponse.json(\n    { success: false, message: 'Method not allowed' },\n    { status: 405, headers: { 'Allow': 'GET' } }\n  );\n}\n\nexport async function DELETE() {\n  return NextResponse.json(\n    { success: false, message: 'Method not allowed' },\n    { status: 405, headers: { 'Allow': 'GET' } }\n  );\n}\n\nexport async function PATCH() {\n  return NextResponse.json(\n    { success: false, message: 'Method not allowed' },\n    { status: 405, headers: { 'Allow': 'GET' } }\n  );\n}\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;;;AAYO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,cAAc,aAAa,GAAG,CAAC,QAAQ;QAC7C,MAAM,OAAO,KAAK,GAAG,CAAC,GAAG,SAAS,aAAa,GAAG,CAAC,WAAW;QAC9D,MAAM,QAAQ,KAAK,GAAG,CAAC,KAAK,KAAK,GAAG,CAAC,GAAG,SAAS,aAAa,GAAG,CAAC,YAAY;QAE9E,kCAAkC;QAClC,MAAM,SAAS,CAAC,OAAO,CAAC,IAAI;QAE5B,4BAA4B;QAC5B,IAAI,QAAQ,wHAAA,CAAA,WAAQ,CACjB,IAAI,CAAC,aACL,MAAM,CAAC,CAAC;;;;;;;;;;;;;MAaT,CAAC,EACA,KAAK,CAAC,QAAQ;YAAE,WAAW;QAAK;QAEnC,kCAAkC;QAClC,IAAI,YAAY,IAAI,IAAI;YACtB,8CAA8C;YAC9C,QAAQ,MAAM,KAAK,CAAC,QAAQ,CAAC,CAAC,EAAE,YAAY,IAAI,GAAG,CAAC,CAAC;QACvD;QAEA,mBAAmB;QACnB,QAAQ,MAAM,KAAK,CAAC,QAAQ,SAAS,QAAQ;QAE7C,MAAM,EAAE,MAAM,SAAS,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,MAAM;QAEhD,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBACE,SAAS;gBACT,SAAS;gBACT,OAAO;YACT,GACA;gBAAE,QAAQ;YAAI;QAElB;QAEA,0CAA0C;QAC1C,IAAI,aAAa;QACjB,IAAI,YAAY,IAAI,IAAI;YACtB,MAAM,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,wHAAA,CAAA,WAAQ,CAC1C,IAAI,CAAC,aACL,MAAM,CAAC,KAAK;gBAAE,OAAO;gBAAS,MAAM;YAAK,GACzC,KAAK,CAAC,QAAQ,CAAC,CAAC,EAAE,YAAY,IAAI,GAAG,CAAC,CAAC;YAC1C,aAAa,eAAe;QAC9B,OAAO;YACL,MAAM,EAAE,OAAO,QAAQ,EAAE,GAAG,MAAM,wHAAA,CAAA,WAAQ,CACvC,IAAI,CAAC,aACL,MAAM,CAAC,KAAK;gBAAE,OAAO;gBAAS,MAAM;YAAK;YAC5C,aAAa,YAAY;QAC3B;QAEA,4BAA4B;QAC5B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;gBACJ,WAAW,aAAa,EAAE;gBAC1B,YAAY;oBACV;oBACA;oBACA,OAAO;oBACP,YAAY,KAAK,IAAI,CAAC,aAAa;oBACnC,SAAS,OAAO,QAAQ;oBACxB,SAAS,OAAO;gBAClB;gBACA,QAAQ,eAAe;YACzB;QACF,GAAG;YACD,SAAS;gBACP,2DAA2D;gBAC3D,iBAAiB;gBACjB,QAAQ;YACV;QACF;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wBAAwB;QACtC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YACE,SAAS;YACT,SAAS;YACT,OAAO;QACT,GACA;YAAE,QAAQ;QAAI;IAElB;AACF;AAKO,eAAe;IACpB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;QAAE,SAAS;QAAO,SAAS;IAAqB,GAChD;QAAE,QAAQ;QAAK,SAAS;YAAE,SAAS;QAAM;IAAE;AAE/C;AAEO,eAAe;IACpB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;QAAE,SAAS;QAAO,SAAS;IAAqB,GAChD;QAAE,QAAQ;QAAK,SAAS;YAAE,SAAS;QAAM;IAAE;AAE/C;AAEO,eAAe;IACpB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;QAAE,SAAS;QAAO,SAAS;IAAqB,GAChD;QAAE,QAAQ;QAAK,SAAS;YAAE,SAAS;QAAM;IAAE;AAE/C;AAEO,eAAe;IACpB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;QAAE,SAAS;QAAO,SAAS;IAAqB,GAChD;QAAE,QAAQ;QAAK,SAAS;YAAE,SAAS;QAAM;IAAE;AAE/C", "debugId": null}}]}
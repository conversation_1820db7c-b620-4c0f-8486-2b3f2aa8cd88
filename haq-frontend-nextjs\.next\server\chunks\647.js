exports.id=647,exports.ids=[647],exports.modules={12388:(e,t,r)=>{"use strict";r.d(t,{SWRProvider:()=>x});var s=r(60687),n=r(5077),a=r(43530),o=r(29605);let i="https://wqbuilazpyxpwyuwuqpi.supabase.co",c="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.GeRI54Rskwdbfm9_lRxy1-7YQ8vA74JWblNF2GRpzrI",l=null,d=()=>(0,o.createBrowserClient)(i,c);l=(0,a.createClient)(i,c);let m={fetcher:async e=>{if(d(),e.startsWith("/api/companies"))return[{id:1,company_id:1,name:"TechCorp Solutions",slug:"techcorp-solutions",logo_url:"/placeholder-company.svg",description:"Leading technology solutions provider",location:"Karachi, Pakistan",industry:"Technology",employee_count:"500-1000",haq_score:85,total_reviews:24,redFlags:["High turnover rate","Unpaid overtime"],greenFlags:["Good benefits","Career growth opportunities"]},{id:2,company_id:2,name:"InnovatePak",slug:"innovatepak",logo_url:"/placeholder-company.svg",description:"Innovation-driven software company",location:"Lahore, Pakistan",industry:"Software",employee_count:"100-500",haq_score:78,total_reviews:18,redFlags:["Limited remote work"],greenFlags:["Modern office","Learning opportunities","Flexible hours"]},{id:3,company_id:3,name:"DataFlow Systems",slug:"dataflow-systems",logo_url:"/placeholder-company.svg",description:"Data analytics and business intelligence",location:"Islamabad, Pakistan",industry:"Analytics",employee_count:"50-100",haq_score:72,total_reviews:12,redFlags:["Micromanagement"],greenFlags:["Good work-life balance","Competitive salary"]}];if(e.startsWith("/api/company/")){let t=e.split("/").pop(),r=[{id:1,company_id:1,name:"TechCorp Solutions",slug:"techcorp-solutions",logo_url:"/placeholder-company.svg",description:"Leading technology solutions provider",location:"Karachi, Pakistan",industry:"Technology",employee_count:"500-1000",haq_score:85,total_reviews:24,redFlags:["High turnover rate","Unpaid overtime"],greenFlags:["Good benefits","Career growth opportunities"]}];return r.find(e=>e.slug===t)||r[0]}let t=await fetch(e);if(!t.ok)throw Error("Failed to fetch");return t.json()},revalidateOnFocus:!1,revalidateOnReconnect:!0,refreshInterval:0,errorRetryCount:3,errorRetryInterval:5e3,onError:e=>{console.error("SWR Error:",e)},onSuccess:(e,t)=>{}};function x({children:e}){return(0,s.jsx)(n.BE,{value:m,children:e})}},14255:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,16444,23)),Promise.resolve().then(r.t.bind(r,16042,23)),Promise.resolve().then(r.t.bind(r,88170,23)),Promise.resolve().then(r.t.bind(r,49477,23)),Promise.resolve().then(r.t.bind(r,29345,23)),Promise.resolve().then(r.t.bind(r,12089,23)),Promise.resolve().then(r.t.bind(r,46577,23)),Promise.resolve().then(r.t.bind(r,31307,23))},15569:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,85814,23)),Promise.resolve().then(r.bind(r,15736)),Promise.resolve().then(r.bind(r,63213)),Promise.resolve().then(r.bind(r,12388))},15736:(e,t,r)=>{"use strict";r.d(t,{Header:()=>g});var s=r(60687),n=r(43210),a=r(85814),o=r.n(a),i=r(16189),c=r(99891),l=r(23928),d=r(33872),m=r(41312),x=r(58869),h=r(84027),p=r(40083),u=r(98712),y=r(11860),f=r(12941),v=r(63213);let g=()=>{let[e,t]=(0,n.useState)(!1),[r,a]=(0,n.useState)(!1),g=(0,i.usePathname)(),{user:b,isAuthenticated:j,isLoading:w,logout:N}=(0,v.As)(),A=[{name:"Companies",href:"/companies",icon:c.A},{name:"Salaries",href:"/salaries",icon:l.A},{name:"Community",href:"/community",icon:d.A}],C=async()=>{try{await N(),a(!1)}catch(e){console.error("Logout failed:",e)}};return(0,s.jsxs)("header",{className:"bg-background-primary border-b border-border-primary sticky top-0 z-50",children:[(0,s.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"flex justify-between items-center h-16",children:[(0,s.jsxs)(o(),{href:"/",className:"flex items-center space-x-2 group",children:[(0,s.jsx)("div",{className:"w-8 h-8 bg-gradient-to-br from-accent-primary to-accent-secondary rounded-lg flex items-center justify-center group-hover:scale-105 transition-transform duration-200",children:(0,s.jsx)(c.A,{className:"w-5 h-5 text-text-on-accent"})}),(0,s.jsxs)("div",{className:"flex flex-col",children:[(0,s.jsx)("span",{className:"text-xl font-bold text-accent-primary",children:"Haq"}),(0,s.jsx)("span",{className:"text-xs text-text-secondary -mt-1",children:"حق"})]})]}),(0,s.jsx)("nav",{className:"hidden md:flex items-center space-x-8",children:A.map(e=>{let t=e.icon,r=g===e.href;return(0,s.jsxs)(o(),{href:e.href,className:`flex items-center space-x-1 px-3 py-2 rounded-lg text-navigation-link font-medium transition-all duration-200 uppercase tracking-wide ${r?"text-accent-primary bg-surface-secondary":"text-text-secondary hover:text-accent-primary hover:bg-surface-primary"}`,children:[(0,s.jsx)(t,{className:"w-4 h-4"}),(0,s.jsx)("span",{children:e.name})]},e.name)})}),(0,s.jsxs)("div",{className:"hidden md:flex items-center space-x-4",children:[j&&(0,s.jsxs)(o(),{href:"/review/submit",className:"bg-accent-primary hover:bg-accent-secondary text-text-on-accent px-4 py-2 rounded-lg text-sm font-semibold transition-all duration-200 flex items-center space-x-2 hover:shadow-glow transform hover:-translate-y-0.5",children:[(0,s.jsx)(m.A,{className:"w-4 h-4"}),(0,s.jsx)("span",{children:"Write Review"})]}),w?(0,s.jsx)("div",{className:"w-8 h-8 animate-spin rounded-full border-2 border-accent-primary border-t-transparent"}):j&&b?(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsxs)("button",{onClick:()=>a(!r),className:"flex items-center space-x-2 px-3 py-2 rounded-lg text-text-secondary hover:text-accent-primary hover:bg-surface-primary transition-all duration-200",children:[(0,s.jsx)("div",{className:"w-8 h-8 bg-gradient-to-br from-accent-primary to-accent-secondary rounded-full flex items-center justify-center",children:(0,s.jsx)(x.A,{className:"w-4 h-4 text-text-on-accent"})}),(0,s.jsx)("span",{className:"text-sm font-medium",children:b.username}),(0,s.jsx)("svg",{className:`w-4 h-4 transition-transform duration-200 ${r?"rotate-180":""}`,fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 9l-7 7-7-7"})})]}),r&&(0,s.jsxs)("div",{className:"absolute right-0 mt-2 w-48 bg-surface-primary border border-border-primary rounded-lg shadow-lg py-1 z-50",children:[(0,s.jsxs)("div",{className:"px-4 py-2 border-b border-border-primary",children:[(0,s.jsx)("p",{className:"text-sm font-medium text-text-primary",children:b.username}),(0,s.jsx)("p",{className:"text-xs text-text-secondary",children:b.email}),(0,s.jsx)("span",{className:`inline-block px-2 py-1 text-xs rounded-full mt-1 ${"admin"===b.role?"bg-accent-primary text-text-on-accent":"bg-surface-secondary text-text-secondary"}`,children:b.role})]}),(0,s.jsxs)(o(),{href:"/profile",onClick:()=>a(!1),className:"flex items-center space-x-2 px-4 py-2 text-sm text-text-secondary hover:text-accent-primary hover:bg-surface-secondary transition-colors duration-200",children:[(0,s.jsx)(h.A,{className:"w-4 h-4"}),(0,s.jsx)("span",{children:"Profile Settings"})]}),(0,s.jsxs)("button",{onClick:C,className:"w-full flex items-center space-x-2 px-4 py-2 text-sm text-text-secondary hover:text-red-600 hover:bg-surface-secondary transition-colors duration-200",children:[(0,s.jsx)(p.A,{className:"w-4 h-4"}),(0,s.jsx)("span",{children:"Sign Out"})]})]})]}):(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsxs)(o(),{href:"/auth/login",className:"flex items-center space-x-1 px-4 py-2 text-sm font-medium text-text-secondary hover:text-accent-primary transition-colors duration-200",children:[(0,s.jsx)(u.A,{className:"w-4 h-4"}),(0,s.jsx)("span",{children:"Sign In"})]}),(0,s.jsxs)(o(),{href:"/auth/signup",className:"bg-accent-primary hover:bg-accent-secondary text-text-on-accent px-4 py-2 rounded-lg text-sm font-semibold transition-all duration-200 flex items-center space-x-1 hover:shadow-glow transform hover:-translate-y-0.5",children:[(0,s.jsx)(x.A,{className:"w-4 h-4"}),(0,s.jsx)("span",{children:"Sign Up"})]})]})]}),(0,s.jsx)("button",{onClick:()=>t(!e),className:"md:hidden p-2 rounded-lg text-text-secondary hover:text-accent-primary hover:bg-surface-primary transition-colors duration-200",children:e?(0,s.jsx)(y.A,{className:"w-6 h-6"}):(0,s.jsx)(f.A,{className:"w-6 h-6"})})]})}),e&&(0,s.jsx)("div",{className:"md:hidden bg-surface-primary border-t border-border-primary",children:(0,s.jsxs)("div",{className:"px-4 py-3 space-y-3",children:[A.map(e=>{let r=e.icon,n=g===e.href;return(0,s.jsxs)(o(),{href:e.href,onClick:()=>t(!1),className:`flex items-center space-x-3 px-3 py-2 rounded-lg text-sm font-medium transition-colors duration-200 ${n?"text-accent-primary bg-surface-secondary":"text-text-secondary hover:text-accent-primary hover:bg-surface-secondary"}`,children:[(0,s.jsx)(r,{className:"w-5 h-5"}),(0,s.jsx)("span",{children:e.name})]},e.name)}),j?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)(o(),{href:"/review/submit",onClick:()=>t(!1),className:"flex items-center space-x-3 bg-accent-primary hover:bg-accent-secondary text-text-on-accent px-3 py-2 rounded-lg text-sm font-medium transition-colors duration-200",children:[(0,s.jsx)(m.A,{className:"w-5 h-5"}),(0,s.jsx)("span",{children:"Write Review"})]}),(0,s.jsxs)("div",{className:"border-t border-border-primary pt-3 mt-3",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-3 px-3 py-2 mb-2",children:[(0,s.jsx)("div",{className:"w-8 h-8 bg-gradient-to-br from-accent-primary to-accent-secondary rounded-full flex items-center justify-center",children:(0,s.jsx)(x.A,{className:"w-4 h-4 text-text-on-accent"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm font-medium text-text-primary",children:b?.username}),(0,s.jsx)("p",{className:"text-xs text-text-secondary",children:b?.email})]})]}),(0,s.jsxs)(o(),{href:"/profile",onClick:()=>t(!1),className:"flex items-center space-x-3 px-3 py-2 rounded-lg text-sm font-medium text-text-secondary hover:text-accent-primary hover:bg-surface-secondary transition-colors duration-200",children:[(0,s.jsx)(h.A,{className:"w-5 h-5"}),(0,s.jsx)("span",{children:"Profile Settings"})]}),(0,s.jsxs)("button",{onClick:()=>{C(),t(!1)},className:"w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-sm font-medium text-text-secondary hover:text-red-600 hover:bg-surface-secondary transition-colors duration-200",children:[(0,s.jsx)(p.A,{className:"w-5 h-5"}),(0,s.jsx)("span",{children:"Sign Out"})]})]})]}):(0,s.jsxs)("div",{className:"border-t border-border-primary pt-3 mt-3 space-y-2",children:[(0,s.jsxs)(o(),{href:"/auth/login",onClick:()=>t(!1),className:"flex items-center space-x-3 px-3 py-2 rounded-lg text-sm font-medium text-text-secondary hover:text-accent-primary hover:bg-surface-secondary transition-colors duration-200",children:[(0,s.jsx)(u.A,{className:"w-5 h-5"}),(0,s.jsx)("span",{children:"Sign In"})]}),(0,s.jsxs)(o(),{href:"/auth/signup",onClick:()=>t(!1),className:"flex items-center space-x-3 bg-accent-primary hover:bg-accent-secondary text-text-on-accent px-3 py-2 rounded-lg text-sm font-medium transition-colors duration-200",children:[(0,s.jsx)(x.A,{className:"w-5 h-5"}),(0,s.jsx)("span",{children:"Sign Up"})]})]})]})})]})}},18293:(e,t,r)=>{"use strict";r.d(t,{SWRProvider:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call SWRProvider() from the server but SWRProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Haq website v1\\haq-frontend-nextjs\\src\\providers\\SWRProvider.tsx","SWRProvider")},22503:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,86346,23)),Promise.resolve().then(r.t.bind(r,27924,23)),Promise.resolve().then(r.t.bind(r,35656,23)),Promise.resolve().then(r.t.bind(r,40099,23)),Promise.resolve().then(r.t.bind(r,38243,23)),Promise.resolve().then(r.t.bind(r,28827,23)),Promise.resolve().then(r.t.bind(r,62763,23)),Promise.resolve().then(r.t.bind(r,97173,23))},25297:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,4536,23)),Promise.resolve().then(r.bind(r,68926)),Promise.resolve().then(r.bind(r,29131)),Promise.resolve().then(r.bind(r,18293))},29131:(e,t,r)=>{"use strict";r.d(t,{AuthProvider:()=>n});var s=r(12907);let n=(0,s.registerClientReference)(function(){throw Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Haq website v1\\haq-frontend-nextjs\\src\\contexts\\AuthContext.tsx","AuthProvider");(0,s.registerClientReference)(function(){throw Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Haq website v1\\haq-frontend-nextjs\\src\\contexts\\AuthContext.tsx","useAuth"),(0,s.registerClientReference)(function(){throw Error("Attempted to call withAuth() from the server but withAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Haq website v1\\haq-frontend-nextjs\\src\\contexts\\AuthContext.tsx","withAuth"),(0,s.registerClientReference)(function(){throw Error("Attempted to call useRole() from the server but useRole is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Haq website v1\\haq-frontend-nextjs\\src\\contexts\\AuthContext.tsx","useRole"),(0,s.registerClientReference)(function(){throw Error("Attempted to call useAdminOnly() from the server but useAdminOnly is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Haq website v1\\haq-frontend-nextjs\\src\\contexts\\AuthContext.tsx","useAdminOnly")},39727:()=>{},47990:()=>{},61135:()=>{},63213:(e,t,r)=>{"use strict";r.d(t,{As:()=>l,AuthProvider:()=>c});var s=r(60687),n=r(43210);let a={user:null,isLoading:!0,isAuthenticated:!1,error:null};function o(e,t){switch(t.type){case"AUTH_START":return{...e,isLoading:!0,error:null};case"AUTH_SUCCESS":return{...e,user:t.payload,isAuthenticated:!0,isLoading:!1,error:null};case"AUTH_FAILURE":return{...e,user:null,isAuthenticated:!1,isLoading:!1,error:t.payload};case"AUTH_LOGOUT":return{...e,user:null,isAuthenticated:!1,isLoading:!1,error:null};case"CLEAR_ERROR":return{...e,error:null};case"SET_LOADING":return{...e,isLoading:t.payload};default:return e}}let i=(0,n.createContext)(null);function c({children:e}){let[t,r]=(0,n.useReducer)(o,a),c=(0,n.useCallback)(async()=>{try{r({type:"SET_LOADING",payload:!0});let e=await fetch("/api/auth/me",{method:"GET",credentials:"include"});if(e.ok){let t=await e.json();t.success&&t.user?(console.log("✅ Auth check: User authenticated",t.user.username),r({type:"AUTH_SUCCESS",payload:t.user})):(console.log("❌ Auth check: No user found"),r({type:"AUTH_LOGOUT"}))}else console.log("❌ Auth check: Response not ok",e.status),r({type:"AUTH_LOGOUT"})}catch(e){console.error("Session check failed:",e),r({type:"AUTH_LOGOUT"})}finally{r({type:"SET_LOADING",payload:!1})}},[]),l=(0,n.useCallback)(async(e,t)=>{try{r({type:"AUTH_START"});let s=await fetch("/api/auth/login",{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({email:e,password:t})}),n=await s.json();if(n.success&&n.user)return r({type:"AUTH_SUCCESS",payload:n.user}),{success:!0,message:n.message};return r({type:"AUTH_FAILURE",payload:n.message}),{success:!1,message:n.message}}catch(t){let e="Network error. Please try again.";return r({type:"AUTH_FAILURE",payload:e}),{success:!1,message:e}}},[]),d=(0,n.useCallback)(async(e,t,s)=>{try{r({type:"AUTH_START"});let n=await fetch("/api/auth/register",{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({username:e,email:t,password:s})}),a=await n.json();if(a.success&&a.user)return r({type:"AUTH_SUCCESS",payload:a.user}),{success:!0,message:a.message};return r({type:"AUTH_FAILURE",payload:a.message}),{success:!1,message:a.message}}catch(t){let e="Network error. Please try again.";return r({type:"AUTH_FAILURE",payload:e}),{success:!1,message:e}}},[]),m=(0,n.useCallback)(async()=>{try{r({type:"SET_LOADING",payload:!0}),await fetch("/api/auth/logout",{method:"POST",credentials:"include"}),r({type:"AUTH_LOGOUT"})}catch(e){console.error("Logout error:",e),r({type:"AUTH_LOGOUT"})}finally{r({type:"SET_LOADING",payload:!1})}},[]),x=(0,n.useCallback)(()=>{r({type:"CLEAR_ERROR"})},[]),h=(0,n.useCallback)(async()=>{await c()},[c]),p=(0,n.useMemo)(()=>({...t,login:l,register:d,logout:m,clearError:x,refreshUser:h}),[t,l,d,m,x,h]);return(0,s.jsx)(i.Provider,{value:p,children:e})}function l(){let e=(0,n.useContext)(i);if(!e)throw Error("useAuth must be used within an AuthProvider");return e}},68926:(e,t,r)=>{"use strict";r.d(t,{Header:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call Header() from the server but Header is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Haq website v1\\haq-frontend-nextjs\\src\\components\\layout\\Header.tsx","Header")},70058:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=70058,e.exports=t},82893:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>f,metadata:()=>y});var s=r(37413),n=r(63441),a=r.n(n);r(61135);var o=r(68926);r(61120);var i=r(4536),c=r.n(i),l=r(69117),d=r(60343),m=r(71750),x=r(49046);let h=()=>(0,s.jsx)("footer",{className:"bg-background-secondary border-t border-border-primary",children:(0,s.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12",children:[(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-8",children:[(0,s.jsxs)("div",{className:"col-span-1 md:col-span-2",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2 mb-4",children:[(0,s.jsx)("div",{className:"w-8 h-8 bg-gradient-to-br from-accent-primary to-accent-secondary rounded-lg flex items-center justify-center",children:(0,s.jsx)(l.A,{className:"w-5 h-5 text-text-on-accent"})}),(0,s.jsxs)("div",{className:"flex flex-col",children:[(0,s.jsx)("span",{className:"text-xl font-bold text-accent-primary",children:"Haq"}),(0,s.jsx)("span",{className:"text-xs text-text-secondary -mt-1",children:"حق"})]})]}),(0,s.jsx)("p",{className:"text-text-secondary mb-4 max-w-md",children:"Empowering Pakistani employees through transparency, anonymous reviews, and workplace accountability. Your voice matters in building a fair workplace culture."}),(0,s.jsxs)("div",{className:"flex space-x-4",children:[(0,s.jsx)("a",{href:"#",className:"text-text-secondary hover:text-accent-primary transition-colors duration-200",children:(0,s.jsx)(d.A,{className:"w-5 h-5"})}),(0,s.jsx)("a",{href:"#",className:"text-text-secondary hover:text-accent-primary transition-colors duration-200",children:(0,s.jsx)(m.A,{className:"w-5 h-5"})}),(0,s.jsx)("a",{href:"#",className:"text-text-secondary hover:text-accent-primary transition-colors duration-200",children:(0,s.jsx)(x.A,{className:"w-5 h-5"})})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-text-primary font-semibold mb-4 uppercase tracking-wide",children:"Quick Links"}),(0,s.jsxs)("ul",{className:"space-y-2",children:[(0,s.jsx)("li",{children:(0,s.jsx)(c(),{href:"/companies",className:"text-text-secondary hover:text-accent-primary transition-colors duration-200",children:"Browse Companies"})}),(0,s.jsx)("li",{children:(0,s.jsx)(c(),{href:"/review/submit",className:"text-text-secondary hover:text-accent-primary transition-colors duration-200",children:"Write Review"})}),(0,s.jsx)("li",{children:(0,s.jsx)(c(),{href:"/salaries",className:"text-text-secondary hover:text-accent-primary transition-colors duration-200",children:"Salary Insights"})}),(0,s.jsx)("li",{children:(0,s.jsx)(c(),{href:"/community",className:"text-text-secondary hover:text-accent-primary transition-colors duration-200",children:"Community"})})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-text-primary font-semibold mb-4 uppercase tracking-wide",children:"Legal"}),(0,s.jsxs)("ul",{className:"space-y-2",children:[(0,s.jsx)("li",{children:(0,s.jsx)(c(),{href:"/privacy",className:"text-text-secondary hover:text-accent-primary transition-colors duration-200",children:"Privacy Policy"})}),(0,s.jsx)("li",{children:(0,s.jsx)(c(),{href:"/terms",className:"text-text-secondary hover:text-accent-primary transition-colors duration-200",children:"Terms of Service"})}),(0,s.jsx)("li",{children:(0,s.jsx)(c(),{href:"/guidelines",className:"text-text-secondary hover:text-accent-primary transition-colors duration-200",children:"Community Guidelines"})}),(0,s.jsx)("li",{children:(0,s.jsx)(c(),{href:"/about",className:"text-text-secondary hover:text-accent-primary transition-colors duration-200",children:"About Us"})})]})]})]}),(0,s.jsxs)("div",{className:"border-t border-border-primary mt-8 pt-8 flex flex-col md:flex-row justify-between items-center",children:[(0,s.jsx)("p",{className:"text-text-secondary text-sm",children:"\xa9 2025 Haq Platform. All rights reserved. Built for Pakistani employees."}),(0,s.jsx)("p",{className:"text-text-secondary text-sm mt-2 md:mt-0",children:"Made with ❤️ for workplace transparency"})]})]})});var p=r(18293),u=r(29131);let y={title:"Haq - Your Right to Workplace Transparency",description:"Empowering Pakistani employees through anonymous reviews, salary insights, and workplace accountability. Your voice matters in building a fair workplace culture.",keywords:"Pakistan jobs, workplace reviews, salary comparison, employee rights, workplace transparency",authors:[{name:"Haq Platform"}],openGraph:{title:"Haq - Your Right to Workplace Transparency",description:"Empowering Pakistani employees through anonymous reviews and workplace accountability.",type:"website",locale:"en_US"}};function f({children:e}){return(0,s.jsx)("html",{lang:"en",className:a().variable,children:(0,s.jsx)("body",{className:"min-h-screen bg-background-primary text-text-primary font-sora antialiased flex flex-col",suppressHydrationWarning:!0,children:(0,s.jsx)(u.AuthProvider,{children:(0,s.jsxs)(p.SWRProvider,{children:[(0,s.jsx)(o.Header,{}),(0,s.jsx)("main",{className:"flex-1",children:e}),(0,s.jsx)(h,{})]})})})})}}};
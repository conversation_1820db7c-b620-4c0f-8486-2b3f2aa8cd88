Core Methodology: Agile Vertical Slices
We will build the MVP feature by feature. Each "Slice" is a complete, testable piece of functionality that cuts through all layers of the application: the database, the backend logic, and the user interface. They are designed to be built sequentially.

Slice 0: The Foundation - Security & Project Setup (Prerequisite)
Goal: Establish the non-negotiable security and architectural foundation. This is not a feature; it is a mandatory first step.
Tasks:
Project Initialization:
Set up three separate code repositories (e.g., on GitHub): haq-frontend, haq-backend, haq-admin-panel.
Choose and initialize your tech stack (e.g., Next.js for frontend/admin, Node.js/Express for backend, PostgreSQL for the database).
Database Schema - "The Two Vaults":
Create two completely separate database schemas (or databases if your budget allows):
haq_users_db: This will hold sensitive user identity information.
haq_content_db: This will hold all public-facing content like reviews and company information.
CRITICAL: There must be NO foreign key relationship between these two databases. This logical separation is a core security principle for Haq.
Backend Server Configuration:
Configure your backend server to explicitly disable IP address logging. For Node.js/Express, this means not using logging middleware that captures req.ip.
Implement a strict CORS (Cross-Origin Resource Sharing) policy to only accept requests from your specific frontend and admin panel domains.
Set up HTTPS for all backend environments from day one, using self-signed certificates for local development.
Be Careful About:
Do NOT Skip This Slice: Building security features "later" is the number one cause of data breaches. This foundation is paramount.
Confirm Logging Settings: Double-check with your hosting provider that you have full control over server and application logs.
Use Security Headers: Implement security-focused middleware from the start (e.g., helmet for Node.js) to protect against common web vulnerabilities.
Slice 1: User Account System
Goal: Allow a user to create a secure, anonymous account. This account is the key to all future contributions.
Tasks:
DB Schema (haq_users_db):
Create a users table: user_id (UUID, Primary Key), username (text, unique), email (text, unique), password_hash (text), role (text, default 'user'), created_at (timestamp).
Backend API (/api/auth/...):
POST /register: Takes username, email, password. Hashes the password using a strong, slow algorithm like Argon2 (preferred) or bcrypt. Saves the new user to the users table.
POST /login: Takes email and password. Finds the user by email, compares the hashed password. If successful, generate a JWT (JSON Web Token). The JWT payload should only contain the user_id and role.
Frontend UI (haq-frontend):
Create "Sign Up" and "Log In" pages with simple forms.
Implement robust client-side and server-side validation (e.g., password strength, valid email format, username availability).
On successful login, store the JWT securely in an HttpOnly cookie to prevent XSS attacks from stealing it.
Be Careful About:
Password Security: Never store plaintext passwords. A slow, salted hash is non-negotiable.
JWT Security: Your JWT secret key must be a long, complex, random string stored as an environment variable (.env file), not hardcoded.
Slice 2: Admin-Managed Company Profiles
Goal: Create a backend system for admins to add companies, which can then be displayed publicly.
Tasks:
DB Schema (haq_content_db):
Create a companies table: company_id (UUID, Primary Key), name (text, unique), industry (text), hq_location (text), created_at (timestamp).
Admin Panel UI (haq-admin-panel):
Create a secure login page for admins. Use the role system from Slice 1 to grant access.
Create a "Companies" management page with a table listing all companies.
Create a simple form ("Add New Company") that allows an admin to input the company details.
Backend API (/api/admin/...):
POST /companies: A protected endpoint that only users with the admin role can access. Takes company data and inserts it into the companies table.
GET /companies: A protected endpoint to list all companies for the admin dashboard.
Be Careful About:
Admin Authorization: Every single admin API endpoint must check for the JWT and validate that the role in the payload is 'admin'.
Slice 3: Anonymous Review Submission
Goal: Allow a logged-in user to submit a detailed, structured review for an existing company.
Tasks:
DB Schema (haq_content_db):
Create a reviews table: review_id (UUID, PK), company_id (UUID, FK to companies), author_id (UUID, NOT a foreign key), overall_rating (integer), pros (text), cons (text), advice_management (text), status (text, default 'pending'), created_at (timestamp).
Frontend UI (Review Form):
Build the full multi-step review form as designed in the feature list.
The "Select Company" step will fetch companies from a new public API endpoint.
The form must only be accessible to logged-in users.
For the MVP, the "PII Scrubber" can be a simple client-side check for keywords (like "manager," "CEO," names) that shows a warning.
Backend API:
GET /api/companies: A new public endpoint to list all companies for the form's dropdown/search.
POST /api/reviews: A protected endpoint for logged-in users.
It takes all the review data. It extracts the user_id from the user's JWT.
It saves the review to the reviews table with status: 'pending' and stores the user_id in the author_id column.
Be Careful About:
The author_id Link: This column in the reviews table is the most sensitive piece of information in this slice. It's necessary for future features like "delete my review," but it must be guarded.
Input Sanitization: On the backend, sanitize all text input thoroughly before saving it to the database to prevent stored XSS attacks.
Slice 4: Public Company & Review Display
Goal: Allow any visitor to see a company page and its approved reviews.
Tasks:
Frontend UI (Company Page):
Create a dynamic page route (e.g., /companies/[companyId]).
This page will fetch data from two new public API endpoints.
Display the company details at the top.
Below, map over the list of approved reviews and display each one in a card.
Backend API:
GET /api/companies/:id: A public endpoint. Fetches a single company's details.
GET /api/companies/:id/reviews: A public endpoint. Fetches all reviews for a company WHERE status = 'approved'.
CRITICAL: The JSON response for each review from this endpoint MUST NOT include the author_id. Manually shape the response object on the backend to exclude this sensitive field.
Be Careful About:
Data Leakage: This is the most important "Be Careful About" in the entire MVP. Write a specific test to ensure the public review endpoint never returns the author_id. A data leak here would destroy all user trust.
Slice 5: Basic Company Search
Goal: Allow any visitor to find a company from the homepage.
Tasks:
Frontend UI (Homepage & Search Results):
Build the homepage with the prominent search bar.
On search submission, navigate to a /search page.
The search page takes the query from the URL, calls the public search API, and displays the results as a list of links to the company pages.
Backend API:
GET /api/search/companies?q=...: A new public endpoint. For the MVP, a simple case-insensitive search (ILIKE '%query%') on the company name is sufficient.
Be Careful About:
SQL Injection: Your backend code must use parameterized queries (prepared statements). Never build a SQL string by concatenating user input into it. This is a fundamental security practice.
Slice 6: The Admin Review Moderation Queue
Goal: The final piece of the loop. Allow an admin to approve or reject new reviews, making them public.
Tasks:
Admin Panel UI:
Create a "Moderation Queue" page.
It fetches all reviews with status: 'pending'.
It displays each review's full content clearly.
Provide an "Approve" and "Reject" button for each review.
Backend API (/api/admin/reviews/...):
GET /pending: A protected endpoint for admins. Fetches all reviews where status = 'pending'.
PATCH /:id/status: A protected endpoint. Takes a review_id and a new status ('approved' or 'rejected') in the request body. Updates the review in the database.
Be Careful About:
Authorization: Again, ensure only authenticated admins can access these powerful endpoints.
Accidental Clicks: The admin UI should have a confirmation step ("Are you sure you want to approve this review?") to prevent mistakes.
{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Haq%20website%20v1/haq-frontend-nextjs/src/app/debug-search/page.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useState } from 'react';\n\nexport default function DebugSearchPage() {\n  const [searchQuery, setSearchQuery] = useState('tech');\n  const [results, setResults] = useState<any>(null);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n\n  const testSearchAPI = async () => {\n    setLoading(true);\n    setError(null);\n    setResults(null);\n\n    try {\n      const response = await fetch(`/api/search/companies?q=${encodeURIComponent(searchQuery)}`);\n      const data = await response.json();\n      \n      if (!response.ok) {\n        throw new Error(`HTTP ${response.status}: ${data.error || 'Unknown error'}`);\n      }\n      \n      setResults(data);\n    } catch (err) {\n      setError(err instanceof Error ? err.message : 'Unknown error');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const testCompaniesAPI = async () => {\n    setLoading(true);\n    setError(null);\n    setResults(null);\n\n    try {\n      const response = await fetch(`/api/companies?q=${encodeURIComponent(searchQuery)}&limit=5`);\n      const data = await response.json();\n      \n      if (!response.ok) {\n        throw new Error(`HTTP ${response.status}: ${data.error || 'Unknown error'}`);\n      }\n      \n      setResults(data);\n    } catch (err) {\n      setError(err instanceof Error ? err.message : 'Unknown error');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen bg-background-primary p-8\">\n      <div className=\"max-w-4xl mx-auto\">\n        <h1 className=\"text-3xl font-bold text-text-primary mb-8\">Search API Debug</h1>\n        \n        <div className=\"bg-surface-primary border border-border-primary rounded-lg p-6 mb-6\">\n          <h2 className=\"text-xl font-semibold text-text-primary mb-4\">Test Search APIs</h2>\n          \n          <div className=\"mb-4\">\n            <label className=\"block text-sm font-medium text-text-primary mb-2\">\n              Search Query:\n            </label>\n            <input\n              type=\"text\"\n              value={searchQuery}\n              onChange={(e) => setSearchQuery(e.target.value)}\n              className=\"w-full px-3 py-2 border border-border-primary rounded-lg bg-surface-secondary text-text-primary\"\n              placeholder=\"Enter search query...\"\n            />\n          </div>\n          \n          <div className=\"flex space-x-4 mb-6\">\n            <button\n              onClick={testSearchAPI}\n              disabled={loading}\n              className=\"px-4 py-2 bg-accent-primary text-text-on-accent rounded-lg hover:bg-accent-secondary disabled:opacity-50\"\n            >\n              Test Search API\n            </button>\n            <button\n              onClick={testCompaniesAPI}\n              disabled={loading}\n              className=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50\"\n            >\n              Test Companies API\n            </button>\n          </div>\n          \n          {loading && (\n            <div className=\"text-text-secondary\">Loading...</div>\n          )}\n          \n          {error && (\n            <div className=\"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4\">\n              <strong>Error:</strong> {error}\n            </div>\n          )}\n          \n          {results && (\n            <div className=\"bg-surface-secondary border border-border-primary rounded-lg p-4\">\n              <h3 className=\"text-lg font-semibold text-text-primary mb-2\">Results:</h3>\n              <pre className=\"text-sm text-text-secondary overflow-auto max-h-96\">\n                {JSON.stringify(results, null, 2)}\n              </pre>\n            </div>\n          )}\n        </div>\n        \n        <div className=\"bg-surface-primary border border-border-primary rounded-lg p-6\">\n          <h2 className=\"text-xl font-semibold text-text-primary mb-4\">API Endpoints</h2>\n          <ul className=\"space-y-2 text-text-secondary\">\n            <li><strong>Search API:</strong> GET /api/search/companies?q=tech</li>\n            <li><strong>Companies API:</strong> GET /api/companies?q=tech&limit=5</li>\n          </ul>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AAIe,SAAS;;IACtB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAO;IAC5C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,MAAM,gBAAgB;QACpB,WAAW;QACX,SAAS;QACT,WAAW;QAEX,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,wBAAwB,EAAE,mBAAmB,cAAc;YACzF,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,KAAK,EAAE,SAAS,MAAM,CAAC,EAAE,EAAE,KAAK,KAAK,IAAI,iBAAiB;YAC7E;YAEA,WAAW;QACb,EAAE,OAAO,KAAK;YACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;QAChD,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,mBAAmB;QACvB,WAAW;QACX,SAAS;QACT,WAAW;QAEX,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,iBAAiB,EAAE,mBAAmB,aAAa,QAAQ,CAAC;YAC1F,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,KAAK,EAAE,SAAS,MAAM,CAAC,EAAE,EAAE,KAAK,KAAK,IAAI,iBAAiB;YAC7E;YAEA,WAAW;QACb,EAAE,OAAO,KAAK;YACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;QAChD,SAAU;YACR,WAAW;QACb;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAG,WAAU;8BAA4C;;;;;;8BAE1D,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAA+C;;;;;;sCAE7D,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAM,WAAU;8CAAmD;;;;;;8CAGpE,6LAAC;oCACC,MAAK;oCACL,OAAO;oCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;oCAC9C,WAAU;oCACV,aAAY;;;;;;;;;;;;sCAIhB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,SAAS;oCACT,UAAU;oCACV,WAAU;8CACX;;;;;;8CAGD,6LAAC;oCACC,SAAS;oCACT,UAAU;oCACV,WAAU;8CACX;;;;;;;;;;;;wBAKF,yBACC,6LAAC;4BAAI,WAAU;sCAAsB;;;;;;wBAGtC,uBACC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;8CAAO;;;;;;gCAAe;gCAAE;;;;;;;wBAI5B,yBACC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAA+C;;;;;;8CAC7D,6LAAC;oCAAI,WAAU;8CACZ,KAAK,SAAS,CAAC,SAAS,MAAM;;;;;;;;;;;;;;;;;;8BAMvC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAA+C;;;;;;sCAC7D,6LAAC;4BAAG,WAAU;;8CACZ,6LAAC;;sDAAG,6LAAC;sDAAO;;;;;;wCAAoB;;;;;;;8CAChC,6LAAC;;sDAAG,6LAAC;sDAAO;;;;;;wCAAuB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM/C;GApHwB;KAAA", "debugId": null}}]}
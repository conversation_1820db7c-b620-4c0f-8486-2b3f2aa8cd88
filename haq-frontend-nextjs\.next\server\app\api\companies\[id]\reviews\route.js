(()=>{var e={};e.id=389,e.ids=[389],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32325:e=>{"use strict";e.exports=require("jsdom")},34631:e=>{"use strict";e.exports=require("tls")},37019:(e,s,r)=>{"use strict";r.r(s),r.d(s,{patchFetch:()=>f,routeModule:()=>g,serverHooks:()=>h,workAsyncStorage:()=>v,workUnitAsyncStorage:()=>w});var t={};r.r(t),r.d(t,{DELETE:()=>m,GET:()=>d,PATCH:()=>x,POST:()=>p,PUT:()=>l});var a=r(96559),o=r(48088),n=r(37719),i=r(32190),c=r(56621),u=r(77882);async function d(e,{params:s}){try{let r=s.id,{searchParams:t}=new URL(e.url);if(!u.ne.isValidUUID(r))return i.NextResponse.json({success:!1,message:"Invalid company ID format",error:"Company ID must be a valid UUID"},{status:400});let a=Math.max(1,parseInt(t.get("page")||"1")),o=Math.min(50,Math.max(1,parseInt(t.get("limit")||"10"))),n=t.get("sort")||"newest",d=["newest","oldest","highest_rated","lowest_rated"];if(!d.includes(n))return i.NextResponse.json({success:!1,message:"Invalid sort parameter",error:`Sort must be one of: ${d.join(", ")}`},{status:400});let p=(a-1)*o,{data:l,error:m}=await c.ND.from("companies").select("company_id, name").eq("company_id",r).single();if(m||!l)return i.NextResponse.json({success:!1,message:"Company not found",error:"The requested company does not exist"},{status:404});let x=c.ND.from("reviews").select(`
        review_id,
        overall_rating,
        pros,
        cons,
        advice_management,
        created_at
      `).eq("company_id",r).eq("status","approved");switch(n){case"newest":x=x.order("created_at",{ascending:!1});break;case"oldest":x=x.order("created_at",{ascending:!0});break;case"highest_rated":x=x.order("overall_rating",{ascending:!1}).order("created_at",{ascending:!1});break;case"lowest_rated":x=x.order("overall_rating",{ascending:!0}).order("created_at",{ascending:!1})}x=x.range(p,p+o-1);let{data:g,error:v}=await x;if(v)return console.error("Error fetching reviews:",v),i.NextResponse.json({success:!1,message:"Failed to fetch reviews",error:"Database query failed"},{status:500});let{count:w,error:h}=await c.ND.from("reviews").select("*",{count:"exact",head:!0}).eq("company_id",r).eq("status","approved");if(h)return console.error("Error counting reviews:",h),i.NextResponse.json({success:!1,message:"Failed to count reviews",error:"Database query failed"},{status:500});let f=(g||[]).map(e=>({review_id:e.review_id,overall_rating:e.overall_rating,pros:e.pros,cons:e.cons,advice_management:e.advice_management,created_at:e.created_at}));return i.NextResponse.json({success:!0,data:{company:{company_id:l.company_id,name:l.name},reviews:f,pagination:{page:a,limit:o,total:w||0,totalPages:Math.ceil((w||0)/o),hasNext:a*o<(w||0),hasPrev:a>1},sort:n,filters:{status:"approved"}}},{headers:{"Cache-Control":"public, max-age=300, s-maxage=300",Vary:"Accept-Encoding","X-Content-Type-Options":"nosniff","X-Frame-Options":"DENY"}})}catch(e){return console.error("Company reviews API error:",e),i.NextResponse.json({success:!1,message:"Internal server error",error:"Unexpected error occurred"},{status:500})}}async function p(){return i.NextResponse.json({success:!1,message:"Method not allowed"},{status:405,headers:{Allow:"GET"}})}async function l(){return i.NextResponse.json({success:!1,message:"Method not allowed"},{status:405,headers:{Allow:"GET"}})}async function m(){return i.NextResponse.json({success:!1,message:"Method not allowed"},{status:405,headers:{Allow:"GET"}})}async function x(){return i.NextResponse.json({success:!1,message:"Method not allowed"},{status:405,headers:{Allow:"GET"}})}let g=new a.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/companies/[id]/reviews/route",pathname:"/api/companies/[id]/reviews",filename:"route",bundlePath:"app/api/companies/[id]/reviews/route"},resolvedPagePath:"D:\\Haq website v1\\haq-frontend-nextjs\\src\\app\\api\\companies\\[id]\\reviews\\route.ts",nextConfigOutput:"",userland:t}),{workAsyncStorage:v,workUnitAsyncStorage:w,serverHooks:h}=g;function f(){return(0,n.patchFetch)({workAsyncStorage:v,workUnitAsyncStorage:w})}},39727:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},51906:e=>{function s(e){var s=Error("Cannot find module '"+e+"'");throw s.code="MODULE_NOT_FOUND",s}s.keys=()=>[],s.resolve=s,s.id=51906,e.exports=s},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56621:(e,s,r)=>{"use strict";r.d(s,{ND:()=>o});var t=r(39398);r(98766);let a=null,o=a=(0,t.createClient)("https://wqbuilazpyxpwyuwuqpi.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6IndxYnVpbGF6cHl4cHd5dXd1cXBpIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTA1NTYyNDMsImV4cCI6MjA2NjEzMjI0M30.GeRI54Rskwdbfm9_lRxy1-7YQ8vA74JWblNF2GRpzrI")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},77882:(e,s,r)=>{"use strict";let t;r.d(s,{ne:()=>n});var a=r(39296);{let{JSDOM:e}=r(32325),s=new e("").window;t=(0,a.A)(s)}let o={BASIC_TEXT:{ALLOWED_TAGS:[],ALLOWED_ATTR:[],KEEP_CONTENT:!0,RETURN_DOM:!1}},n={isValidUUID:e=>/^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(e),isValidRating:e=>Number.isInteger(e)&&e>=1&&e<=5,isValidTextLength:(e,s=5e3)=>"string"==typeof e&&e.length<=s}},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var s=require("../../../../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[447,580,398,766,296],()=>r(37019));module.exports=t})();
import React, { useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { Search, Menu, X, Shield, Users, DollarSign, MessageCircle } from 'lucide-react';

export const Header: React.FC = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const location = useLocation();

  const navigation = [
    { name: 'Companies', href: '/companies', icon: Shield },
    { name: 'Salaries', href: '/salaries', icon: DollarSign },
    { name: 'Community', href: '/community', icon: MessageCircle },
  ];

  return (
    <header className="bg-background-primary border-b border-border-primary sticky top-0 z-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <Link to="/" className="flex items-center space-x-2 group">
            <div className="w-8 h-8 bg-gradient-to-br from-accent-primary to-accent-secondary rounded-lg flex items-center justify-center group-hover:scale-105 transition-transform duration-200">
              <Shield className="w-5 h-5 text-text-on-accent" />
            </div>
            <div className="flex flex-col">
              <span className="text-xl font-bold text-accent-primary">Haq</span>
              <span className="text-xs text-text-secondary -mt-1">حق</span>
            </div>
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-8">
            {navigation.map((item) => {
              const Icon = item.icon;
              const isActive = location.pathname === item.href;
              return (
                <Link
                  key={item.name}
                  to={item.href}
                  className={`flex items-center space-x-1 px-3 py-2 rounded-lg text-navigation-link font-medium transition-all duration-200 uppercase tracking-wide ${
                    isActive
                      ? 'text-accent-primary bg-surface-secondary'
                      : 'text-text-secondary hover:text-accent-primary hover:bg-surface-primary'
                  }`}
                >
                  <Icon className="w-4 h-4" />
                  <span>{item.name}</span>
                </Link>
              );
            })}
          </nav>

          {/* Action Buttons */}
          <div className="hidden md:flex items-center space-x-4">
            <Link
              to="/review/submit"
              className="bg-accent-primary hover:bg-accent-secondary text-text-on-accent px-4 py-2 rounded-lg text-sm font-semibold transition-all duration-200 flex items-center space-x-2 hover:shadow-glow transform hover:-translate-y-0.5"
            >
              <Users className="w-4 h-4" />
              <span>Write Review</span>
            </Link>
          </div>

          {/* Mobile menu button */}
          <button
            onClick={() => setIsMenuOpen(!isMenuOpen)}
            className="md:hidden p-2 rounded-lg text-text-secondary hover:text-accent-primary hover:bg-surface-primary transition-colors duration-200"
          >
            {isMenuOpen ? <X className="w-6 h-6" /> : <Menu className="w-6 h-6" />}
          </button>
        </div>
      </div>

      {/* Mobile Navigation */}
      {isMenuOpen && (
        <div className="md:hidden bg-surface-primary border-t border-border-primary">
          <div className="px-4 py-3 space-y-3">
            {navigation.map((item) => {
              const Icon = item.icon;
              const isActive = location.pathname === item.href;
              return (
                <Link
                  key={item.name}
                  to={item.href}
                  onClick={() => setIsMenuOpen(false)}
                  className={`flex items-center space-x-3 px-3 py-2 rounded-lg text-sm font-medium transition-colors duration-200 ${
                    isActive
                      ? 'text-accent-primary bg-surface-secondary'
                      : 'text-text-secondary hover:text-accent-primary hover:bg-surface-secondary'
                  }`}
                >
                  <Icon className="w-5 h-5" />
                  <span>{item.name}</span>
                </Link>
              );
            })}
            <Link
              to="/review/submit"
              onClick={() => setIsMenuOpen(false)}
              className="flex items-center space-x-3 bg-accent-primary hover:bg-accent-secondary text-text-on-accent px-3 py-2 rounded-lg text-sm font-medium transition-colors duration-200"
            >
              <Users className="w-5 h-5" />
              <span>Write Review</span>
            </Link>
          </div>
        </div>
      )}
    </header>
  );
};
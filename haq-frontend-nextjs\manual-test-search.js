// Manual test for search functionality
// Run this with: node manual-test-search.js

const https = require('https');

function testAPI(url, description) {
  return new Promise((resolve, reject) => {
    console.log(`\n🧪 Testing: ${description}`);
    console.log(`URL: ${url}`);
    
    https.get(url, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          const jsonData = JSON.parse(data);
          console.log(`✅ Status: ${res.statusCode}`);
          console.log(`✅ Response:`, JSON.stringify(jsonData, null, 2));
          resolve(jsonData);
        } catch (error) {
          console.log(`❌ JSON Parse Error:`, error.message);
          console.log(`Raw response:`, data);
          reject(error);
        }
      });
    }).on('error', (error) => {
      console.log(`❌ Request Error:`, error.message);
      reject(error);
    });
  });
}

async function runTests() {
  console.log('🚀 Starting Search API Tests...\n');
  
  try {
    // Test 1: Companies API
    await testAPI(
      'http://localhost:3000/api/companies?q=tech&limit=3',
      'Companies API with search query'
    );
    
    // Test 2: Search API
    await testAPI(
      'http://localhost:3000/api/search/companies?q=tech',
      'Search API with tech query'
    );
    
    // Test 3: Search API with pagination
    await testAPI(
      'http://localhost:3000/api/search/companies?q=tech&page=1&limit=2',
      'Search API with pagination'
    );
    
    console.log('\n🎉 All tests completed!');
    
  } catch (error) {
    console.error('\n❌ Test failed:', error.message);
  }
}

runTests();

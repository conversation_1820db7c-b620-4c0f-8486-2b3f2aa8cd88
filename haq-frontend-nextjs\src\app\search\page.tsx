'use client'

import React, { useState, useEffect } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import Link from 'next/link';
import useSWR from 'swr';
import { Search, ArrowLeft, Building2, MapPin, Star, Users, AlertCircle } from 'lucide-react';
import { SearchBar } from '@/components/common/SearchBar';

// Fetcher function for SWR
const fetcher = (url: string) => fetch(url).then((res) => res.json());

interface Company {
  company_id: string;
  name: string;
  industry: string;
  location: string;
  website_url?: string;
  logo_url?: string;
  employee_count_range?: string;
  haq_score?: number;
  total_reviews?: number;
  created_at: string;
}

interface SearchResponse {
  companies: Company[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

function SearchResultCard({ company }: { company: Company }) {
  return (
    <Link 
      href={`/companies/${company.company_id}`}
      className="block bg-surface-primary border border-border-primary rounded-medium p-6 hover:border-accent-primary hover:shadow-glow transition-all duration-200 group"
    >
      <div className="flex items-start space-x-4">
        {/* Company Logo */}
        <div className="flex-shrink-0">
          {company.logo_url ? (
            <img
              src={company.logo_url}
              alt={`${company.name} logo`}
              className="w-12 h-12 rounded-lg object-cover"
            />
          ) : (
            <div className="w-12 h-12 bg-surface-secondary rounded-lg flex items-center justify-center">
              <Building2 className="w-6 h-6 text-text-secondary" />
            </div>
          )}
        </div>

        {/* Company Info */}
        <div className="flex-1 min-w-0">
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <h3 className="text-lg font-semibold text-text-primary group-hover:text-accent-primary transition-colors duration-200 truncate">
                {company.name}
              </h3>
              <div className="flex items-center space-x-4 mt-1 text-sm text-text-secondary">
                <span className="flex items-center space-x-1">
                  <Building2 className="w-4 h-4" />
                  <span>{company.industry}</span>
                </span>
                <span className="flex items-center space-x-1">
                  <MapPin className="w-4 h-4" />
                  <span>{company.location}</span>
                </span>
              </div>
            </div>

            {/* HAQ Score & Reviews */}
            <div className="flex flex-col items-end space-y-1">
              {company.haq_score && (
                <div className="flex items-center space-x-1">
                  <Star className="w-4 h-4 text-yellow-500 fill-current" />
                  <span className="text-sm font-medium text-text-primary">
                    {company.haq_score.toFixed(1)}
                  </span>
                </div>
              )}
              {company.total_reviews && (
                <div className="flex items-center space-x-1 text-xs text-text-secondary">
                  <Users className="w-3 h-3" />
                  <span>{company.total_reviews} reviews</span>
                </div>
              )}
            </div>
          </div>

          {/* Employee Count */}
          {company.employee_count_range && (
            <p className="mt-3 text-sm text-text-secondary">
              {company.employee_count_range} employees
            </p>
          )}
        </div>
      </div>
    </Link>
  );
}

function LoadingSkeleton() {
  return (
    <div className="space-y-6">
      {Array.from({ length: 5 }).map((_, index) => (
        <div key={index} className="bg-surface-primary border border-border-primary rounded-medium p-6 animate-pulse">
          <div className="flex items-start space-x-4">
            <div className="w-12 h-12 bg-surface-secondary rounded-lg"></div>
            <div className="flex-1">
              <div className="h-5 bg-surface-secondary rounded w-1/3 mb-2"></div>
              <div className="h-4 bg-surface-secondary rounded w-1/2 mb-3"></div>
              <div className="h-3 bg-surface-secondary rounded w-full"></div>
              <div className="h-3 bg-surface-secondary rounded w-2/3 mt-1"></div>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
}

export default function SearchPage() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const [searchQuery, setSearchQuery] = useState('');
  const [mounted, setMounted] = useState(false);

  // Get search query from URL
  const urlQuery = searchParams.get('q') || '';

  // Update local search state when URL changes
  useEffect(() => {
    setSearchQuery(urlQuery);
    setMounted(true);
  }, [urlQuery]);

  // Build API URL for search
  const searchApiUrl = mounted && urlQuery ? `/api/search/companies?q=${encodeURIComponent(urlQuery)}` : null;

  // Fetch search results
  const { data, error, isLoading } = useSWR<SearchResponse>(searchApiUrl, fetcher);

  // Handle search form submission
  const handleSearch = () => {
    if (searchQuery.trim()) {
      const params = new URLSearchParams();
      params.set('q', searchQuery.trim());
      router.push(`/search?${params.toString()}`);
    }
  };

  if (!mounted) {
    return <div className="min-h-screen bg-background-primary"></div>;
  }

  return (
    <div className="min-h-screen bg-background-primary">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center space-x-4 mb-6">
            <Link
              href="/"
              className="flex items-center space-x-2 text-text-secondary hover:text-accent-primary transition-colors duration-200"
            >
              <ArrowLeft className="w-5 h-5" />
              <span>Back to Home</span>
            </Link>
          </div>

          <h1 className="text-2xl md:text-3xl font-bold text-text-primary mb-4">
            Search Companies
          </h1>

          {/* Search Bar */}
          <div className="max-w-2xl">
            <SearchBar
              value={searchQuery}
              onChange={setSearchQuery}
              placeholder="Search companies by name..."
              onSubmit={handleSearch}
            />
          </div>
        </div>

        {/* Search Results */}
        <div className="space-y-6">
          {/* Results Header */}
          {urlQuery && (
            <div className="flex items-center justify-between">
              <div>
                <h2 className="text-lg font-semibold text-text-primary">
                  Search Results for "{urlQuery}"
                </h2>
                {data && (
                  <p className="text-sm text-text-secondary mt-1">
                    {data.pagination.total} companies found
                  </p>
                )}
              </div>
            </div>
          )}

          {/* Loading State */}
          {isLoading && <LoadingSkeleton />}

          {/* Error State */}
          {error && (
            <div className="text-center py-12">
              <AlertCircle className="w-12 h-12 text-red-500 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-text-primary mb-2">
                Search Error
              </h3>
              <p className="text-text-secondary">
                Failed to search companies. Please try again later.
              </p>
            </div>
          )}

          {/* No Results */}
          {data && data.companies.length === 0 && !isLoading && (
            <div className="text-center py-12">
              <Search className="w-12 h-12 text-text-secondary mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-text-primary mb-2">
                No Companies Found
              </h3>
              <p className="text-text-secondary mb-4">
                No companies match your search for "{urlQuery}". Try different keywords or browse all companies.
              </p>
              <Link
                href="/companies"
                className="inline-flex items-center space-x-2 bg-accent-primary hover:bg-accent-secondary text-text-on-accent px-4 py-2 rounded-lg font-medium transition-colors duration-200"
              >
                <Building2 className="w-4 h-4" />
                <span>Browse All Companies</span>
              </Link>
            </div>
          )}

          {/* Search Results */}
          {data && data.companies.length > 0 && (
            <div className="space-y-4">
              {data.companies.map((company) => (
                <SearchResultCard key={company.company_id} company={company} />
              ))}
            </div>
          )}

          {/* Pagination */}
          {data && data.pagination.totalPages > 1 && (
            <div className="flex justify-center mt-8">
              <div className="flex items-center space-x-2">
                {data.pagination.hasPrev && (
                  <button
                    onClick={() => {
                      const params = new URLSearchParams();
                      params.set('q', urlQuery);
                      params.set('page', (data.pagination.page - 1).toString());
                      router.push(`/search?${params.toString()}`);
                    }}
                    className="px-3 py-2 text-sm bg-surface-primary border border-border-primary rounded-lg hover:border-accent-primary transition-colors duration-200"
                  >
                    Previous
                  </button>
                )}
                
                <span className="px-3 py-2 text-sm text-text-secondary">
                  Page {data.pagination.page} of {data.pagination.totalPages}
                </span>
                
                {data.pagination.hasNext && (
                  <button
                    onClick={() => {
                      const params = new URLSearchParams();
                      params.set('q', urlQuery);
                      params.set('page', (data.pagination.page + 1).toString());
                      router.push(`/search?${params.toString()}`);
                    }}
                    className="px-3 py-2 text-sm bg-surface-primary border border-border-primary rounded-lg hover:border-accent-primary transition-colors duration-200"
                  >
                    Next
                  </button>
                )}
              </div>
            </div>
          )}

          {/* No Query State */}
          {!urlQuery && (
            <div className="text-center py-12">
              <Search className="w-12 h-12 text-text-secondary mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-text-primary mb-2">
                Start Your Search
              </h3>
              <p className="text-text-secondary mb-4">
                Enter a company name in the search bar above to find companies and their reviews.
              </p>
              <Link
                href="/companies"
                className="inline-flex items-center space-x-2 bg-accent-primary hover:bg-accent-secondary text-text-on-accent px-4 py-2 rounded-lg font-medium transition-colors duration-200"
              >
                <Building2 className="w-4 h-4" />
                <span>Browse All Companies</span>
              </Link>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

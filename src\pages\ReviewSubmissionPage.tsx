import React, { useState } from 'react';
import { Shield, Star, AlertTriangle, CheckCircle, ArrowLeft } from 'lucide-react';
import { Link } from 'react-router-dom';

interface ReviewForm {
  companyName: string;
  jobTitle: string;
  employmentStatus: 'current' | 'former';
  overallRating: number;
  pros: string;
  cons: string;
  advice: string;
  workLifeBalance: number;
  compensation: number;
  careerOpportunities: number;
  management: number;
  culture: number;
  salary: string;
  benefits: string[];
  redFlags: string[];
  city: string;
}

const initialForm: ReviewForm = {
  companyName: '',
  jobTitle: '',
  employmentStatus: 'current',
  overallRating: 0,
  pros: '',
  cons: '',
  advice: '',
  workLifeBalance: 0,
  compensation: 0,
  careerOpportunities: 0,
  management: 0,
  culture: 0,
  salary: '',
  benefits: [],
  redFlags: [],
  city: ''
};

const benefitOptions = [
  'Health Insurance',
  'Provident Fund',
  'Paid Time Off',
  'Flexible Hours',
  'Remote Work',
  'Training Budget',
  'Performance Bonus',
  'Transportation',
  'Meal Allowance',
  'Life Insurance'
];

const redFlagOptions = [
  'Unpaid Salaries',
  'Wrongful Termination',
  'Provident Fund Issues',
  'Harassment',
  'Toxic Work Culture',
  'Unpaid Overtime',
  'Poor Management',
  'No Growth Opportunities',
  'Discrimination',
  'Unsafe Working Conditions'
];

const cities = ['Karachi', 'Lahore', 'Islamabad', 'Rawalpindi', 'Faisalabad', 'Multan', 'Peshawar', 'Quetta'];

export const ReviewSubmissionPage: React.FC = () => {
  const [form, setForm] = useState<ReviewForm>(initialForm);
  const [currentStep, setCurrentStep] = useState(1);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const updateForm = (field: keyof ReviewForm, value: any) => {
    setForm(prev => ({ ...prev, [field]: value }));
  };

  const updateRating = (field: keyof ReviewForm, rating: number) => {
    updateForm(field, rating);
  };

  const toggleBenefit = (benefit: string) => {
    const currentBenefits = form.benefits;
    if (currentBenefits.includes(benefit)) {
      updateForm('benefits', currentBenefits.filter(b => b !== benefit));
    } else {
      updateForm('benefits', [...currentBenefits, benefit]);
    }
  };

  const toggleRedFlag = (flag: string) => {
    const currentFlags = form.redFlags;
    if (currentFlags.includes(flag)) {
      updateForm('redFlags', currentFlags.filter(f => f !== flag));
    } else {
      updateForm('redFlags', [...currentFlags, flag]);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    alert('Review submitted successfully! Thank you for helping build a more transparent workplace culture.');
    setForm(initialForm);
    setCurrentStep(1);
    setIsSubmitting(false);
  };

  const renderStars = (rating: number, onRate: (rating: number) => void) => {
    return (
      <div className="flex space-x-1">
        {[1, 2, 3, 4, 5].map((star) => (
          <button
            key={star}
            type="button"
            onClick={() => onRate(star)}
            className="focus:outline-none"
          >
            <Star
              className={`w-6 h-6 transition-colors duration-200 ${
                star <= rating ? 'text-yellow-400 fill-current' : 'text-gray-300 hover:text-yellow-200'
              }`}
            />
          </button>
        ))}
      </div>
    );
  };

  const canProceedToStep2 = form.companyName && form.jobTitle && form.overallRating > 0;
  const canProceedToStep3 = form.pros && form.cons && form.advice;

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <Link
            to="/"
            className="inline-flex items-center space-x-2 text-gray-600 hover:text-gray-900 transition-colors duration-200 mb-4"
          >
            <ArrowLeft className="w-4 h-4" />
            <span>Back to Home</span>
          </Link>
          
          <div className="flex items-center space-x-3 mb-6">
            <div className="w-12 h-12 bg-primary-100 rounded-full flex items-center justify-center">
              <Shield className="w-6 h-6 text-primary-600" />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Write Anonymous Review</h1>
              <p className="text-gray-600">Your identity is completely protected</p>
            </div>
          </div>

          {/* Progress Bar */}
          <div className="flex items-center space-x-4">
            {[1, 2, 3].map((step) => (
              <div key={step} className="flex items-center space-x-2">
                <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                  step <= currentStep ? 'bg-primary-600 text-white' : 'bg-gray-200 text-gray-500'
                }`}>
                  {step}
                </div>
                <span className={`text-sm ${step <= currentStep ? 'text-primary-600' : 'text-gray-500'}`}>
                  {step === 1 ? 'Basic Info' : step === 2 ? 'Experience' : 'Details'}
                </span>
                {step < 3 && <div className="w-8 h-0.5 bg-gray-200" />}
              </div>
            ))}
          </div>
        </div>
      </div>

      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <form onSubmit={handleSubmit} className="space-y-8">
          {/* Step 1: Basic Information */}
          {currentStep === 1 && (
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-6">Company & Role Information</h2>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Company Name *
                  </label>
                  <input
                    type="text"
                    required
                    value={form.companyName}
                    onChange={(e) => updateForm('companyName', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                    placeholder="Enter company name"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Job Title *
                  </label>
                  <input
                    type="text"
                    required
                    value={form.jobTitle}
                    onChange={(e) => updateForm('jobTitle', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                    placeholder="e.g., Software Engineer"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Employment Status *
                  </label>
                  <select
                    value={form.employmentStatus}
                    onChange={(e) => updateForm('employmentStatus', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                  >
                    <option value="current">Current Employee</option>
                    <option value="former">Former Employee</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    City
                  </label>
                  <select
                    value={form.city}
                    onChange={(e) => updateForm('city', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                  >
                    <option value="">Select city</option>
                    {cities.map(city => (
                      <option key={city} value={city}>{city}</option>
                    ))}
                  </select>
                </div>
              </div>

              <div className="mt-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Overall Rating *
                </label>
                {renderStars(form.overallRating, (rating) => updateRating('overallRating', rating))}
              </div>

              <div className="mt-8 flex justify-end">
                <button
                  type="button"
                  onClick={() => setCurrentStep(2)}
                  disabled={!canProceedToStep2}
                  className="bg-primary-600 hover:bg-primary-700 disabled:bg-gray-300 disabled:cursor-not-allowed text-white px-6 py-2 rounded-lg font-medium transition-colors duration-200"
                >
                  Next Step
                </button>
              </div>
            </div>
          )}

          {/* Step 2: Experience Details */}
          {currentStep === 2 && (
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-6">Your Experience</h2>
              
              <div className="space-y-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    What are the pros of working here? *
                  </label>
                  <textarea
                    required
                    value={form.pros}
                    onChange={(e) => updateForm('pros', e.target.value)}
                    rows={4}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                    placeholder="Share the positive aspects of working at this company..."
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    What are the cons of working here? *
                  </label>
                  <textarea
                    required
                    value={form.cons}
                    onChange={(e) => updateForm('cons', e.target.value)}
                    rows={4}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                    placeholder="Share the challenges or negative aspects..."
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Advice to Management *
                  </label>
                  <textarea
                    required
                    value={form.advice}
                    onChange={(e) => updateForm('advice', e.target.value)}
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                    placeholder="What suggestions do you have for the management?"
                  />
                </div>

                {/* Detailed Ratings */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Work-Life Balance</label>
                    {renderStars(form.workLifeBalance, (rating) => updateRating('workLifeBalance', rating))}
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Compensation</label>
                    {renderStars(form.compensation, (rating) => updateRating('compensation', rating))}
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Career Opportunities</label>
                    {renderStars(form.careerOpportunities, (rating) => updateRating('careerOpportunities', rating))}
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Management</label>
                    {renderStars(form.management, (rating) => updateRating('management', rating))}
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Culture & Values</label>
                    {renderStars(form.culture, (rating) => updateRating('culture', rating))}
                  </div>
                </div>
              </div>

              <div className="mt-8 flex justify-between">
                <button
                  type="button"
                  onClick={() => setCurrentStep(1)}
                  className="border border-gray-300 hover:bg-gray-50 text-gray-700 px-6 py-2 rounded-lg font-medium transition-colors duration-200"
                >
                  Previous
                </button>
                <button
                  type="button"
                  onClick={() => setCurrentStep(3)}
                  disabled={!canProceedToStep3}
                  className="bg-primary-600 hover:bg-primary-700 disabled:bg-gray-300 disabled:cursor-not-allowed text-white px-6 py-2 rounded-lg font-medium transition-colors duration-200"
                >
                  Next Step
                </button>
              </div>
            </div>
          )}

          {/* Step 3: Additional Details */}
          {currentStep === 3 && (
            <div className="space-y-6">
              {/* Salary Information */}
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h2 className="text-xl font-semibold text-gray-900 mb-4">Salary & Benefits</h2>
                
                <div className="mb-6">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Monthly Salary (Optional)
                  </label>
                  <input
                    type="text"
                    value={form.salary}
                    onChange={(e) => updateForm('salary', e.target.value)}
                    className="w-full md:w-1/2 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                    placeholder="e.g., 80,000 PKR"
                  />
                  <p className="text-xs text-gray-500 mt-1">This helps other employees understand salary ranges</p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-3">
                    Benefits Offered (Select all that apply)
                  </label>
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                    {benefitOptions.map((benefit) => (
                      <label key={benefit} className="flex items-center space-x-2 cursor-pointer">
                        <input
                          type="checkbox"
                          checked={form.benefits.includes(benefit)}
                          onChange={() => toggleBenefit(benefit)}
                          className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                        />
                        <span className="text-sm text-gray-700">{benefit}</span>
                      </label>
                    ))}
                  </div>
                </div>
              </div>

              {/* Red Flags */}
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div className="flex items-center space-x-2 mb-4">
                  <AlertTriangle className="w-5 h-5 text-red-500" />
                  <h2 className="text-xl font-semibold text-gray-900">Report Issues</h2>
                </div>
                <p className="text-sm text-gray-600 mb-4">
                  Help other employees by flagging any serious workplace issues you've experienced.
                </p>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                  {redFlagOptions.map((flag) => (
                    <label key={flag} className="flex items-center space-x-2 cursor-pointer">
                      <input
                        type="checkbox"
                        checked={form.redFlags.includes(flag)}
                        onChange={() => toggleRedFlag(flag)}
                        className="rounded border-gray-300 text-red-600 focus:ring-red-500"
                      />
                      <span className="text-sm text-gray-700">{flag}</span>
                    </label>
                  ))}
                </div>
              </div>

              {/* Privacy Notice */}
              <div className="bg-primary-50 border border-primary-200 rounded-lg p-6">
                <div className="flex items-start space-x-3">
                  <Shield className="w-6 h-6 text-primary-600 mt-0.5" />
                  <div>
                    <h3 className="font-medium text-primary-900 mb-2">Your Privacy is Protected</h3>
                    <ul className="text-sm text-primary-800 space-y-1">
                      <li>• Your review will be completely anonymous</li>
                      <li>• We never store personally identifiable information</li>
                      <li>• All submissions are encrypted and moderated</li>
                      <li>• You cannot be traced back through this review</li>
                    </ul>
                  </div>
                </div>
              </div>

              <div className="flex justify-between">
                <button
                  type="button"
                  onClick={() => setCurrentStep(2)}
                  className="border border-gray-300 hover:bg-gray-50 text-gray-700 px-6 py-2 rounded-lg font-medium transition-colors duration-200"
                >
                  Previous
                </button>
                <button
                  type="submit"
                  disabled={isSubmitting}
                  className="bg-primary-600 hover:bg-primary-700 disabled:bg-primary-400 text-white px-8 py-2 rounded-lg font-medium transition-colors duration-200 flex items-center space-x-2"
                >
                  {isSubmitting ? (
                    <>
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                      <span>Submitting...</span>
                    </>
                  ) : (
                    <>
                      <CheckCircle className="w-5 h-5" />
                      <span>Submit Review</span>
                    </>
                  )}
                </button>
              </div>
            </div>
          )}
        </form>
      </div>
    </div>
  );
};
"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[683],{2436:(e,t,r)=>{var n=r(2115),i="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},a=n.useState,o=n.useEffect,u=n.useLayoutEffect,l=n.useDebugValue;function s(e){var t=e.getSnapshot;e=e.value;try{var r=t();return!i(e,r)}catch(e){return!0}}var c="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,t){return t()}:function(e,t){var r=t(),n=a({inst:{value:r,getSnapshot:t}}),i=n[0].inst,c=n[1];return u(function(){i.value=r,i.getSnapshot=t,s(i)&&c({inst:i})},[e,r,t]),o(function(){return s(i)&&c({inst:i}),e(function(){s(i)&&c({inst:i})})},[e]),l(r),r};t.useSyncExternalStore=void 0!==n.useSyncExternalStore?n.useSyncExternalStore:c},6072:(e,t,r)=>{let n;r.d(t,{BE:()=>ei,Ay:()=>ea});var i=r(2115),a=r(9033),o=Object.prototype.hasOwnProperty;let u=new WeakMap,l=()=>{},s=l(),c=Object,d=e=>e===s,f=e=>"function"==typeof e,g=(e,t)=>({...e,...t}),h=e=>f(e.then),p={},w={},y="undefined",v=typeof window!=y,m=typeof document!=y,b=v&&"Deno"in window,E=()=>v&&typeof window.requestAnimationFrame!=y,O=(e,t)=>{let r=u.get(e);return[()=>!d(t)&&e.get(t)||p,n=>{if(!d(t)){let i=e.get(t);t in w||(w[t]=i),r[5](t,g(i,n),i||p)}},r[6],()=>!d(t)&&t in w?w[t]:!d(t)&&e.get(t)||p]},k=!0,[S,_]=v&&window.addEventListener?[window.addEventListener.bind(window),window.removeEventListener.bind(window)]:[l,l],R={initFocus:e=>(m&&document.addEventListener("visibilitychange",e),S("focus",e),()=>{m&&document.removeEventListener("visibilitychange",e),_("focus",e)}),initReconnect:e=>{let t=()=>{k=!0,e()},r=()=>{k=!1};return S("online",t),S("offline",r),()=>{_("online",t),_("offline",r)}}},L=!i.useId,C=!v||b,T=e=>E()?window.requestAnimationFrame(e):setTimeout(e,1),A=C?i.useEffect:i.useLayoutEffect,j="undefined"!=typeof navigator&&navigator.connection,V=!C&&j&&(["slow-2g","2g"].includes(j.effectiveType)||j.saveData),x=new WeakMap,D=(e,t)=>c.prototype.toString.call(e)==="[object ".concat(t,"]"),M=0,P=e=>{let t,r,n=typeof e,i=D(e,"Date"),a=D(e,"RegExp"),o=D(e,"Object");if(c(e)!==e||i||a)t=i?e.toJSON():"symbol"==n?e.toString():"string"==n?JSON.stringify(e):""+e;else{if(t=x.get(e))return t;if(t=++M+"~",x.set(e,t),Array.isArray(e)){for(r=0,t="@";r<e.length;r++)t+=P(e[r])+",";x.set(e,t)}if(o){t="#";let n=c.keys(e).sort();for(;!d(r=n.pop());)d(e[r])||(t+=r+":"+P(e[r])+",");x.set(e,t)}}return t},W=e=>{if(f(e))try{e=e()}catch(t){e=""}let t=e;return[e="string"==typeof e?e:(Array.isArray(e)?e.length:e)?P(e):"",t]},F=0,I=()=>++F;async function N(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];let[n,i,a,o]=t,l=g({populateCache:!0,throwOnError:!0},"boolean"==typeof o?{revalidate:o}:o||{}),c=l.populateCache,p=l.rollbackOnError,w=l.optimisticData,y=e=>"function"==typeof p?p(e):!1!==p,v=l.throwOnError;if(f(i)){let e=[];for(let t of n.keys())!/^\$(inf|sub)\$/.test(t)&&i(n.get(t)._k)&&e.push(t);return Promise.all(e.map(m))}return m(i);async function m(e){let r,[i]=W(e);if(!i)return;let[o,g]=O(n,i),[p,m,b,E]=u.get(n),k=()=>{let t=p[i];return(f(l.revalidate)?l.revalidate(o().data,e):!1!==l.revalidate)&&(delete b[i],delete E[i],t&&t[0])?t[0](2).then(()=>o().data):o().data};if(t.length<3)return k();let S=a,_=I();m[i]=[_,0];let R=!d(w),L=o(),C=L.data,T=L._c,A=d(T)?C:T;if(R&&g({data:w=f(w)?w(A,C):w,_c:A}),f(S))try{S=S(A)}catch(e){r=e}if(S&&h(S)){if(S=await S.catch(e=>{r=e}),_!==m[i][0]){if(r)throw r;return S}r&&R&&y(r)&&(c=!0,g({data:A,_c:s}))}if(c&&!r&&(f(c)?g({data:c(S,A),error:s,_c:s}):g({data:S,error:s,_c:s})),m[i][1]=I(),Promise.resolve(k()).then(()=>{g({_c:s})}),r){if(v)throw r;return}return S}}let $=(e,t)=>{for(let r in e)e[r][0]&&e[r][0](t)},q=(e,t)=>{if(!u.has(e)){let r=g(R,t),n=Object.create(null),i=N.bind(s,e),a=l,o=Object.create(null),c=(e,t)=>{let r=o[e]||[];return o[e]=r,r.push(t),()=>r.splice(r.indexOf(t),1)},d=(t,r,n)=>{e.set(t,r);let i=o[t];if(i)for(let e of i)e(r,n)},f=()=>{if(!u.has(e)&&(u.set(e,[n,Object.create(null),Object.create(null),Object.create(null),i,d,c]),!C)){let t=r.initFocus(setTimeout.bind(s,$.bind(s,n,0))),i=r.initReconnect(setTimeout.bind(s,$.bind(s,n,1)));a=()=>{t&&t(),i&&i(),u.delete(e)}}};return f(),[e,i,f,a]}return[e,u.get(e)[4]]},[U,B]=q(new Map),J=g({onLoadingSlow:l,onSuccess:l,onError:l,onErrorRetry:(e,t,r,n,i)=>{let a=r.errorRetryCount,o=i.retryCount,u=~~((Math.random()+.5)*(1<<(o<8?o:8)))*r.errorRetryInterval;(d(a)||!(o>a))&&setTimeout(n,u,i)},onDiscarded:l,revalidateOnFocus:!0,revalidateOnReconnect:!0,revalidateIfStale:!0,shouldRetryOnError:!0,errorRetryInterval:V?1e4:5e3,focusThrottleInterval:5e3,dedupingInterval:2e3,loadingTimeout:V?5e3:3e3,compare:function e(t,r){var n,i;if(t===r)return!0;if(t&&r&&(n=t.constructor)===r.constructor){if(n===Date)return t.getTime()===r.getTime();if(n===RegExp)return t.toString()===r.toString();if(n===Array){if((i=t.length)===r.length)for(;i--&&e(t[i],r[i]););return -1===i}if(!n||"object"==typeof t){for(n in i=0,t)if(o.call(t,n)&&++i&&!o.call(r,n)||!(n in r)||!e(t[n],r[n]))return!1;return Object.keys(r).length===i}}return t!=t&&r!=r},isPaused:()=>!1,cache:U,mutate:B,fallback:{}},{isOnline:()=>k,isVisible:()=>{let e=m&&document.visibilityState;return d(e)||"hidden"!==e}}),Z=(e,t)=>{let r=g(e,t);if(t){let{use:n,fallback:i}=e,{use:a,fallback:o}=t;n&&a&&(r.use=n.concat(a)),i&&o&&(r.fallback=g(i,o))}return r},z=(0,i.createContext)({}),H=v&&window.__SWR_DEVTOOLS_USE__,G=H?window.__SWR_DEVTOOLS_USE__:[],K=e=>f(e[1])?[e[0],e[1],e[2]||{}]:[e[0],null,(null===e[1]?e[2]:e[1])||{}],Q=()=>g(J,(0,i.useContext)(z)),X=G.concat(e=>(t,r,n)=>{let i=r&&((...e)=>{let[n]=W(t),[,,,i]=u.get(U);if(n.startsWith("$inf$"))return r(...e);let a=i[n];return d(a)?r(...e):(delete i[n],a)});return e(t,i,n)}),Y=(e,t,r)=>{let n=t[e]||(t[e]=[]);return n.push(r),()=>{let e=n.indexOf(r);e>=0&&(n[e]=n[n.length-1],n.pop())}};H&&(window.__SWR_DEVTOOLS_REACT__=i);let ee=()=>{},et=ee();new WeakMap;let er=i.use||(e=>{switch(e.status){case"pending":throw e;case"fulfilled":return e.value;case"rejected":throw e.reason;default:throw e.status="pending",e.then(t=>{e.status="fulfilled",e.value=t},t=>{e.status="rejected",e.reason=t}),e}}),en={dedupe:!0},ei=c.defineProperty(e=>{let{value:t}=e,r=(0,i.useContext)(z),n=f(t),a=(0,i.useMemo)(()=>n?t(r):t,[n,r,t]),o=(0,i.useMemo)(()=>n?a:Z(r,a),[n,r,a]),u=a&&a.provider,l=(0,i.useRef)(s);u&&!l.current&&(l.current=q(u(o.cache||U),a));let c=l.current;return c&&(o.cache=c[0],o.mutate=c[1]),A(()=>{if(c)return c[2]&&c[2](),c[3]},[]),(0,i.createElement)(z.Provider,g(e,{value:o}))},"defaultValue",{value:J}),ea=(n=(e,t,r)=>{let{cache:n,compare:o,suspense:l,fallbackData:c,revalidateOnMount:p,revalidateIfStale:w,refreshInterval:y,refreshWhenHidden:v,refreshWhenOffline:m,keepPreviousData:b}=r,[E,k,S,_]=u.get(n),[R,j]=W(e),V=(0,i.useRef)(!1),x=(0,i.useRef)(!1),D=(0,i.useRef)(R),M=(0,i.useRef)(t),P=(0,i.useRef)(r),F=()=>P.current,$=()=>F().isVisible()&&F().isOnline(),[q,U,B,J]=O(n,R),Z=(0,i.useRef)({}).current,z=d(c)?d(r.fallback)?s:r.fallback[R]:c,H=(e,t)=>{for(let r in Z)if("data"===r){if(!o(e[r],t[r])&&(!d(e[r])||!o(eo,t[r])))return!1}else if(t[r]!==e[r])return!1;return!0},G=(0,i.useMemo)(()=>{let e=!!R&&!!t&&(d(p)?!F().isPaused()&&!l&&!1!==w:p),r=t=>{let r=g(t);return(delete r._k,e)?{isValidating:!0,isLoading:!0,...r}:r},n=q(),i=J(),a=r(n),o=n===i?a:r(i),u=a;return[()=>{let e=r(q());return H(e,u)?(u.data=e.data,u.isLoading=e.isLoading,u.isValidating=e.isValidating,u.error=e.error,u):(u=e,e)},()=>o]},[n,R]),K=(0,a.useSyncExternalStore)((0,i.useCallback)(e=>B(R,(t,r)=>{H(r,t)||e()}),[n,R]),G[0],G[1]),Q=!V.current,X=E[R]&&E[R].length>0,ee=K.data,et=d(ee)?z&&h(z)?er(z):z:ee,ei=K.error,ea=(0,i.useRef)(et),eo=b?d(ee)?d(ea.current)?et:ea.current:ee:et,eu=(!X||!!d(ei))&&(Q&&!d(p)?p:!F().isPaused()&&(l?!d(et)&&w:d(et)||w)),el=!!(R&&t&&Q&&eu),es=d(K.isValidating)?el:K.isValidating,ec=d(K.isLoading)?el:K.isLoading,ed=(0,i.useCallback)(async e=>{let t,n,i=M.current;if(!R||!i||x.current||F().isPaused())return!1;let a=!0,u=e||{},l=!S[R]||!u.dedupe,c=()=>L?!x.current&&R===D.current&&V.current:R===D.current,g={isValidating:!1,isLoading:!1},h=()=>{U(g)},p=()=>{let e=S[R];e&&e[1]===n&&delete S[R]},w={isValidating:!0};d(q().data)&&(w.isLoading=!0);try{if(l&&(U(w),r.loadingTimeout&&d(q().data)&&setTimeout(()=>{a&&c()&&F().onLoadingSlow(R,r)},r.loadingTimeout),S[R]=[i(j),I()]),[t,n]=S[R],t=await t,l&&setTimeout(p,r.dedupingInterval),!S[R]||S[R][1]!==n)return l&&c()&&F().onDiscarded(R),!1;g.error=s;let e=k[R];if(!d(e)&&(n<=e[0]||n<=e[1]||0===e[1]))return h(),l&&c()&&F().onDiscarded(R),!1;let u=q().data;g.data=o(u,t)?u:t,l&&c()&&F().onSuccess(t,R,r)}catch(r){p();let e=F(),{shouldRetryOnError:t}=e;!e.isPaused()&&(g.error=r,l&&c()&&(e.onError(r,R,e),(!0===t||f(t)&&t(r))&&(!F().revalidateOnFocus||!F().revalidateOnReconnect||$())&&e.onErrorRetry(r,R,e,e=>{let t=E[R];t&&t[0]&&t[0](3,e)},{retryCount:(u.retryCount||0)+1,dedupe:!0})))}return a=!1,h(),!0},[R,n]),ef=(0,i.useCallback)((...e)=>N(n,D.current,...e),[]);if(A(()=>{M.current=t,P.current=r,d(ee)||(ea.current=ee)}),A(()=>{if(!R)return;let e=ed.bind(s,en),t=0;F().revalidateOnFocus&&(t=Date.now()+F().focusThrottleInterval);let r=Y(R,E,(r,n={})=>{if(0==r){let r=Date.now();F().revalidateOnFocus&&r>t&&$()&&(t=r+F().focusThrottleInterval,e())}else if(1==r)F().revalidateOnReconnect&&$()&&e();else if(2==r)return ed();else if(3==r)return ed(n)});return x.current=!1,D.current=R,V.current=!0,U({_k:j}),eu&&(d(et)||C?e():T(e)),()=>{x.current=!0,r()}},[R]),A(()=>{let e;function t(){let t=f(y)?y(q().data):y;t&&-1!==e&&(e=setTimeout(r,t))}function r(){!q().error&&(v||F().isVisible())&&(m||F().isOnline())?ed(en).then(t):t()}return t(),()=>{e&&(clearTimeout(e),e=-1)}},[y,v,m,R]),(0,i.useDebugValue)(eo),l&&d(et)&&R){if(!L&&C)throw Error("Fallback data is required when using Suspense in SSR.");M.current=t,P.current=r,x.current=!1;let e=_[R];if(d(e)||er(ef(e)),d(ei)){let e=ed(en);d(eo)||(e.status="fulfilled",e.value=!0),er(e)}else throw ei}return{mutate:ef,get data(){return Z.data=!0,eo},get error(){return Z.error=!0,ei},get isValidating(){return Z.isValidating=!0,es},get isLoading(){return Z.isLoading=!0,ec}}},function(...e){let t=Q(),[r,i,a]=K(e),o=Z(t,a),u=n,{use:l}=o,s=(l||[]).concat(X);for(let e=s.length;e--;)u=s[e](u);return u(r,i||o.fetcher||null,o)})},7580:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},9033:(e,t,r)=>{e.exports=r(2436)},9946:(e,t,r)=>{r.d(t,{A:()=>d});var n=r(2115);let i=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),a=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,r)=>r?r.toUpperCase():t.toLowerCase()),o=e=>{let t=a(e);return t.charAt(0).toUpperCase()+t.slice(1)},u=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim()},l=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var s={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let c=(0,n.forwardRef)((e,t)=>{let{color:r="currentColor",size:i=24,strokeWidth:a=2,absoluteStrokeWidth:o,className:c="",children:d,iconNode:f,...g}=e;return(0,n.createElement)("svg",{ref:t,...s,width:i,height:i,stroke:r,strokeWidth:o?24*Number(a)/Number(i):a,className:u("lucide",c),...!d&&!l(g)&&{"aria-hidden":"true"},...g},[...f.map(e=>{let[t,r]=e;return(0,n.createElement)(t,r)}),...Array.isArray(d)?d:[d]])}),d=(e,t)=>{let r=(0,n.forwardRef)((r,a)=>{let{className:l,...s}=r;return(0,n.createElement)(c,{ref:a,iconNode:t,className:u("lucide-".concat(i(o(e))),"lucide-".concat(e),l),...s})});return r.displayName=o(e),r}}}]);
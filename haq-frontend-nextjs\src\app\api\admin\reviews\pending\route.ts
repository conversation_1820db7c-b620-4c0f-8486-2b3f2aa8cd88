import { NextRequest, NextResponse } from 'next/server';
import { withAdminAuth } from '@/lib/admin-middleware';
import { supabase } from '@/lib/supabase';

/**
 * GET /api/admin/reviews/pending
 * Admin endpoint to get all pending reviews for moderation
 * Following HAQ-rules.md ADMIN-02 specification
 * 
 * SECURITY REQUIREMENTS:
 * - Requires valid JWT with admin role (enforced by withAdminAuth)
 * - Returns pending reviews with company information
 * - author_id is included for admin context but NEVER exposed to public
 * 
 * Query Parameters:
 * - page: Page number for pagination (default: 1)
 * - limit: Number of results per page (default: 20, max: 100)
 * - sort: Sort order - 'newest', 'oldest', 'company_name' (default: 'newest')
 */
async function handleGetPendingReviews(
  request: NextRequest,
  context: any,
  user: any
): Promise<NextResponse> {
  try {
    const { searchParams } = new URL(request.url);
    
    // Parse and validate query parameters
    const page = Math.max(1, parseInt(searchParams.get('page') || '1', 10));
    const limit = Math.min(100, Math.max(1, parseInt(searchParams.get('limit') || '20', 10)));
    const sort = searchParams.get('sort') || 'newest';
    
    // Calculate offset for pagination
    const offset = (page - 1) * limit;
    
    // Validate sort parameter
    const validSorts = ['newest', 'oldest', 'company_name'];
    if (!validSorts.includes(sort)) {
      return NextResponse.json(
        { 
          success: false, 
          message: 'Invalid sort parameter. Must be one of: newest, oldest, company_name' 
        },
        { status: 400 }
      );
    }
    
    // Build sort clause
    let orderBy: { column: string; ascending: boolean };
    switch (sort) {
      case 'oldest':
        orderBy = { column: 'created_at', ascending: true };
        break;
      case 'company_name':
        orderBy = { column: 'companies.name', ascending: true };
        break;
      case 'newest':
      default:
        orderBy = { column: 'created_at', ascending: false };
        break;
    }
    
    // Get pending reviews with company information
    // NOTE: author_id is included for admin context but never exposed publicly
    const { data: reviews, error: reviewsError, count } = await supabase
      .from('reviews')
      .select(`
        review_id,
        company_id,
        author_id,
        overall_rating,
        pros,
        cons,
        advice_management,
        status,
        created_at,
        updated_at,
        companies!inner (
          company_id,
          name,
          industry,
          location
        )
      `, { count: 'exact' })
      .eq('status', 'pending')
      .order(orderBy.column, { ascending: orderBy.ascending })
      .range(offset, offset + limit - 1);
    
    if (reviewsError) {
      console.error('Error fetching pending reviews:', reviewsError);
      return NextResponse.json(
        { 
          success: false, 
          message: 'Failed to fetch pending reviews' 
        },
        { status: 500 }
      );
    }
    
    // Format reviews for admin interface
    const formattedReviews = reviews?.map(review => ({
      review_id: review.review_id,
      company: {
        company_id: review.companies.company_id,
        name: review.companies.name,
        industry: review.companies.industry,
        location: review.companies.location
      },
      author_id: review.author_id, // Admin context only
      overall_rating: review.overall_rating,
      pros: review.pros,
      cons: review.cons,
      advice_management: review.advice_management,
      status: review.status,
      created_at: review.created_at,
      updated_at: review.updated_at
    })) || [];
    
    // Calculate pagination metadata
    const totalCount = count || 0;
    const totalPages = Math.ceil(totalCount / limit);
    const hasNext = page < totalPages;
    const hasPrev = page > 1;
    
    // Return paginated response
    return NextResponse.json({
      success: true,
      data: {
        reviews: formattedReviews,
        pagination: {
          page,
          limit,
          total: totalCount,
          totalPages,
          hasNext,
          hasPrev
        },
        sort,
        filters: {
          status: 'pending'
        }
      }
    }, {
      headers: {
        // API_MEDIUM caching policy as per HAQ-rules.md RULE-402
        'Cache-Control': 'private, max-age=60, s-maxage=60', // 1 minute for admin data
        'Vary': 'Accept-Encoding',
        'X-Content-Type-Options': 'nosniff',
        'X-Frame-Options': 'DENY'
      }
    });
    
  } catch (error) {
    console.error('Admin pending reviews error:', error);
    return NextResponse.json(
      { 
        success: false, 
        message: 'Internal server error' 
      },
      { status: 500 }
    );
  }
}

// Export the GET handler wrapped with admin authentication
export const GET = withAdminAuth(handleGetPendingReviews);

// Only allow GET requests
export async function POST() {
  return NextResponse.json(
    { success: false, message: 'Method not allowed' },
    { status: 405, headers: { 'Allow': 'GET' } }
  );
}

export async function PUT() {
  return NextResponse.json(
    { success: false, message: 'Method not allowed' },
    { status: 405, headers: { 'Allow': 'GET' } }
  );
}

export async function DELETE() {
  return NextResponse.json(
    { success: false, message: 'Method not allowed' },
    { status: 405, headers: { 'Allow': 'GET' } }
  );
}

export async function PATCH() {
  return NextResponse.json(
    { success: false, message: 'Method not allowed' },
    { status: 405, headers: { 'Allow': 'GET' } }
  );
}

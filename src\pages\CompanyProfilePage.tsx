import React from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import { Star, MapPin, Users, Building, AlertTriangle, CheckCircle, TrendingUp, ArrowLeft, Flag } from 'lucide-react';

// Mock data - in real app this would come from API
const companyData = {
  1: {
    id: 1,
    name: 'TechFlow Solutions',
    logo: 'https://images.pexels.com/photos/3184398/pexels-photo-3184398.jpeg?auto=compress&cs=tinysrgb&w=200',
    haqScore: 4.2,
    totalReviews: 156,
    industry: 'Technology',
    location: 'Karachi',
    founded: '2018',
    employees: '100-200',
    website: 'www.techflow.com',
    description: 'A leading software development company specializing in web and mobile applications for local and international clients.',
    redFlags: ['Overtime Issues', 'Limited Growth Opportunities'],
    greenFlags: ['Good Benefits', 'Learning Opportunities', 'Modern Office', 'Flexible Hours'],
    reviews: [
      {
        id: 1,
        rating: 4,
        title: 'Great place for fresh graduates',
        pros: 'Good learning environment, supportive team members, modern technology stack',
        cons: 'Sometimes tight deadlines lead to overtime work',
        advice: 'Better project planning could reduce last-minute pressure',
        jobTitle: 'Software Engineer',
        employmentStatus: 'Current Employee',
        reviewDate: '2024-01-15',
        helpful: 23
      },
      {
        id: 2,
        rating: 5,
        title: 'Excellent work culture',
        pros: 'Amazing team, good salary, excellent benefits package',
        cons: 'Office location could be better accessible',
        advice: 'Keep up the good work!',
        jobTitle: 'Product Manager',
        employmentStatus: 'Current Employee',
        reviewDate: '2024-01-10',
        helpful: 31
      },
      {
        id: 3,
        rating: 3,
        title: 'Mixed experience',
        pros: 'Good technical challenges, learning opportunities',
        cons: 'Management could be more transparent about career progression',
        advice: 'Provide clearer career paths and regular feedback sessions',
        jobTitle: 'Frontend Developer',
        employmentStatus: 'Former Employee',
        reviewDate: '2023-12-20',
        helpful: 15
      }
    ],
    salaries: [
      { position: 'Software Engineer', avgSalary: 85000, experience: '1-3 years', reports: 12 },
      { position: 'Senior Software Engineer', avgSalary: 140000, experience: '3-5 years', reports: 8 },
      { position: 'Product Manager', avgSalary: 180000, experience: '5+ years', reports: 5 },
      { position: 'UI/UX Designer', avgSalary: 95000, experience: '2-4 years', reports: 7 }
    ]
  }
};

export const CompanyProfilePage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const company = companyData[parseInt(id!) as keyof typeof companyData];

  if (!company) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Company Not Found</h2>
          <Link to="/companies" className="text-primary-600 hover:text-primary-700">
            ← Back to Companies
          </Link>
        </div>
      </div>
    );
  }

  const getScoreColor = (score: number) => {
    if (score >= 4.0) return 'text-green-600 bg-green-50 border-green-200';
    if (score >= 3.0) return 'text-yellow-600 bg-yellow-50 border-yellow-200';
    return 'text-red-600 bg-red-50 border-red-200';
  };

  const getRatingStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`w-4 h-4 ${i < rating ? 'text-yellow-400 fill-current' : 'text-gray-300'}`}
      />
    ));
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Back Navigation */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <Link
            to="/companies"
            className="inline-flex items-center space-x-2 text-gray-600 hover:text-gray-900 transition-colors duration-200"
          >
            <ArrowLeft className="w-4 h-4" />
            <span>Back to Companies</span>
          </Link>
        </div>
      </div>

      {/* Company Header */}
      <div className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex flex-col md:flex-row md:items-start md:space-x-6">
            <img
              src={company.logo}
              alt={`${company.name} logo`}
              className="w-20 h-20 rounded-lg object-cover mb-4 md:mb-0"
            />
            
            <div className="flex-1">
              <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-4">
                <div>
                  <h1 className="text-3xl font-bold text-gray-900 mb-2">{company.name}</h1>
                  <div className="flex flex-wrap items-center gap-4 text-sm text-gray-600">
                    <div className="flex items-center space-x-1">
                      <Building className="w-4 h-4" />
                      <span>{company.industry}</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <MapPin className="w-4 h-4" />
                      <span>{company.location}</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Users className="w-4 h-4" />
                      <span>{company.employees} employees</span>
                    </div>
                  </div>
                </div>
                
                <div className="mt-4 md:mt-0">
                  <div className={`inline-flex items-center space-x-2 px-4 py-2 rounded-lg border font-medium ${getScoreColor(company.haqScore)}`}>
                    <Star className="w-5 h-5" />
                    <span className="text-lg">{company.haqScore}</span>
                    <span className="text-sm">Haq Score</span>
                  </div>
                  <p className="text-sm text-gray-500 mt-1 text-center">
                    {company.totalReviews} reviews
                  </p>
                </div>
              </div>

              <p className="text-gray-700 mb-6">{company.description}</p>

              {/* Action Buttons */}
              <div className="flex flex-col sm:flex-row gap-3">
                <Link
                  to="/review/submit"
                  className="bg-primary-600 hover:bg-primary-700 text-white px-6 py-3 rounded-lg font-medium transition-colors duration-200 flex items-center justify-center space-x-2"
                >
                  <Users className="w-5 h-5" />
                  <span>Write Review</span>
                </Link>
                <button className="border border-gray-300 hover:bg-gray-50 text-gray-700 px-6 py-3 rounded-lg font-medium transition-colors duration-200 flex items-center justify-center space-x-2">
                  <Flag className="w-5 h-5" />
                  <span>Report Issue</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-8">
            {/* Red Flags & Green Flags */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-6">Company Insights</h2>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Red Flags */}
                <div>
                  <div className="flex items-center space-x-2 mb-4">
                    <AlertTriangle className="w-5 h-5 text-red-500" />
                    <h3 className="font-medium text-gray-900">Red Flags</h3>
                  </div>
                  {company.redFlags.length > 0 ? (
                    <div className="space-y-2">
                      {company.redFlags.map((flag, index) => (
                        <div
                          key={index}
                          className="flex items-center space-x-2 p-3 bg-red-50 border border-red-200 rounded-lg"
                        >
                          <span className="text-sm text-red-700">{flag}</span>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <p className="text-sm text-gray-500 italic">No red flags reported</p>
                  )}
                </div>

                {/* Green Flags */}
                <div>
                  <div className="flex items-center space-x-2 mb-4">
                    <CheckCircle className="w-5 h-5 text-green-500" />
                    <h3 className="font-medium text-gray-900">Green Flags</h3>
                  </div>
                  <div className="space-y-2">
                    {company.greenFlags.map((flag, index) => (
                      <div
                        key={index}
                        className="flex items-center space-x-2 p-3 bg-green-50 border border-green-200 rounded-lg"
                      >
                        <span className="text-sm text-green-700">{flag}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>

            {/* Reviews */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-xl font-semibold text-gray-900">Employee Reviews</h2>
                <Link
                  to="/review/submit"
                  className="text-primary-600 hover:text-primary-700 text-sm font-medium"
                >
                  Write a Review
                </Link>
              </div>

              <div className="space-y-6">
                {company.reviews.map((review) => (
                  <div key={review.id} className="border-b border-gray-200 pb-6 last:border-b-0 last:pb-0">
                    <div className="flex items-start justify-between mb-4">
                      <div>
                        <div className="flex items-center space-x-2 mb-2">
                          <div className="flex space-x-1">
                            {getRatingStars(review.rating)}
                          </div>
                          <span className="text-sm font-medium text-gray-900">{review.title}</span>
                        </div>
                        <div className="text-sm text-gray-600">
                          {review.jobTitle} • {review.employmentStatus} • {review.reviewDate}
                        </div>
                      </div>
                    </div>

                    <div className="space-y-3 text-sm">
                      <div>
                        <span className="font-medium text-green-700">Pros: </span>
                        <span className="text-gray-700">{review.pros}</span>
                      </div>
                      <div>
                        <span className="font-medium text-red-700">Cons: </span>
                        <span className="text-gray-700">{review.cons}</span>
                      </div>
                      <div>
                        <span className="font-medium text-blue-700">Advice to Management: </span>
                        <span className="text-gray-700">{review.advice}</span>
                      </div>
                    </div>

                    <div className="mt-4 flex items-center justify-between">
                      <button className="text-sm text-gray-500 hover:text-gray-700">
                        Helpful ({review.helpful})
                      </button>
                      <button className="text-sm text-gray-500 hover:text-gray-700">
                        Report
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Sidebar */}
          <div className="space-y-8">
            {/* Company Info */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h3 className="font-semibold text-gray-900 mb-4">Company Information</h3>
              <div className="space-y-3 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600">Founded</span>
                  <span className="text-gray-900">{company.founded}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Industry</span>
                  <span className="text-gray-900">{company.industry}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Size</span>
                  <span className="text-gray-900">{company.employees}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Website</span>
                  <a href={`https://${company.website}`} className="text-primary-600 hover:underline">
                    {company.website}
                  </a>
                </div>
              </div>
            </div>

            {/* Salary Information */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-center space-x-2 mb-4">
                <TrendingUp className="w-5 h-5 text-primary-600" />
                <h3 className="font-semibold text-gray-900">Salary Information</h3>
              </div>
              <div className="space-y-4">
                {company.salaries.map((salary, index) => (
                  <div key={index} className="p-3 bg-gray-50 rounded-lg">
                    <div className="flex justify-between items-start mb-1">
                      <span className="text-sm font-medium text-gray-900">{salary.position}</span>
                      <span className="text-sm font-semibold text-primary-600">
                        PKR {salary.avgSalary.toLocaleString()}/month
                      </span>
                    </div>
                    <div className="text-xs text-gray-600">
                      {salary.experience} • {salary.reports} reports
                    </div>
                  </div>
                ))}
              </div>
              <Link
                to="/salaries"
                className="block text-center mt-4 text-sm text-primary-600 hover:text-primary-700 font-medium"
              >
                View All Salaries
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
/* [next]/internal/font/google/sora_a090df0f.module.css [app-client] (css) */
@font-face {
  font-family: Sora;
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: url("../media/xMQbuFFYT72XzQspDqW1KX7wmA-s.c6a2c6a4.woff2") format("woff2");
  unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: Sora;
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: url("../media/xMQbuFFYT72XzQUpDqW1KX4-s.p.0560adac.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Sora;
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url("../media/xMQbuFFYT72XzQspDqW1KX7wmA-s.c6a2c6a4.woff2") format("woff2");
  unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: Sora;
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url("../media/xMQbuFFYT72XzQUpDqW1KX4-s.p.0560adac.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Sora;
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: url("../media/xMQbuFFYT72XzQspDqW1KX7wmA-s.c6a2c6a4.woff2") format("woff2");
  unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: Sora;
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: url("../media/xMQbuFFYT72XzQUpDqW1KX4-s.p.0560adac.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Sora;
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url("../media/xMQbuFFYT72XzQspDqW1KX7wmA-s.c6a2c6a4.woff2") format("woff2");
  unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: Sora;
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url("../media/xMQbuFFYT72XzQUpDqW1KX4-s.p.0560adac.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Sora;
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url("../media/xMQbuFFYT72XzQspDqW1KX7wmA-s.c6a2c6a4.woff2") format("woff2");
  unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: Sora;
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url("../media/xMQbuFFYT72XzQUpDqW1KX4-s.p.0560adac.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Sora;
  font-style: normal;
  font-weight: 800;
  font-display: swap;
  src: url("../media/xMQbuFFYT72XzQspDqW1KX7wmA-s.c6a2c6a4.woff2") format("woff2");
  unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: Sora;
  font-style: normal;
  font-weight: 800;
  font-display: swap;
  src: url("../media/xMQbuFFYT72XzQUpDqW1KX4-s.p.0560adac.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Sora Fallback;
  src: local(Arial);
  ascent-override: 85.29%;
  descent-override: 25.5%;
  line-gap-override: 0.0%;
  size-adjust: 113.73%;
}

.sora_a090df0f-module__CgnO3a__className {
  font-family: Sora, Sora Fallback;
  font-style: normal;
}

.sora_a090df0f-module__CgnO3a__variable {
  --font-sora: "Sora", "Sora Fallback";
}

/*# sourceMappingURL=%5Bnext%5D_internal_font_google_sora_a090df0f_module_css_f9ee138c._.single.css.map*/
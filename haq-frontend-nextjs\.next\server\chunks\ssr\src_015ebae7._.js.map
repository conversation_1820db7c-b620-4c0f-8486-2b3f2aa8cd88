{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Haq%20website%20v1/haq-frontend-nextjs/src/components/common/SearchBar.tsx"], "sourcesContent": ["import React from 'react';\nimport { useRouter } from 'next/navigation';\nimport { Search } from 'lucide-react';\n\ninterface SearchBarProps {\n  value: string;\n  onChange: (value: string) => void;\n  placeholder?: string;\n  onSubmit?: () => void;\n  autoNavigate?: boolean; // New prop to enable automatic navigation to search results\n}\n\nexport const SearchBar: React.FC<SearchBarProps> = ({\n  value,\n  onChange,\n  placeholder = \"Search companies...\",\n  onSubmit,\n  autoNavigate = false\n}) => {\n  const router = useRouter();\n\n  const handleSubmit = (e: React.FormEvent) => {\n    e.preventDefault();\n\n    // If custom onSubmit is provided, use it\n    if (onSubmit) {\n      onSubmit();\n      return;\n    }\n\n    // If autoNavigate is enabled and there's a search query, navigate to search page\n    if (autoNavigate && value.trim()) {\n      const searchParams = new URLSearchParams();\n      searchParams.set('q', value.trim());\n      router.push(`/search?${searchParams.toString()}`);\n    }\n  };\n\n  return (\n    <form onSubmit={handleSubmit} className=\"relative\">\n      <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n        <Search className=\"h-5 w-5 text-text-secondary\" />\n      </div>\n      <input\n        type=\"text\"\n        value={value}\n        onChange={(e) => onChange(e.target.value)}\n        className=\"block w-full pl-10 pr-4 py-3 border border-border-primary rounded-medium bg-surface-primary placeholder-text-secondary focus:outline-none focus:ring-2 focus:ring-accent-primary focus:border-accent-primary text-text-primary transition-all duration-200\"\n        placeholder={placeholder}\n      />\n      {autoNavigate && (\n        <button\n          type=\"submit\"\n          className=\"absolute inset-y-0 right-0 pr-3 flex items-center text-text-secondary hover:text-accent-primary transition-colors duration-200\"\n          aria-label=\"Search\"\n        >\n          <Search className=\"h-5 w-5\" />\n        </button>\n      )}\n    </form>\n  );\n};\n"], "names": [], "mappings": ";;;;AACA;AACA;;;;AAUO,MAAM,YAAsC,CAAC,EAClD,KAAK,EACL,QAAQ,EACR,cAAc,qBAAqB,EACnC,QAAQ,EACR,eAAe,KAAK,EACrB;IACC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAEhB,yCAAyC;QACzC,IAAI,UAAU;YACZ;YACA;QACF;QAEA,iFAAiF;QACjF,IAAI,gBAAgB,MAAM,IAAI,IAAI;YAChC,MAAM,eAAe,IAAI;YACzB,aAAa,GAAG,CAAC,KAAK,MAAM,IAAI;YAChC,OAAO,IAAI,CAAC,CAAC,QAAQ,EAAE,aAAa,QAAQ,IAAI;QAClD;IACF;IAEA,qBACE,8OAAC;QAAK,UAAU;QAAc,WAAU;;0BACtC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,sMAAA,CAAA,SAAM;oBAAC,WAAU;;;;;;;;;;;0BAEpB,8OAAC;gBACC,MAAK;gBACL,OAAO;gBACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;gBACxC,WAAU;gBACV,aAAa;;;;;;YAEd,8BACC,8OAAC;gBACC,MAAK;gBACL,WAAU;gBACV,cAAW;0BAEX,cAAA,8OAAC,sMAAA,CAAA,SAAM;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAK5B", "debugId": null}}, {"offset": {"line": 90, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Haq%20website%20v1/haq-frontend-nextjs/src/components/common/ErrorBoundary.tsx"], "sourcesContent": ["'use client';\n\nimport React, { Component, ErrorInfo, ReactNode } from 'react';\n\ninterface Props {\n  children: ReactNode;\n  fallback?: ReactNode;\n}\n\ninterface State {\n  hasError: boolean;\n  error?: Error;\n}\n\nexport class ErrorBoundary extends Component<Props, State> {\n  public state: State = {\n    hasError: false\n  };\n\n  public static getDerivedStateFromError(error: Error): State {\n    // Update state so the next render will show the fallback UI\n    return { hasError: true, error };\n  }\n\n  public componentDidCatch(error: Error, errorInfo: ErrorInfo) {\n    // Log error details but don't spam console in development\n    if (process.env.NODE_ENV === 'production') {\n      console.error('ErrorBoundary caught an error:', error, errorInfo);\n    }\n  }\n\n  public render() {\n    if (this.state.hasError) {\n      // Return custom fallback UI or default\n      return this.props.fallback || (\n        <div className=\"w-full h-full bg-surface-secondary rounded-lg flex items-center justify-center\">\n          <div className=\"text-text-secondary text-sm\">Unable to load</div>\n        </div>\n      );\n    }\n\n    return this.props.children;\n  }\n}\n\n// Simple wrapper for images\nexport const SafeImageWrapper: React.FC<{ children: ReactNode }> = ({ children }) => {\n  return (\n    <ErrorBoundary \n      fallback={\n        <div className=\"w-full h-full bg-surface-secondary rounded-lg flex items-center justify-center\">\n          <div className=\"w-6 h-6 bg-surface-tertiary rounded\"></div>\n        </div>\n      }\n    >\n      {children}\n    </ErrorBoundary>\n  );\n};\n"], "names": [], "mappings": ";;;;;AAEA;AAFA;;;AAcO,MAAM,sBAAsB,qMAAA,CAAA,YAAS;IACnC,QAAe;QACpB,UAAU;IACZ,EAAE;IAEF,OAAc,yBAAyB,KAAY,EAAS;QAC1D,4DAA4D;QAC5D,OAAO;YAAE,UAAU;YAAM;QAAM;IACjC;IAEO,kBAAkB,KAAY,EAAE,SAAoB,EAAE;QAC3D,0DAA0D;QAC1D,uCAA2C;;QAE3C;IACF;IAEO,SAAS;QACd,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE;YACvB,uCAAuC;YACvC,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,kBACxB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BAA8B;;;;;;;;;;;QAGnD;QAEA,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ;IAC5B;AACF;AAGO,MAAM,mBAAsD,CAAC,EAAE,QAAQ,EAAE;IAC9E,qBACE,8OAAC;QACC,wBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;;;;;;;;;;kBAIlB;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 167, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Haq%20website%20v1/haq-frontend-nextjs/src/components/common/CompanyCard.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport Link from 'next/link';\nimport Image from 'next/image';\nimport { Star, MapPin, Users, AlertTriangle, CheckCircle } from 'lucide-react';\nimport { SafeImageWrapper } from './ErrorBoundary';\n\ninterface CompanyCardProps {\n  company: {\n    company_id?: string; // New UUID format\n    id?: number; // Legacy support\n    name: string;\n    logo_url?: string;\n    haq_score: number;\n    total_reviews?: number;\n    industry?: string;\n    location?: string;\n    redFlags?: string[];\n    greenFlags?: string[];\n  };\n  delay?: number;\n}\n\n// Robust image component that handles errors and prevents hydration issues\nconst SafeImage: React.FC<{\n  src: string;\n  alt: string;\n  className?: string;\n}> = ({ src, alt, className }) => {\n  const [isClient, setIsClient] = useState(false);\n  const [imageError, setImageError] = useState(false);\n  const [imageSrc, setImageSrc] = useState(src);\n\n  useEffect(() => {\n    setIsClient(true);\n    setImageSrc(src);\n    setImageError(false);\n  }, [src]);\n\n  const handleImageError = () => {\n    console.warn(`Image failed to load: ${imageSrc}`);\n    setImageError(true);\n    setImageSrc('/placeholder-company.svg');\n  };\n\n  if (!isClient) {\n    return <div className=\"w-full h-full bg-surface-secondary animate-pulse rounded-lg flex items-center justify-center\">\n      <div className=\"w-6 h-6 bg-surface-tertiary rounded\"></div>\n    </div>;\n  }\n\n  if (imageError && imageSrc === '/placeholder-company.svg') {\n    return <div className=\"w-full h-full bg-surface-secondary rounded-lg flex items-center justify-center\">\n      <div className=\"w-6 h-6 bg-surface-tertiary rounded\"></div>\n    </div>;\n  }\n\n  return (\n    <Image\n      src={imageSrc}\n      alt={alt}\n      fill\n      className={className}\n      sizes=\"48px\"\n      priority={false}\n      onError={handleImageError}\n      placeholder=\"blur\"\n      blurDataURL=\"data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAhEAACAQMDBQAAAAAAAAAAAAABAgMABAUGIWGRkqGx0f/EABUBAQEAAAAAAAAAAAAAAAAAAAMF/8QAGhEAAgIDAAAAAAAAAAAAAAAAAAECEgMRkf/aAAwDAQACEQMRAD8AltJagyeH0AthI5xdrLcNM91BF5pX2HaH9bcfaSXWGaRmknyJckliyjqTzSlT54b6bk+h0R+Kcp/9k=\"\n    />\n  );\n};\n\nexport const CompanyCard: React.FC<CompanyCardProps> = ({ company, delay = 0 }) => {\n  const getScoreColor = (score: number) => {\n    if (score >= 4.0) return 'text-green-400';\n    if (score >= 3.0) return 'text-yellow-400';\n    return 'text-red-400';\n  };\n\n  const getScoreBgColor = (score: number) => {\n    if (score >= 4.0) return 'bg-green-400/20';\n    if (score >= 3.0) return 'bg-yellow-400/20';\n    return 'bg-red-400/20';\n  };\n\n  // Use company_id if available, fallback to legacy id\n  const companyId = company.company_id || company.id;\n  const companyUrl = `/companies/${companyId}`;\n\n  return (\n    <Link href={companyUrl}>\n      <div \n        className=\"bg-surface-primary border border-border-primary rounded-medium p-6 hover:border-accent-primary transition-all duration-200 animate-slide-up group cursor-pointer\"\n        style={{ animationDelay: `${delay}ms` }}\n      >\n        {/* Header */}\n        <div className=\"flex items-start space-x-4 mb-4\">\n          <div className=\"relative w-12 h-12 rounded-lg overflow-hidden bg-surface-secondary flex-shrink-0\">\n            <SafeImageWrapper>\n              <SafeImage\n                src={company.logo_url || \"/placeholder-company.svg\"}\n                alt={`${company.name} logo`}\n                className=\"object-cover\"\n              />\n            </SafeImageWrapper>\n          </div>\n          <div className=\"flex-1 min-w-0\">\n            <h3 className=\"text-lg font-semibold text-text-primary group-hover:text-accent-primary transition-colors duration-200 truncate\">\n              {company.name}\n            </h3>\n            <div className=\"flex items-center space-x-4 text-sm text-text-secondary\">\n              {company.industry && <span>{company.industry}</span>}\n              {company.location && (\n                <div className=\"flex items-center space-x-1\">\n                  <MapPin className=\"w-3 h-3\" />\n                  <span>{company.location}</span>\n                </div>\n              )}\n            </div>\n          </div>\n        </div>\n\n        {/* Haq Score */}\n        <div className=\"flex items-center justify-between mb-4\">\n          <div className=\"flex items-center space-x-2\">\n            <div className={`px-3 py-1 rounded-lg ${getScoreBgColor(company.haq_score || 0)}`}>\n              <span className={`font-bold ${getScoreColor(company.haq_score || 0)}`}>\n                {(company.haq_score || 0).toFixed(1)}\n              </span>\n            </div>\n            <div className=\"flex items-center space-x-1\">\n              <Star className={`w-4 h-4 ${getScoreColor(company.haq_score || 0)}`} fill=\"currentColor\" />\n              <span className=\"text-text-secondary text-sm\">Haq Score</span>\n            </div>\n          </div>\n          <div className=\"flex items-center space-x-1 text-text-secondary text-sm\">\n            <Users className=\"w-4 h-4\" />\n            <span>{company.total_reviews || 0} reviews</span>\n          </div>\n        </div>\n\n        {/* Flags */}\n        <div className=\"space-y-3\">\n          {/* Green Flags */}\n          {company.greenFlags && company.greenFlags.length > 0 && (\n            <div>\n              <div className=\"flex items-center space-x-2 mb-2\">\n                <CheckCircle className=\"w-4 h-4 text-green-400\" />\n                <span className=\"text-sm font-medium text-text-primary\">Positives</span>\n              </div>\n              <div className=\"flex flex-wrap gap-1\">\n                {company.greenFlags.slice(0, 2).map((flag, index) => (\n                  <span\n                    key={index}\n                    className=\"px-2 py-1 bg-green-400/20 text-green-400 text-xs rounded-lg\"\n                  >\n                    {flag}\n                  </span>\n                ))}\n                {company.greenFlags.length > 2 && (\n                  <span className=\"px-2 py-1 bg-surface-secondary text-text-secondary text-xs rounded-lg\">\n                    +{company.greenFlags.length - 2} more\n                  </span>\n                )}\n              </div>\n            </div>\n          )}\n\n          {/* Red Flags */}\n          {company.redFlags && company.redFlags.length > 0 && (\n            <div>\n              <div className=\"flex items-center space-x-2 mb-2\">\n                <AlertTriangle className=\"w-4 h-4 text-red-400\" />\n                <span className=\"text-sm font-medium text-text-primary\">Issues</span>\n              </div>\n              <div className=\"flex flex-wrap gap-1\">\n                {company.redFlags.slice(0, 2).map((flag, index) => (\n                  <span\n                    key={index}\n                    className=\"px-2 py-1 bg-red-400/20 text-red-400 text-xs rounded-lg\"\n                  >\n                    {flag}\n                  </span>\n                ))}\n                {company.redFlags.length > 2 && (\n                  <span className=\"px-2 py-1 bg-surface-secondary text-text-secondary text-xs rounded-lg\">\n                    +{company.redFlags.length - 2} more\n                  </span>\n                )}\n              </div>\n            </div>\n          )}\n        </div>\n\n        {/* View Details */}\n        <div className=\"mt-4 pt-4 border-t border-border-primary\">\n          <span className=\"text-accent-primary text-sm font-medium group-hover:underline\">\n            View Details →\n          </span>\n        </div>\n      </div>\n    </Link>\n  );\n};\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;;;;;;;AAkBA,2EAA2E;AAC3E,MAAM,YAID,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,SAAS,EAAE;IAC3B,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,YAAY;QACZ,YAAY;QACZ,cAAc;IAChB,GAAG;QAAC;KAAI;IAER,MAAM,mBAAmB;QACvB,QAAQ,IAAI,CAAC,CAAC,sBAAsB,EAAE,UAAU;QAChD,cAAc;QACd,YAAY;IACd;IAEA,IAAI,CAAC,UAAU;QACb,qBAAO,8OAAC;YAAI,WAAU;sBACpB,cAAA,8OAAC;gBAAI,WAAU;;;;;;;;;;;IAEnB;IAEA,IAAI,cAAc,aAAa,4BAA4B;QACzD,qBAAO,8OAAC;YAAI,WAAU;sBACpB,cAAA,8OAAC;gBAAI,WAAU;;;;;;;;;;;IAEnB;IAEA,qBACE,8OAAC,6HAAA,CAAA,UAAK;QACJ,KAAK;QACL,KAAK;QACL,IAAI;QACJ,WAAW;QACX,OAAM;QACN,UAAU;QACV,SAAS;QACT,aAAY;QACZ,aAAY;;;;;;AAGlB;AAEO,MAAM,cAA0C,CAAC,EAAE,OAAO,EAAE,QAAQ,CAAC,EAAE;IAC5E,MAAM,gBAAgB,CAAC;QACrB,IAAI,SAAS,KAAK,OAAO;QACzB,IAAI,SAAS,KAAK,OAAO;QACzB,OAAO;IACT;IAEA,MAAM,kBAAkB,CAAC;QACvB,IAAI,SAAS,KAAK,OAAO;QACzB,IAAI,SAAS,KAAK,OAAO;QACzB,OAAO;IACT;IAEA,qDAAqD;IACrD,MAAM,YAAY,QAAQ,UAAU,IAAI,QAAQ,EAAE;IAClD,MAAM,aAAa,CAAC,WAAW,EAAE,WAAW;IAE5C,qBACE,8OAAC,4JAAA,CAAA,UAAI;QAAC,MAAM;kBACV,cAAA,8OAAC;YACC,WAAU;YACV,OAAO;gBAAE,gBAAgB,GAAG,MAAM,EAAE,CAAC;YAAC;;8BAGtC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,6IAAA,CAAA,mBAAgB;0CACf,cAAA,8OAAC;oCACC,KAAK,QAAQ,QAAQ,IAAI;oCACzB,KAAK,GAAG,QAAQ,IAAI,CAAC,KAAK,CAAC;oCAC3B,WAAU;;;;;;;;;;;;;;;;sCAIhB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CACX,QAAQ,IAAI;;;;;;8CAEf,8OAAC;oCAAI,WAAU;;wCACZ,QAAQ,QAAQ,kBAAI,8OAAC;sDAAM,QAAQ,QAAQ;;;;;;wCAC3C,QAAQ,QAAQ,kBACf,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,0MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,8OAAC;8DAAM,QAAQ,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQjC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAW,CAAC,qBAAqB,EAAE,gBAAgB,QAAQ,SAAS,IAAI,IAAI;8CAC/E,cAAA,8OAAC;wCAAK,WAAW,CAAC,UAAU,EAAE,cAAc,QAAQ,SAAS,IAAI,IAAI;kDAClE,CAAC,QAAQ,SAAS,IAAI,CAAC,EAAE,OAAO,CAAC;;;;;;;;;;;8CAGtC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAW,CAAC,QAAQ,EAAE,cAAc,QAAQ,SAAS,IAAI,IAAI;4CAAE,MAAK;;;;;;sDAC1E,8OAAC;4CAAK,WAAU;sDAA8B;;;;;;;;;;;;;;;;;;sCAGlD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,oMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;8CACjB,8OAAC;;wCAAM,QAAQ,aAAa,IAAI;wCAAE;;;;;;;;;;;;;;;;;;;8BAKtC,8OAAC;oBAAI,WAAU;;wBAEZ,QAAQ,UAAU,IAAI,QAAQ,UAAU,CAAC,MAAM,GAAG,mBACjD,8OAAC;;8CACC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,2NAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;sDACvB,8OAAC;4CAAK,WAAU;sDAAwC;;;;;;;;;;;;8CAE1D,8OAAC;oCAAI,WAAU;;wCACZ,QAAQ,UAAU,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,MAAM,sBACzC,8OAAC;gDAEC,WAAU;0DAET;+CAHI;;;;;wCAMR,QAAQ,UAAU,CAAC,MAAM,GAAG,mBAC3B,8OAAC;4CAAK,WAAU;;gDAAwE;gDACpF,QAAQ,UAAU,CAAC,MAAM,GAAG;gDAAE;;;;;;;;;;;;;;;;;;;wBAQzC,QAAQ,QAAQ,IAAI,QAAQ,QAAQ,CAAC,MAAM,GAAG,mBAC7C,8OAAC;;8CACC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,wNAAA,CAAA,gBAAa;4CAAC,WAAU;;;;;;sDACzB,8OAAC;4CAAK,WAAU;sDAAwC;;;;;;;;;;;;8CAE1D,8OAAC;oCAAI,WAAU;;wCACZ,QAAQ,QAAQ,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,MAAM,sBACvC,8OAAC;gDAEC,WAAU;0DAET;+CAHI;;;;;wCAMR,QAAQ,QAAQ,CAAC,MAAM,GAAG,mBACzB,8OAAC;4CAAK,WAAU;;gDAAwE;gDACpF,QAAQ,QAAQ,CAAC,MAAM,GAAG;gDAAE;;;;;;;;;;;;;;;;;;;;;;;;;8BAS1C,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAK,WAAU;kCAAgE;;;;;;;;;;;;;;;;;;;;;;AAO1F", "debugId": null}}, {"offset": {"line": 608, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Haq%20website%20v1/haq-frontend-nextjs/src/app/page.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport React, { useState } from 'react';\r\nimport Link from 'next/link';\r\nimport useSWR from 'swr';\r\nimport { Search, Shield, Users, Star, TrendingUp, AlertTriangle, CheckCircle, ArrowRight, MessageCircle, DollarSign } from 'lucide-react';\r\nimport { SearchBar } from '@/components/common/SearchBar';\r\nimport { CompanyCard } from '@/components/common/CompanyCard';\r\n\r\n// Mock data\r\nconst featuredCompanies = [\r\n  {\r\n    id: 1,\r\n    name: 'TechFlow Solutions',\r\n    logo: 'https://images.pexels.com/photos/3184398/pexels-photo-3184398.jpeg?auto=compress&cs=tinysrgb&w=100',\r\n    haqScore: 4.2,\r\n    totalReviews: 156,\r\n    industry: 'Technology',\r\n    location: 'Karachi',\r\n    redFlags: ['Overtime Issues'],\r\n    greenFlags: ['Good Benefits', 'Learning Opportunities']\r\n  },\r\n  {\r\n    id: 2,\r\n    name: 'Innovate Marketing',\r\n    logo: 'https://images.pexels.com/photos/3184291/pexels-photo-3184291.jpeg?auto=compress&cs=tinysrgb&w=100',\r\n    haqScore: 3.8,\r\n    totalReviews: 89,\r\n    industry: 'Marketing',\r\n    location: 'Lahore',\r\n    redFlags: ['Work-Life Balance'],\r\n    greenFlags: ['Creative Environment', 'Fair Pay']\r\n  },\r\n  {\r\n    id: 3,\r\n    name: 'Prime Logistics',\r\n    logo: 'https://images.pexels.com/photos/3184357/pexels-photo-3184357.jpeg?auto=compress&cs=tinysrgb&w=100',\r\n    haqScore: 4.5,\r\n    totalReviews: 234,\r\n    industry: 'Logistics',\r\n    location: 'Islamabad',\r\n    redFlags: [],\r\n    greenFlags: ['Excellent Management', 'Timely Payments', 'Growth Opportunities']\r\n  }\r\n];\r\n\r\nconst stats = [\r\n  { label: 'Company Reviews', value: '12,456', icon: Shield, color: 'primary' },\r\n  { label: 'Anonymous Users', value: '8,234', icon: Users, color: 'secondary' },\r\n  { label: 'Salary Reports', value: '5,678', icon: TrendingUp, color: 'warning' },\r\n  { label: 'Issues Reported', value: '1,234', icon: AlertTriangle, color: 'danger' }\r\n] as const;\r\n\r\n// Loading skeleton component to prevent hydration mismatch\r\nfunction LoadingSkeleton() {\r\n  return (\r\n    <div className=\"min-h-screen bg-background-primary\">\r\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\">\r\n        {/* Hero Section Skeleton */}\r\n        <div className=\"text-center mb-12\">\r\n          <div className=\"h-12 bg-surface-secondary rounded mb-4 animate-pulse\"></div>\r\n          <div className=\"h-6 bg-surface-secondary rounded w-3/4 mx-auto mb-8 animate-pulse\"></div>\r\n          <div className=\"max-w-2xl mx-auto\">\r\n            <div className=\"h-12 bg-surface-secondary rounded animate-pulse\"></div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Stats Section Skeleton */}\r\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8 mb-16\">\r\n          {Array.from({ length: 3 }).map((_, index) => (\r\n            <div key={index} className=\"text-center\">\r\n              <div className=\"h-8 bg-surface-secondary rounded mb-2 animate-pulse\"></div>\r\n              <div className=\"h-4 bg-surface-secondary rounded w-2/3 mx-auto animate-pulse\"></div>\r\n            </div>\r\n          ))}\r\n        </div>\r\n\r\n        {/* Featured Companies Skeleton */}\r\n        <div className=\"mb-12\">\r\n          <div className=\"h-8 bg-surface-secondary rounded w-1/3 mb-6 animate-pulse\"></div>\r\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\r\n            {Array.from({ length: 3 }).map((_, index) => (\r\n              <div key={index} className=\"bg-surface-primary border border-border-primary rounded-medium p-6\">\r\n                <div className=\"flex items-start space-x-4 mb-4\">\r\n                  <div className=\"w-12 h-12 bg-surface-secondary rounded-lg animate-pulse\"></div>\r\n                  <div className=\"flex-1\">\r\n                    <div className=\"h-4 bg-surface-secondary rounded mb-2 animate-pulse\"></div>\r\n                    <div className=\"h-3 bg-surface-secondary rounded w-3/4 animate-pulse\"></div>\r\n                  </div>\r\n                </div>\r\n                <div className=\"h-3 bg-surface-secondary rounded mb-4 animate-pulse\"></div>\r\n                <div className=\"space-y-2\">\r\n                  <div className=\"h-3 bg-surface-secondary rounded w-1/2 animate-pulse\"></div>\r\n                  <div className=\"h-3 bg-surface-secondary rounded w-2/3 animate-pulse\"></div>\r\n                </div>\r\n              </div>\r\n            ))}\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default function HomePage() {\r\n  const [searchQuery, setSearchQuery] = useState('');\r\n  const [mounted, setMounted] = useState(false);\r\n\r\n  // Fetch companies data using SWR\r\n  const { data: companies, error, isLoading } = useSWR(mounted ? '/api/companies' : null);\r\n\r\n  // Prevent hydration mismatch by ensuring client-side only rendering\r\n  React.useEffect(() => {\r\n    setMounted(true);\r\n  }, []);\r\n\r\n  // Show loading state during hydration to prevent mismatch\r\n  if (!mounted) {\r\n    return <LoadingSkeleton />;\r\n  }\r\n\r\n  return (\r\n    <div className=\"min-h-screen bg-background-primary\">\r\n      {/* Hero Section */}\r\n      <section className=\"relative bg-gradient-to-br from-background-primary via-background-secondary to-background-primary text-text-primary overflow-hidden\">\r\n        {/* Background Pattern */}\r\n        <div className=\"absolute inset-0 opacity-10\">\r\n          <div className=\"absolute top-20 left-10 w-32 h-32 bg-accent-primary rounded-full blur-3xl\"></div>\r\n          <div className=\"absolute bottom-20 right-10 w-40 h-40 bg-accent-secondary rounded-full blur-3xl\"></div>\r\n        </div>\r\n\r\n        <div className=\"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20\">\r\n          <div className=\"text-center max-w-4xl mx-auto\">\r\n            <h1 className=\"text-4xl md:text-display-title font-bold mb-6 animate-fade-in uppercase tracking-wider\">\r\n              Your Rights, Your Voice\r\n              <span className=\"block text-2xl md:text-3xl font-normal mt-2 text-accent-primary font-urdu\">\r\n                آپ کے حقوق، آپ کی آواز\r\n              </span>\r\n            </h1>\r\n            <p className=\"text-lg md:text-xl text-text-secondary mb-8 animate-slide-up max-w-3xl mx-auto\">\r\n              Empowering Pakistani employees through transparency, anonymous reviews, and workplace accountability.\r\n              Combat exploitation with honest feedback from real employees.\r\n            </p>\r\n\r\n            {/* Search Bar */}\r\n            <div className=\"max-w-2xl mx-auto mb-8 animate-slide-up\">\r\n              <SearchBar\r\n                value={searchQuery}\r\n                onChange={setSearchQuery}\r\n                placeholder=\"Search companies, reviews, or salaries...\"\r\n                autoNavigate={true}\r\n              />\r\n            </div>\r\n\r\n            {/* Quick Actions */}\r\n            <div className=\"flex flex-col sm:flex-row gap-4 justify-center items-center animate-slide-up\">\r\n              <Link\r\n                href=\"/review/submit\"\r\n                className=\"bg-accent-primary hover:bg-accent-secondary text-text-on-accent px-6 py-3 rounded-lg font-semibold transition-all duration-200 flex items-center space-x-2 group hover:shadow-glow-lg transform hover:-translate-y-1\"\r\n              >\r\n                <Users className=\"w-5 h-5\" />\r\n                <span>Write Anonymous Review</span>\r\n                <ArrowRight className=\"w-4 h-4 group-hover:translate-x-1 transition-transform\" />\r\n              </Link>\r\n              <Link\r\n                href=\"/companies\"\r\n                className=\"border-2 border-accent-primary text-accent-primary hover:bg-accent-primary hover:text-text-on-accent px-6 py-3 rounded-lg font-semibold transition-all duration-200 flex items-center space-x-2\"\r\n              >\r\n                <Search className=\"w-5 h-5\" />\r\n                <span>Browse Companies</span>\r\n              </Link>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Stats Section */}\r\n      <section className=\"py-16 bg-surface-primary\">\r\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\r\n          <div className=\"grid grid-cols-2 md:grid-cols-4 gap-6\">\r\n            {stats.map((stat, index) => (\r\n              <div\r\n                key={stat.label}\r\n                className=\"bg-background-secondary rounded-medium p-6 border border-border-primary hover:border-accent-primary transition-all duration-200 animate-slide-up text-center group\"\r\n                style={{ animationDelay: `${index * 100}ms` }}\r\n              >\r\n                <div className=\"flex items-center justify-center mb-4\">\r\n                  <div className=\"p-3 rounded-lg bg-surface-secondary group-hover:bg-accent-primary/20 transition-colors duration-200\">\r\n                    <stat.icon className=\"w-6 h-6 text-accent-primary\" />\r\n                  </div>\r\n                </div>\r\n                <p className=\"text-data-readout font-bold text-text-primary mb-1\">{stat.value}</p>\r\n                <p className=\"text-body-label text-text-secondary\">{stat.label}</p>\r\n              </div>\r\n            ))}\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Featured Companies */}\r\n      <section className=\"py-16 bg-background-primary\">\r\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\r\n          <div className=\"text-center mb-12\">\r\n            <h2 className=\"text-module-header font-semibold text-accent-primary mb-4 uppercase tracking-wider\">\r\n              Featured Companies\r\n            </h2>\r\n            <p className=\"text-lg text-text-secondary max-w-2xl mx-auto\">\r\n              Discover companies with the highest Haq Scores and positive employee feedback across Pakistan.\r\n            </p>\r\n          </div>\r\n\r\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12\">\r\n            {isLoading ? (\r\n              // Loading skeleton\r\n              Array.from({ length: 3 }).map((_, index) => (\r\n                <div key={index} className=\"bg-surface-primary border border-border-primary rounded-medium p-6 animate-pulse\">\r\n                  <div className=\"flex items-start space-x-4 mb-4\">\r\n                    <div className=\"w-12 h-12 bg-surface-secondary rounded-lg\"></div>\r\n                    <div className=\"flex-1\">\r\n                      <div className=\"h-4 bg-surface-secondary rounded mb-2\"></div>\r\n                      <div className=\"h-3 bg-surface-secondary rounded w-3/4\"></div>\r\n                    </div>\r\n                  </div>\r\n                  <div className=\"h-3 bg-surface-secondary rounded mb-4\"></div>\r\n                  <div className=\"space-y-2\">\r\n                    <div className=\"h-3 bg-surface-secondary rounded w-1/2\"></div>\r\n                    <div className=\"h-3 bg-surface-secondary rounded w-2/3\"></div>\r\n                  </div>\r\n                </div>\r\n              ))\r\n            ) : error ? (\r\n              <div className=\"col-span-full text-center py-12\">\r\n                <p className=\"text-text-secondary\">Failed to load companies. Please try again later.</p>\r\n              </div>\r\n            ) : companies && companies.length > 0 ? (\r\n              companies.slice(0, 3).map((company: any, index: number) => (\r\n                <CompanyCard key={company.company_id || company.id} company={company} delay={index * 100} />\r\n              ))\r\n            ) : (\r\n              <div className=\"col-span-full text-center py-12\">\r\n                <p className=\"text-text-secondary\">No companies found.</p>\r\n              </div>\r\n            )}\r\n          </div>\r\n\r\n          <div className=\"text-center\">\r\n            <Link\r\n              href=\"/companies\"\r\n              className=\"inline-flex items-center space-x-2 bg-accent-primary hover:bg-accent-secondary text-text-on-accent px-6 py-3 rounded-lg font-semibold transition-all duration-200 hover:shadow-glow transform hover:-translate-y-0.5\"\r\n            >\r\n              <span>View All Companies</span>\r\n              <ArrowRight className=\"w-5 h-5\" />\r\n            </Link>\r\n          </div>\r\n        </div>\r\n      </section>\r\n    </div>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAPA;;;;;;;;AASA,YAAY;AACZ,MAAM,oBAAoB;IACxB;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,UAAU;QACV,cAAc;QACd,UAAU;QACV,UAAU;QACV,UAAU;YAAC;SAAkB;QAC7B,YAAY;YAAC;YAAiB;SAAyB;IACzD;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,UAAU;QACV,cAAc;QACd,UAAU;QACV,UAAU;QACV,UAAU;YAAC;SAAoB;QAC/B,YAAY;YAAC;YAAwB;SAAW;IAClD;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,UAAU;QACV,cAAc;QACd,UAAU;QACV,UAAU;QACV,UAAU,EAAE;QACZ,YAAY;YAAC;YAAwB;YAAmB;SAAuB;IACjF;CACD;AAED,MAAM,QAAQ;IACZ;QAAE,OAAO;QAAmB,OAAO;QAAU,MAAM,sMAAA,CAAA,SAAM;QAAE,OAAO;IAAU;IAC5E;QAAE,OAAO;QAAmB,OAAO;QAAS,MAAM,oMAAA,CAAA,QAAK;QAAE,OAAO;IAAY;IAC5E;QAAE,OAAO;QAAkB,OAAO;QAAS,MAAM,kNAAA,CAAA,aAAU;QAAE,OAAO;IAAU;IAC9E;QAAE,OAAO;QAAmB,OAAO;QAAS,MAAM,wNAAA,CAAA,gBAAa;QAAE,OAAO;IAAS;CAClF;AAED,2DAA2D;AAC3D,SAAS;IACP,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;;;;;;;;;;;;;;;;8BAKnB,8OAAC;oBAAI,WAAU;8BACZ,MAAM,IAAI,CAAC;wBAAE,QAAQ;oBAAE,GAAG,GAAG,CAAC,CAAC,GAAG,sBACjC,8OAAC;4BAAgB,WAAU;;8CACzB,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAI,WAAU;;;;;;;2BAFP;;;;;;;;;;8BAQd,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAI,WAAU;sCACZ,MAAM,IAAI,CAAC;gCAAE,QAAQ;4BAAE,GAAG,GAAG,CAAC,CAAC,GAAG,sBACjC,8OAAC;oCAAgB,WAAU;;sDACzB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;;;;;8DACf,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;;;;;sEACf,8OAAC;4DAAI,WAAU;;;;;;;;;;;;;;;;;;sDAGnB,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;;;;;8DACf,8OAAC;oDAAI,WAAU;;;;;;;;;;;;;mCAXT;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoBxB;AAEe,SAAS;IACtB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,iCAAiC;IACjC,MAAM,EAAE,MAAM,SAAS,EAAE,KAAK,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,8JAAA,CAAA,UAAM,AAAD,EAAE,UAAU,mBAAmB;IAElF,oEAAoE;IACpE,qMAAA,CAAA,UAAK,CAAC,SAAS,CAAC;QACd,WAAW;IACb,GAAG,EAAE;IAEL,0DAA0D;IAC1D,IAAI,CAAC,SAAS;QACZ,qBAAO,8OAAC;;;;;IACV;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAQ,WAAU;;kCAEjB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;;;;;;;;;;;;kCAGjB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;;wCAAyF;sDAErG,8OAAC;4CAAK,WAAU;sDAA4E;;;;;;;;;;;;8CAI9F,8OAAC;oCAAE,WAAU;8CAAiF;;;;;;8CAM9F,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,yIAAA,CAAA,YAAS;wCACR,OAAO;wCACP,UAAU;wCACV,aAAY;wCACZ,cAAc;;;;;;;;;;;8CAKlB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;;8DAEV,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;8DACjB,8OAAC;8DAAK;;;;;;8DACN,8OAAC,kNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;;;;;;;sDAExB,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;;8DAEV,8OAAC,sMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,8OAAC;8DAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQhB,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACZ,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,8OAAC;gCAEC,WAAU;gCACV,OAAO;oCAAE,gBAAgB,GAAG,QAAQ,IAAI,EAAE,CAAC;gCAAC;;kDAE5C,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,KAAK,IAAI;gDAAC,WAAU;;;;;;;;;;;;;;;;kDAGzB,8OAAC;wCAAE,WAAU;kDAAsD,KAAK,KAAK;;;;;;kDAC7E,8OAAC;wCAAE,WAAU;kDAAuC,KAAK,KAAK;;;;;;;+BAVzD,KAAK,KAAK;;;;;;;;;;;;;;;;;;;;0BAkBzB,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAqF;;;;;;8CAGnG,8OAAC;oCAAE,WAAU;8CAAgD;;;;;;;;;;;;sCAK/D,8OAAC;4BAAI,WAAU;sCACZ,YACC,mBAAmB;4BACnB,MAAM,IAAI,CAAC;gCAAE,QAAQ;4BAAE,GAAG,GAAG,CAAC,CAAC,GAAG,sBAChC,8OAAC;oCAAgB,WAAU;;sDACzB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;;;;;8DACf,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;;;;;sEACf,8OAAC;4DAAI,WAAU;;;;;;;;;;;;;;;;;;sDAGnB,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;;;;;8DACf,8OAAC;oDAAI,WAAU;;;;;;;;;;;;;mCAXT;;;;4CAeV,sBACF,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAE,WAAU;8CAAsB;;;;;;;;;;uCAEnC,aAAa,UAAU,MAAM,GAAG,IAClC,UAAU,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,SAAc,sBACvC,8OAAC,2IAAA,CAAA,cAAW;oCAAwC,SAAS;oCAAS,OAAO,QAAQ;mCAAnE,QAAQ,UAAU,IAAI,QAAQ,EAAE;;;;0DAGpD,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAE,WAAU;8CAAsB;;;;;;;;;;;;;;;;sCAKzC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;;kDAEV,8OAAC;kDAAK;;;;;;kDACN,8OAAC,kNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOpC", "debugId": null}}]}
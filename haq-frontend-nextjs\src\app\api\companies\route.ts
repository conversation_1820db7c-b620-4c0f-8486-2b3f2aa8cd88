import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';

/**
 * GET /api/companies
 * Public endpoint to list all companies for review form dropdown/search
 * Following HAQ-rules.md PUB-01 specification
 * 
 * Query Parameters:
 * - q: Search query for company name (optional)
 * - page: Page number for pagination (default: 1)
 * - limit: Number of results per page (default: 50, max: 100)
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const searchQuery = searchParams.get('q') || '';
    const page = Math.max(1, parseInt(searchParams.get('page') || '1'));
    const limit = Math.min(100, Math.max(1, parseInt(searchParams.get('limit') || '50')));
    
    // Calculate offset for pagination
    const offset = (page - 1) * limit;

    // Build query for companies
    let query = supabase
      .from('companies')
      .select(`
        company_id,
        name,
        slug,
        industry,
        location,
        website_url,
        logo_url,
        employee_count_range,
        founded_year,
        haq_score,
        total_reviews,
        is_verified
      `)
      .order('name', { ascending: true });

    // Apply search filter if provided
    if (searchQuery.trim()) {
      // Use case-insensitive search on company name
      query = query.ilike('name', `%${searchQuery.trim()}%`);
    }

    // Apply pagination
    query = query.range(offset, offset + limit - 1);

    const { data: companies, error, count } = await query;

    if (error) {
      console.error('Error fetching companies:', error);
      return NextResponse.json(
        { 
          success: false, 
          message: 'Failed to fetch companies',
          error: 'Database query failed'
        },
        { status: 500 }
      );
    }

    // Get total count for pagination metadata
    let totalCount = 0;
    if (searchQuery.trim()) {
      const { count: searchCount } = await supabase
        .from('companies')
        .select('*', { count: 'exact', head: true })
        .ilike('name', `%${searchQuery.trim()}%`);
      totalCount = searchCount || 0;
    } else {
      const { count: allCount } = await supabase
        .from('companies')
        .select('*', { count: 'exact', head: true });
      totalCount = allCount || 0;
    }

    // Return paginated response
    return NextResponse.json({
      success: true,
      data: {
        companies: companies || [],
        pagination: {
          page,
          limit,
          total: totalCount,
          totalPages: Math.ceil(totalCount / limit),
          hasNext: page * limit < totalCount,
          hasPrev: page > 1
        },
        search: searchQuery || null
      }
    }, {
      headers: {
        // Add caching headers as per HAQ-rules.md CDN_SHORT policy
        'Cache-Control': 'public, max-age=300, s-maxage=300', // 5 minutes
        'Vary': 'Accept-Encoding'
      }
    });

  } catch (error) {
    console.error('Companies API error:', error);
    return NextResponse.json(
      { 
        success: false, 
        message: 'Internal server error',
        error: 'Unexpected error occurred'
      },
      { status: 500 }
    );
  }
}

/**
 * Handle unsupported HTTP methods
 */
export async function POST() {
  return NextResponse.json(
    { success: false, message: 'Method not allowed' },
    { status: 405, headers: { 'Allow': 'GET' } }
  );
}

export async function PUT() {
  return NextResponse.json(
    { success: false, message: 'Method not allowed' },
    { status: 405, headers: { 'Allow': 'GET' } }
  );
}

export async function DELETE() {
  return NextResponse.json(
    { success: false, message: 'Method not allowed' },
    { status: 405, headers: { 'Allow': 'GET' } }
  );
}

export async function PATCH() {
  return NextResponse.json(
    { success: false, message: 'Method not allowed' },
    { status: 405, headers: { 'Allow': 'GET' } }
  );
}

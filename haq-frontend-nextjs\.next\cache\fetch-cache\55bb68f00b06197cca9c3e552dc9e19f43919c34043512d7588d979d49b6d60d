{"kind": "FETCH", "data": {"headers": {"cache-control": "public, max-age=300, s-maxage=300", "connection": "keep-alive", "content-type": "application/json", "date": "Mon, 23 Jun 2025 20:39:11 GMT", "keep-alive": "timeout=5", "transfer-encoding": "chunked", "vary": "RSC, Next-Router-State-Tree, Next-Router-Prefetch, Next-Router-Segment-Prefetch, Accept-Encoding", "x-content-type-options": "nosniff", "x-frame-options": "DENY"}, "body": "eyJzdWNjZXNzIjp0cnVlLCJkYXRhIjp7ImNvbXBhbnkiOnsiY29tcGFueV9pZCI6IjU4OGM2Nzc1LTdlNjctNDJjMC04YjYzLTA1NjQ5MTcyNzhiNiIsIm5hbWUiOiJUZWNoRmxvdyBTb2x1dGlvbnMiLCJzbHVnIjoidGVjaGZsb3ctc29sdXRpb25zIiwiaW5kdXN0cnkiOiJUZWNobm9sb2d5IiwibG9jYXRpb24iOiJLYXJhY2hpIiwid2Vic2l0ZV91cmwiOiJodHRwczovL3RlY2hmbG93LnBrIiwibG9nb191cmwiOiJodHRwczovL2ltYWdlcy5wZXhlbHMuY29tL3Bob3Rvcy8zMTg0Mzk4L3BleGVscy1waG90by0zMTg0Mzk4LmpwZWc/YXV0bz1jb21wcmVzcyZjcz10aW55c3JnYiZ3PTEwMCIsImVtcGxveWVlX2NvdW50X3JhbmdlIjoiNTAtMjAwIiwiZm91bmRlZF95ZWFyIjoyMDE4LCJkZXNjcmlwdGlvbiI6IkxlYWRpbmcgc29mdHdhcmUgZGV2ZWxvcG1lbnQgY29tcGFueSBzcGVjaWFsaXppbmcgaW4gd2ViIGFuZCBtb2JpbGUgYXBwbGljYXRpb25zIiwiaGFxX3Njb3JlIjo0LjIsInRvdGFsX3Jldmlld3MiOjAsImlzX3ZlcmlmaWVkIjp0cnVlLCJjcmVhdGVkX2F0IjoiMjAyNS0wNi0yMlQwMjozOTo1NC43ODI1NzgrMDA6MDAiLCJ1cGRhdGVkX2F0IjoiMjAyNS0wNi0yMlQwMjozOTo1NC43ODI1NzgrMDA6MDAiLCJhdmVyYWdlX3JhdGluZyI6MCwicmF0aW5nX2Rpc3RyaWJ1dGlvbiI6eyIxIjowLCIyIjowLCIzIjowLCI0IjowLCI1IjowfX19fQ==", "status": 200, "url": "http://localhost:3000/api/companies/588c6775-7e67-42c0-8b63-0564917278b6"}, "revalidate": 300, "tags": []}
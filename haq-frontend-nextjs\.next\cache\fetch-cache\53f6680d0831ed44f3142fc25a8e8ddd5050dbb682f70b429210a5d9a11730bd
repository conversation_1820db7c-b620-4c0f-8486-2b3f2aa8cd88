{"kind": "FETCH", "data": {"headers": {"cache-control": "public, max-age=300, s-maxage=300", "connection": "keep-alive", "content-type": "application/json", "date": "Mon, 23 Jun 2025 20:39:11 GMT", "keep-alive": "timeout=5", "transfer-encoding": "chunked", "vary": "RSC, Next-Router-State-Tree, Next-Router-Prefetch, Next-Router-Segment-Prefetch, Accept-Encoding", "x-content-type-options": "nosniff", "x-frame-options": "DENY"}, "body": "eyJzdWNjZXNzIjp0cnVlLCJkYXRhIjp7ImNvbXBhbnkiOnsiY29tcGFueV9pZCI6IjUyZjBmN2YyLTE0ZDEtNGU4My04ZGRmLTE4NWY0MDRkM2M1NSIsIm5hbWUiOiJHcmVlblRlY2ggRW5lcmd5Iiwic2x1ZyI6ImdyZWVudGVjaC1lbmVyZ3kiLCJpbmR1c3RyeSI6IkVuZXJneSIsImxvY2F0aW9uIjoiTGFob3JlIiwid2Vic2l0ZV91cmwiOiJodHRwczovL2dyZWVudGVjaC5wayIsImxvZ29fdXJsIjoiaHR0cHM6Ly9pbWFnZXMucGV4ZWxzLmNvbS9waG90b3MvMzE4NDMzOS9wZXhlbHMtcGhvdG8tMzE4NDMzOS5qcGVnP2F1dG89Y29tcHJlc3MmY3M9dGlueXNyZ2Imdz0xMDAiLCJlbXBsb3llZV9jb3VudF9yYW5nZSI6IjEwMC0yMDAiLCJmb3VuZGVkX3llYXIiOjIwMTcsImRlc2NyaXB0aW9uIjoiUmVuZXdhYmxlIGVuZXJneSBzb2x1dGlvbnMgYW5kIHNvbGFyIHBhbmVsIGluc3RhbGxhdGlvbnMiLCJoYXFfc2NvcmUiOjMuOSwidG90YWxfcmV2aWV3cyI6MCwiaXNfdmVyaWZpZWQiOnRydWUsImNyZWF0ZWRfYXQiOiIyMDI1LTA2LTIyVDAyOjM5OjU0Ljc4MjU3OCswMDowMCIsInVwZGF0ZWRfYXQiOiIyMDI1LTA2LTIyVDAyOjM5OjU0Ljc4MjU3OCswMDowMCIsImF2ZXJhZ2VfcmF0aW5nIjowLCJyYXRpbmdfZGlzdHJpYnV0aW9uIjp7IjEiOjAsIjIiOjAsIjMiOjAsIjQiOjAsIjUiOjB9fX19", "status": 200, "url": "http://localhost:3000/api/companies/52f0f7f2-14d1-4e83-8ddf-185f404d3c55"}, "revalidate": 300, "tags": []}
'use client';

import React from 'react';
import AdminLayout from '@/components/admin/AdminLayout';
import { Building2, FileText, Users, TrendingUp } from 'lucide-react';

const AdminDashboard: React.FC = () => {
  // Mock data - in real app this would come from API
  const stats = [
    {
      name: 'Total Companies',
      value: '12',
      change: '+2',
      changeType: 'increase',
      icon: Building2,
    },
    {
      name: 'Pending Reviews',
      value: '8',
      change: '+3',
      changeType: 'increase',
      icon: FileText,
    },
    {
      name: 'Total Users',
      value: '156',
      change: '+12',
      changeType: 'increase',
      icon: Users,
    },
    {
      name: 'Platform Growth',
      value: '24%',
      change: '+5%',
      changeType: 'increase',
      icon: TrendingUp,
    },
  ];

  const recentActivity = [
    {
      id: 1,
      type: 'company',
      message: 'New company "TechFlow Solutions" added',
      time: '2 hours ago',
    },
    {
      id: 2,
      type: 'review',
      message: 'Review submitted for "DataCorp Inc"',
      time: '4 hours ago',
    },
    {
      id: 3,
      type: 'user',
      message: 'New user registration: <EMAIL>',
      time: '6 hours ago',
    },
    {
      id: 4,
      type: 'review',
      message: 'Review approved for "InnovateTech"',
      time: '8 hours ago',
    },
  ];

  return (
    <AdminLayout title="Dashboard">
      <div className="space-y-6">
        {/* Stats Grid */}
        <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
          {stats.map((stat) => (
            <div
              key={stat.name}
              className="relative bg-white pt-5 px-4 pb-12 sm:pt-6 sm:px-6 shadow rounded-lg overflow-hidden"
            >
              <dt>
                <div className="absolute bg-blue-500 rounded-md p-3">
                  <stat.icon className="h-6 w-6 text-white" aria-hidden="true" />
                </div>
                <p className="ml-16 text-sm font-medium text-gray-500 truncate">
                  {stat.name}
                </p>
              </dt>
              <dd className="ml-16 pb-6 flex items-baseline sm:pb-7">
                <p className="text-2xl font-semibold text-gray-900">{stat.value}</p>
                <p
                  className={`ml-2 flex items-baseline text-sm font-semibold ${
                    stat.changeType === 'increase'
                      ? 'text-green-600'
                      : 'text-red-600'
                  }`}
                >
                  {stat.change}
                </p>
              </dd>
            </div>
          ))}
        </div>

        {/* Recent Activity */}
        <div className="bg-white shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
              Recent Activity
            </h3>
            <div className="flow-root">
              <ul className="-mb-8">
                {recentActivity.map((activity, activityIdx) => (
                  <li key={activity.id}>
                    <div className="relative pb-8">
                      {activityIdx !== recentActivity.length - 1 ? (
                        <span
                          className="absolute top-4 left-4 -ml-px h-full w-0.5 bg-gray-200"
                          aria-hidden="true"
                        />
                      ) : null}
                      <div className="relative flex space-x-3">
                        <div>
                          <span
                            className={`h-8 w-8 rounded-full flex items-center justify-center ring-8 ring-white ${
                              activity.type === 'company'
                                ? 'bg-blue-500'
                                : activity.type === 'review'
                                ? 'bg-green-500'
                                : 'bg-purple-500'
                            }`}
                          >
                            {activity.type === 'company' ? (
                              <Building2 className="h-4 w-4 text-white" />
                            ) : activity.type === 'review' ? (
                              <FileText className="h-4 w-4 text-white" />
                            ) : (
                              <Users className="h-4 w-4 text-white" />
                            )}
                          </span>
                        </div>
                        <div className="min-w-0 flex-1 pt-1.5 flex justify-between space-x-4">
                          <div>
                            <p className="text-sm text-gray-500">{activity.message}</p>
                          </div>
                          <div className="text-right text-sm whitespace-nowrap text-gray-500">
                            <time>{activity.time}</time>
                          </div>
                        </div>
                      </div>
                    </div>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="bg-white shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
              Quick Actions
            </h3>
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
              <a
                href="/admin/companies"
                className="relative group bg-white p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-blue-500 border border-gray-200 rounded-lg hover:border-gray-300"
              >
                <div>
                  <span className="rounded-lg inline-flex p-3 bg-blue-50 text-blue-700 ring-4 ring-white">
                    <Building2 className="h-6 w-6" />
                  </span>
                </div>
                <div className="mt-8">
                  <h3 className="text-lg font-medium">
                    <span className="absolute inset-0" aria-hidden="true" />
                    Manage Companies
                  </h3>
                  <p className="mt-2 text-sm text-gray-500">
                    Add, edit, and manage company profiles
                  </p>
                </div>
              </a>

              <a
                href="/admin/moderation"
                className="relative group bg-white p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-blue-500 border border-gray-200 rounded-lg hover:border-gray-300"
              >
                <div>
                  <span className="rounded-lg inline-flex p-3 bg-green-50 text-green-700 ring-4 ring-white">
                    <FileText className="h-6 w-6" />
                  </span>
                </div>
                <div className="mt-8">
                  <h3 className="text-lg font-medium">
                    <span className="absolute inset-0" aria-hidden="true" />
                    Review Moderation Queue
                  </h3>
                  <p className="mt-2 text-sm text-gray-500">
                    Approve or reject pending reviews
                  </p>
                </div>
              </a>

              <a
                href="/admin/users"
                className="relative group bg-white p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-blue-500 border border-gray-200 rounded-lg hover:border-gray-300"
              >
                <div>
                  <span className="rounded-lg inline-flex p-3 bg-purple-50 text-purple-700 ring-4 ring-white">
                    <Users className="h-6 w-6" />
                  </span>
                </div>
                <div className="mt-8">
                  <h3 className="text-lg font-medium">
                    <span className="absolute inset-0" aria-hidden="true" />
                    User Management
                  </h3>
                  <p className="mt-2 text-sm text-gray-500">
                    View and manage user accounts
                  </p>
                </div>
              </a>
            </div>
          </div>
        </div>
      </div>
    </AdminLayout>
  );
};

export default AdminDashboard;

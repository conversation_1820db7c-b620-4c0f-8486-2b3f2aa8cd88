(()=>{var e={};e.id=706,e.ids=[706],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},39727:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},51906:e=>{function r(e){var r=Error("Cannot find module '"+e+"'");throw r.code="MODULE_NOT_FOUND",r}r.keys=()=>[],r.resolve=r,r.id=51906,e.exports=r},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},68104:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>q,routeModule:()=>x,serverHooks:()=>f,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>g});var s={};t.r(s),t.d(s,{DELETE:()=>d,GET:()=>p,PATCH:()=>h,POST:()=>c,PUT:()=>l});var o=t(96559),n=t(48088),a=t(37719),i=t(32190);let u=(0,t(39398).createClient)("https://wqbuilazpyxpwyuwuqpi.supabase.co",process.env.SUPABASE_SERVICE_ROLE_KEY);async function p(e){try{let{searchParams:r}=new URL(e.url),t=r.get("q"),s=r.get("page"),o=r.get("limit");if(!t)return i.NextResponse.json({error:'Search query parameter "q" is required'},{status:400});let n=function(e){if(!e||"string"!=typeof e)throw Error("Search query is required and must be a string");let r=e.trim();if(0===r.length)throw Error("Search query cannot be empty");if(r.length>100)throw Error("Search query is too long (max 100 characters)");return r}(t),{page:a,limit:p}=function(e,r){let t=e?parseInt(e,10):1,s=r?parseInt(r,10):10;if(isNaN(t)||t<1)throw Error("Page must be a positive integer");if(isNaN(s)||s<1||s>50)throw Error("Limit must be between 1 and 50");return{page:t,limit:s}}(s,o),c=(a-1)*p,{data:l,error:d,count:h}=await u.from("companies").select(`
        company_id,
        name,
        industry,
        hq_location,
        website,
        logo_url,
        description,
        haq_score,
        created_at
      `,{count:"exact"}).ilike("name",`%${n}%`).range(c,c+p-1).order("name",{ascending:!0});if(d)return console.error("Database search error:",d),i.NextResponse.json({error:"Failed to search companies"},{status:500});let x=h||0,m=Math.ceil(x/p),g=new Headers({"Content-Type":"application/json","Cache-Control":"public, max-age=60, s-maxage=60",Vary:"Accept-Encoding","X-Content-Type-Options":"nosniff","X-Frame-Options":"DENY"});return new i.NextResponse(JSON.stringify({companies:l||[],pagination:{page:a,limit:p,total:x,totalPages:m,hasNext:a<m,hasPrev:a>1}}),{status:200,headers:g})}catch(e){if(console.error("Search API error:",e),e instanceof Error)return i.NextResponse.json({error:e.message},{status:400});return i.NextResponse.json({error:"Internal server error"},{status:500})}}async function c(){return i.NextResponse.json({error:"Method not allowed"},{status:405,headers:{Allow:"GET"}})}async function l(){return i.NextResponse.json({error:"Method not allowed"},{status:405,headers:{Allow:"GET"}})}async function d(){return i.NextResponse.json({error:"Method not allowed"},{status:405,headers:{Allow:"GET"}})}async function h(){return i.NextResponse.json({error:"Method not allowed"},{status:405,headers:{Allow:"GET"}})}let x=new o.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/search/companies/route",pathname:"/api/search/companies",filename:"route",bundlePath:"app/api/search/companies/route"},resolvedPagePath:"D:\\Haq website v1\\haq-frontend-nextjs\\src\\app\\api\\search\\companies\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:m,workUnitAsyncStorage:g,serverHooks:f}=x;function q(){return(0,a.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:g})}},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[447,580,398],()=>t(68104));module.exports=s})();
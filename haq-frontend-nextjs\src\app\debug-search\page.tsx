'use client'

import React, { useState } from 'react';

export default function DebugSearchPage() {
  const [searchQuery, setSearchQuery] = useState('tech');
  const [results, setResults] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const testSearchAPI = async () => {
    setLoading(true);
    setError(null);
    setResults(null);

    try {
      const response = await fetch(`/api/search/companies?q=${encodeURIComponent(searchQuery)}`);
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${data.error || 'Unknown error'}`);
      }
      
      setResults(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  };

  const testCompaniesAPI = async () => {
    setLoading(true);
    setError(null);
    setResults(null);

    try {
      const response = await fetch(`/api/companies?q=${encodeURIComponent(searchQuery)}&limit=5`);
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${data.error || 'Unknown error'}`);
      }
      
      setResults(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-background-primary p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold text-text-primary mb-8">Search API Debug</h1>
        
        <div className="bg-surface-primary border border-border-primary rounded-lg p-6 mb-6">
          <h2 className="text-xl font-semibold text-text-primary mb-4">Test Search APIs</h2>
          
          <div className="mb-4">
            <label className="block text-sm font-medium text-text-primary mb-2">
              Search Query:
            </label>
            <input
              type="text"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full px-3 py-2 border border-border-primary rounded-lg bg-surface-secondary text-text-primary"
              placeholder="Enter search query..."
            />
          </div>
          
          <div className="flex space-x-4 mb-6">
            <button
              onClick={testSearchAPI}
              disabled={loading}
              className="px-4 py-2 bg-accent-primary text-text-on-accent rounded-lg hover:bg-accent-secondary disabled:opacity-50"
            >
              Test Search API
            </button>
            <button
              onClick={testCompaniesAPI}
              disabled={loading}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
            >
              Test Companies API
            </button>
          </div>
          
          {loading && (
            <div className="text-text-secondary">Loading...</div>
          )}
          
          {error && (
            <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
              <strong>Error:</strong> {error}
            </div>
          )}
          
          {results && (
            <div className="bg-surface-secondary border border-border-primary rounded-lg p-4">
              <h3 className="text-lg font-semibold text-text-primary mb-2">Results:</h3>
              <pre className="text-sm text-text-secondary overflow-auto max-h-96">
                {JSON.stringify(results, null, 2)}
              </pre>
            </div>
          )}
        </div>
        
        <div className="bg-surface-primary border border-border-primary rounded-lg p-6">
          <h2 className="text-xl font-semibold text-text-primary mb-4">API Endpoints</h2>
          <ul className="space-y-2 text-text-secondary">
            <li><strong>Search API:</strong> GET /api/search/companies?q=tech</li>
            <li><strong>Companies API:</strong> GET /api/companies?q=tech&limit=5</li>
          </ul>
        </div>
      </div>
    </div>
  );
}

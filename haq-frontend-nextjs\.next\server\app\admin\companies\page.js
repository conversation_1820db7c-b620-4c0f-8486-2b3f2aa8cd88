(()=>{var e={};e.id=722,e.ids=[722],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10022:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},17313:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("building-2",[["path",{d:"M6 22V4a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v18Z",key:"1b4qmf"}],["path",{d:"M6 12H4a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2h2",key:"i71pzd"}],["path",{d:"M18 9h2a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2h-2",key:"10jefs"}],["path",{d:"M10 6h4",key:"1itunk"}],["path",{d:"M10 10h4",key:"tcdvrf"}],["path",{d:"M10 14h4",key:"kelpxr"}],["path",{d:"M10 18h4",key:"1ulq68"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},24296:(e,s,t)=>{Promise.resolve().then(t.bind(t,93880))},25334:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("external-link",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]])},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},59365:(e,s,t)=>{"use strict";t.d(s,{A:()=>b});var r=t(60687),a=t(43210),n=t.n(a),l=t(85814),i=t.n(l),o=t(16189),d=t(63213);let c=(0,t(62688).A)("layout-dashboard",[["rect",{width:"7",height:"9",x:"3",y:"3",rx:"1",key:"10lvy0"}],["rect",{width:"7",height:"5",x:"14",y:"3",rx:"1",key:"16une8"}],["rect",{width:"7",height:"9",x:"14",y:"12",rx:"1",key:"1hutg5"}],["rect",{width:"7",height:"5",x:"3",y:"16",rx:"1",key:"ldoo1y"}]]);var m=t(17313),u=t(10022),x=t(41312),h=t(84027),p=t(99891),f=t(11860),g=t(40083),y=t(12941);let b=({children:e,title:s="Admin Dashboard"})=>{let[t,l]=(0,a.useState)(!1),{user:b,logout:v}=(0,d.As)(),j=(0,o.useRouter)(),N=b?.role==="admin";n().useEffect(()=>{b&&!N&&j.push("/")},[b,N,j]);let w=async()=>{await v(),j.push("/")},k=[{name:"Dashboard",href:"/admin",icon:c,current:!1},{name:"Companies",href:"/admin/companies",icon:m.A,current:!1},{name:"Reviews",href:"/admin/reviews",icon:u.A,current:!1},{name:"Users",href:"/admin/users",icon:x.A,current:!1},{name:"Settings",href:"/admin/settings",icon:h.A,current:!1}];return N?(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,r.jsxs)("div",{className:`fixed inset-0 z-40 lg:hidden ${t?"block":"hidden"}`,children:[(0,r.jsx)("div",{className:"fixed inset-0 bg-gray-600 bg-opacity-75",onClick:()=>l(!1)}),(0,r.jsxs)("div",{className:"relative flex-1 flex flex-col max-w-xs w-full bg-white",children:[(0,r.jsx)("div",{className:"absolute top-0 right-0 -mr-12 pt-2",children:(0,r.jsx)("button",{type:"button",className:"ml-1 flex items-center justify-center h-10 w-10 rounded-full focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white",onClick:()=>l(!1),children:(0,r.jsx)(f.A,{className:"h-6 w-6 text-white"})})}),(0,r.jsxs)("div",{className:"flex-1 h-0 pt-5 pb-4 overflow-y-auto",children:[(0,r.jsx)("div",{className:"flex-shrink-0 flex items-center px-4",children:(0,r.jsx)("h1",{className:"text-xl font-bold text-gray-900",children:"HAQ Admin"})}),(0,r.jsx)("nav",{className:"mt-5 px-2 space-y-1",children:k.map(e=>(0,r.jsxs)(i(),{href:e.href,className:"group flex items-center px-2 py-2 text-base font-medium rounded-md text-gray-600 hover:bg-gray-50 hover:text-gray-900",children:[(0,r.jsx)(e.icon,{className:"mr-4 h-6 w-6"}),e.name]},e.name))})]}),(0,r.jsx)("div",{className:"flex-shrink-0 flex border-t border-gray-200 p-4",children:(0,r.jsx)("div",{className:"flex items-center",children:(0,r.jsxs)("div",{className:"ml-3",children:[(0,r.jsx)("p",{className:"text-base font-medium text-gray-700",children:b?.username}),(0,r.jsx)("p",{className:"text-sm font-medium text-gray-500",children:"Admin"})]})})})]})]}),(0,r.jsx)("div",{className:"hidden lg:flex lg:w-64 lg:flex-col lg:fixed lg:inset-y-0",children:(0,r.jsxs)("div",{className:"flex-1 flex flex-col min-h-0 border-r border-gray-200 bg-white",children:[(0,r.jsxs)("div",{className:"flex-1 flex flex-col pt-5 pb-4 overflow-y-auto",children:[(0,r.jsx)("div",{className:"flex items-center flex-shrink-0 px-4",children:(0,r.jsx)("h1",{className:"text-xl font-bold text-gray-900",children:"HAQ Admin"})}),(0,r.jsx)("nav",{className:"mt-5 flex-1 px-2 bg-white space-y-1",children:k.map(e=>(0,r.jsxs)(i(),{href:e.href,className:"group flex items-center px-2 py-2 text-sm font-medium rounded-md text-gray-600 hover:bg-gray-50 hover:text-gray-900",children:[(0,r.jsx)(e.icon,{className:"mr-3 h-5 w-5"}),e.name]},e.name))})]}),(0,r.jsx)("div",{className:"flex-shrink-0 flex border-t border-gray-200 p-4",children:(0,r.jsxs)("div",{className:"flex items-center w-full",children:[(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-700",children:b?.username}),(0,r.jsx)("p",{className:"text-xs font-medium text-gray-500",children:"Admin"})]}),(0,r.jsx)("button",{onClick:w,className:"ml-3 flex items-center justify-center h-8 w-8 rounded-full text-gray-400 hover:text-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500",title:"Logout",children:(0,r.jsx)(g.A,{className:"h-4 w-4"})})]})})]})}),(0,r.jsxs)("div",{className:"lg:pl-64 flex flex-col flex-1",children:[(0,r.jsx)("div",{className:"sticky top-0 z-10 lg:hidden pl-1 pt-1 sm:pl-3 sm:pt-3 bg-gray-50",children:(0,r.jsx)("button",{type:"button",className:"-ml-0.5 -mt-0.5 h-12 w-12 inline-flex items-center justify-center rounded-md text-gray-500 hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500",onClick:()=>l(!0),children:(0,r.jsx)(y.A,{className:"h-6 w-6"})})}),(0,r.jsx)("div",{className:"bg-white shadow",children:(0,r.jsx)("div",{className:"px-4 sm:px-6 lg:px-8",children:(0,r.jsx)("div",{className:"py-6",children:(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:s})})})}),(0,r.jsx)("main",{className:"flex-1",children:(0,r.jsx)("div",{className:"py-6",children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:e})})})]})]}):(0,r.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)(p.A,{className:"mx-auto h-12 w-12 text-red-500 mb-4"}),(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-2",children:"Access Denied"}),(0,r.jsx)("p",{className:"text-gray-600 mb-4",children:"You don't have permission to access this area."}),(0,r.jsx)(i(),{href:"/",className:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700",children:"Return to Home"})]})})}},60744:(e,s,t)=>{Promise.resolve().then(t.bind(t,83312))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70440:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});var r=t(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83312:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\Haq website v1\\\\haq-frontend-nextjs\\\\src\\\\app\\\\admin\\\\companies\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Haq website v1\\haq-frontend-nextjs\\src\\app\\admin\\companies\\page.tsx","default")},91645:e=>{"use strict";e.exports=require("net")},93880:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>x});var r=t(60687),a=t(43210),n=t(59365),l=t(62688);let i=(0,l.A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]);var o=t(99270),d=t(17313),c=t(25334);let m=(0,l.A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]]),u=(0,l.A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]]),x=()=>{let[e,s]=(0,a.useState)([]),[t,l]=(0,a.useState)(!0),[x,h]=(0,a.useState)(""),[p,f]=(0,a.useState)(""),[g,y]=(0,a.useState)(!1),[b,v]=(0,a.useState)({name:"",industry:"",hq_location:"",description:"",website_url:"",logo_url:"",employee_count_range:"",founded_year:""}),[j,N]=(0,a.useState)(!1),[w,k]=(0,a.useState)("");(0,a.useEffect)(()=>{_()},[]);let _=async()=>{try{l(!0);let e=await fetch("/api/admin/companies",{credentials:"include"});if(!e.ok)throw Error("Failed to fetch companies");let t=await e.json();t.success?s(t.data.companies):h(t.message||"Failed to load companies")}catch(e){h("Error loading companies"),console.error("Fetch companies error:",e)}finally{l(!1)}},q=async e=>{e.preventDefault(),N(!0),k("");try{let e={...b,founded_year:b.founded_year?parseInt(b.founded_year):void 0},s=await fetch("/api/admin/companies",{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify(e)}),t=await s.json();t.success?(v({name:"",industry:"",hq_location:"",description:"",website_url:"",logo_url:"",employee_count_range:"",founded_year:""}),y(!1),await _()):k(t.message||"Failed to create company")}catch(e){k("Error creating company"),console.error("Create company error:",e)}finally{N(!1)}},A=e.filter(e=>e.name.toLowerCase().includes(p.toLowerCase())||e.industry&&e.industry.toLowerCase().includes(p.toLowerCase()));return(0,r.jsxs)(n.A,{title:"Company Management",children:[(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Companies"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Manage company profiles and information"})]}),(0,r.jsxs)("button",{onClick:()=>y(!0),className:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",children:[(0,r.jsx)(i,{className:"h-4 w-4 mr-2"}),"Add Company"]})]}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,r.jsx)(o.A,{className:"h-5 w-5 text-gray-400"})}),(0,r.jsx)("input",{type:"text",placeholder:"Search companies...",value:p,onChange:e=>f(e.target.value),className:"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500"})]}),x&&(0,r.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-md p-4",children:(0,r.jsx)("p",{className:"text-red-800",children:x})}),t?(0,r.jsxs)("div",{className:"text-center py-12",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"}),(0,r.jsx)("p",{className:"mt-4 text-gray-600",children:"Loading companies..."})]}):(0,r.jsx)("div",{className:"bg-white shadow overflow-hidden sm:rounded-md",children:(0,r.jsx)("ul",{className:"divide-y divide-gray-200",children:0===A.length?(0,r.jsxs)("li",{className:"px-6 py-12 text-center",children:[(0,r.jsx)(d.A,{className:"mx-auto h-12 w-12 text-gray-400"}),(0,r.jsx)("h3",{className:"mt-2 text-sm font-medium text-gray-900",children:"No companies"}),(0,r.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:p?"No companies match your search.":"Get started by adding a new company."})]}):A.map(e=>(0,r.jsx)("li",{children:(0,r.jsxs)("div",{className:"px-6 py-4 flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"flex-shrink-0 h-12 w-12",children:e.logo_url?(0,r.jsx)("img",{className:"h-12 w-12 rounded-lg object-cover",src:e.logo_url,alt:e.name}):(0,r.jsx)("div",{className:"h-12 w-12 rounded-lg bg-gray-200 flex items-center justify-center",children:(0,r.jsx)(d.A,{className:"h-6 w-6 text-gray-400"})})}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:e.name}),e.is_verified&&(0,r.jsx)("span",{className:"ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800",children:"Verified"})]}),(0,r.jsxs)("div",{className:"text-sm text-gray-500",children:[e.industry&&(0,r.jsx)("span",{children:e.industry}),e.industry&&e.location&&(0,r.jsx)("span",{children:" • "}),e.location&&(0,r.jsx)("span",{children:e.location})]}),(0,r.jsxs)("div",{className:"text-sm text-gray-500",children:["HAQ Score: ",e.haq_score," • ",e.total_reviews," reviews"]})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[e.website_url&&(0,r.jsx)("a",{href:e.website_url,target:"_blank",rel:"noopener noreferrer",className:"text-gray-400 hover:text-gray-600",children:(0,r.jsx)(c.A,{className:"h-4 w-4"})}),(0,r.jsx)("button",{className:"text-gray-400 hover:text-gray-600",children:(0,r.jsx)(m,{className:"h-4 w-4"})}),(0,r.jsx)("button",{className:"text-gray-400 hover:text-red-600",children:(0,r.jsx)(u,{className:"h-4 w-4"})})]})]})},e.company_id))})})]}),g&&(0,r.jsx)("div",{className:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50",children:(0,r.jsx)("div",{className:"relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white",children:(0,r.jsxs)("div",{className:"mt-3",children:[(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Add New Company"}),w&&(0,r.jsx)("div",{className:"mb-4 bg-red-50 border border-red-200 rounded-md p-4",children:(0,r.jsx)("p",{className:"text-red-800",children:w})}),(0,r.jsxs)("form",{onSubmit:q,className:"space-y-4",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Company Name *"}),(0,r.jsx)("input",{type:"text",required:!0,value:b.name,onChange:e=>v({...b,name:e.target.value}),className:"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Industry"}),(0,r.jsx)("input",{type:"text",value:b.industry,onChange:e=>v({...b,industry:e.target.value}),className:"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Location"}),(0,r.jsx)("input",{type:"text",value:b.hq_location,onChange:e=>v({...b,hq_location:e.target.value}),className:"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Founded Year"}),(0,r.jsx)("input",{type:"number",min:"1800",max:new Date().getFullYear(),value:b.founded_year,onChange:e=>v({...b,founded_year:e.target.value}),className:"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Description"}),(0,r.jsx)("textarea",{rows:3,value:b.description,onChange:e=>v({...b,description:e.target.value}),className:"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Website URL"}),(0,r.jsx)("input",{type:"url",value:b.website_url,onChange:e=>v({...b,website_url:e.target.value}),className:"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Logo URL"}),(0,r.jsx)("input",{type:"url",value:b.logo_url,onChange:e=>v({...b,logo_url:e.target.value}),className:"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Employee Count Range"}),(0,r.jsxs)("select",{value:b.employee_count_range,onChange:e=>v({...b,employee_count_range:e.target.value}),className:"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500",children:[(0,r.jsx)("option",{value:"",children:"Select range"}),(0,r.jsx)("option",{value:"1-10",children:"1-10"}),(0,r.jsx)("option",{value:"11-50",children:"11-50"}),(0,r.jsx)("option",{value:"51-200",children:"51-200"}),(0,r.jsx)("option",{value:"201-500",children:"201-500"}),(0,r.jsx)("option",{value:"501-1000",children:"501-1000"}),(0,r.jsx)("option",{value:"1000+",children:"1000+"})]})]}),(0,r.jsxs)("div",{className:"flex justify-end space-x-3 pt-4",children:[(0,r.jsx)("button",{type:"button",onClick:()=>y(!1),className:"px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",children:"Cancel"}),(0,r.jsx)("button",{type:"submit",disabled:j,className:"px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50",children:j?"Creating...":"Create Company"})]})]})]})})})]})}},94735:e=>{"use strict";e.exports=require("events")},96486:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>l.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>u,tree:()=>d});var r=t(65239),a=t(48088),n=t(88170),l=t.n(n),i=t(30893),o={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>i[e]);t.d(s,o);let d={children:["",{children:["admin",{children:["companies",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,83312)),"D:\\Haq website v1\\haq-frontend-nextjs\\src\\app\\admin\\companies\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,82893)),"D:\\Haq website v1\\haq-frontend-nextjs\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["D:\\Haq website v1\\haq-frontend-nextjs\\src\\app\\admin\\companies\\page.tsx"],m={require:t,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/admin/companies/page",pathname:"/admin/companies",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},99270:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])}};var s=require("../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[447,942,658,647],()=>t(96486));module.exports=r})();
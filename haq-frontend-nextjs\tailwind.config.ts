import type { Config } from 'tailwindcss'

const config: Config = {
  content: [
    './src/pages/**/*.{js,ts,jsx,tsx,mdx}',
    './src/components/**/*.{js,ts,jsx,tsx,mdx}',
    './src/app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {
      colors: {
        // Phoenix Energy UI Design System Colors
        background: {
          primary: '#1A1C20',
          secondary: '#25282D',
        },
        surface: {
          primary: '#25282D',
          secondary: '#3E4149',
        },
        accent: {
          primary: '#FF6B00',
          secondary: '#FFA500',
        },
        text: {
          primary: '#EAEAEA',
          secondary: '#8A8F98',
          'on-accent': '#FFFFFF',
        },
        border: {
          primary: '#3A3D44',
        },
        // Keep some original colors for specific use cases
        primary: {
          50: '#fff7ed',
          100: '#ffedd5',
          200: '#fed7aa',
          300: '#fdba74',
          400: '#fb923c',
          500: '#FF6B00',
          600: '#ea580c',
          700: '#c2410c',
          800: '#9a3412',
          900: '#7c2d12',
        },
        secondary: {
          50: '#fff7ed',
          100: '#ffedd5',
          200: '#fed7aa',
          300: '#fdba74',
          400: '#fb923c',
          500: '#FFA500',
          600: '#ea580c',
          700: '#c2410c',
          800: '#9a3412',
          900: '#7c2d12',
        },
        warning: {
          50: '#fefce8',
          100: '#fef9c3',
          200: '#fef08a',
          300: '#fde047',
          400: '#facc15',
          500: '#eab308',
          600: '#ca8a04',
          700: '#a16207',
          800: '#854d0e',
          900: '#713f12',
        },
        danger: {
          50: '#fef2f2',
          100: '#fee2e2',
          200: '#fecaca',
          300: '#fca5a5',
          400: '#f87171',
          500: '#ef4444',
          600: '#dc2626',
          700: '#b91c1c',
          800: '#991b1b',
          900: '#7f1d1d',
        }
      },
      fontFamily: {
        'sora': ['Sora', 'system-ui', '-apple-system', 'sans-serif'],
        'urdu': ['Noto Nastaliq Urdu', 'serif'],
        'sans': ['Sora', 'Inter', 'system-ui', '-apple-system', 'sans-serif'],
      },
      fontSize: {
        'display-title': ['52px', { lineHeight: '1.2', letterSpacing: '1.5px', fontWeight: '700' }],
        'module-header': ['18px', { lineHeight: '1.4', letterSpacing: '1.5px', fontWeight: '600' }],
        'data-readout': ['30px', { lineHeight: '1.3', fontWeight: '500' }],
        'body-label': ['14px', { lineHeight: '1.5', fontWeight: '400' }],
        'navigation-link': ['14px', { lineHeight: '1.4', letterSpacing: '0.5px', fontWeight: '500' }],
      },
      spacing: {
        'unit': '8px',
        '2unit': '16px',
        '3unit': '24px',
        '4unit': '32px',
        '5unit': '40px',
        '6unit': '48px',
        '8unit': '64px',
        '10unit': '80px',
      },
      borderRadius: {
        'medium': '12px',
        'pill': '9999px',
      },
      animation: {
        'fade-in': 'fadeIn 0.5s ease-in-out',
        'slide-up': 'slideUp 0.3s ease-out',
        'pulse-soft': 'pulseSoft 2s cubic-bezier(0.4, 0, 0.6, 1) infinite',
        'glow': 'glow 0.3s ease-in-out',
      },
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        slideUp: {
          '0%': { transform: 'translateY(10px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
        pulseSoft: {
          '0%, 100%': { opacity: '1' },
          '50%': { opacity: '0.8' },
        },
        glow: {
          '0%': { boxShadow: '0 0 5px rgba(255, 107, 0, 0.2)' },
          '100%': { boxShadow: '0 0 20px 7px rgba(255, 107, 0, 0.4)' },
        },
      },
      boxShadow: {
        'glow': '0 0 15px 5px rgba(255, 107, 0, 0.4)',
        'glow-lg': '0 0 20px 7px rgba(255, 107, 0, 0.5)',
      },
    },
  },
  plugins: [],
}

export default config

import React from 'react';
import { useRouter } from 'next/navigation';
import { Search } from 'lucide-react';

interface SearchBarProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  onSubmit?: () => void;
  autoNavigate?: boolean; // New prop to enable automatic navigation to search results
}

export const SearchBar: React.FC<SearchBarProps> = ({
  value,
  onChange,
  placeholder = "Search companies...",
  onSubmit,
  autoNavigate = false
}) => {
  const router = useRouter();

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // If custom onSubmit is provided, use it
    if (onSubmit) {
      onSubmit();
      return;
    }

    // If autoNavigate is enabled and there's a search query, navigate to search page
    if (autoNavigate && value.trim()) {
      const searchParams = new URLSearchParams();
      searchParams.set('q', value.trim());
      router.push(`/search?${searchParams.toString()}`);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="relative">
      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
        <Search className="h-5 w-5 text-text-secondary" />
      </div>
      <input
        type="text"
        value={value}
        onChange={(e) => onChange(e.target.value)}
        className={`block w-full pl-10 ${autoNavigate ? 'pr-12' : 'pr-4'} py-3 border border-border-primary rounded-medium bg-surface-primary placeholder-text-secondary focus:outline-none focus:ring-2 focus:ring-accent-primary focus:border-accent-primary text-text-primary transition-all duration-200`}
        placeholder={placeholder}
      />
      {autoNavigate && (
        <button
          type="submit"
          className="absolute inset-y-0 right-0 pr-3 flex items-center text-text-secondary hover:text-accent-primary transition-colors duration-200 bg-accent-primary hover:bg-accent-secondary rounded-r-medium px-4"
          aria-label="Search"
        >
          <Search className="h-5 w-5 text-text-on-accent" />
        </button>
      )}
    </form>
  );
};

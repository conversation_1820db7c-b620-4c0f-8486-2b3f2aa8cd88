'use client'

import React, { useState, useEffect, useRef } from 'react';
import { useRouter } from 'next/navigation';
import { Search, X } from 'lucide-react';
import Link from 'next/link';

interface Company {
  company_id: string;
  name: string;
  industry: string;
  location: string;
}

interface SearchBarProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  onSubmit?: () => void;
  autoNavigate?: boolean;
  showSuggestions?: boolean; // New prop to enable search suggestions
}

export const SearchBar: React.FC<SearchBarProps> = ({
  value,
  onChange,
  placeholder = "Search companies...",
  onSubmit,
  autoNavigate = false,
  showSuggestions = false
}) => {
  const router = useRouter();
  const [suggestions, setSuggestions] = useState<Company[]>([]);
  const [showDropdown, setShowDropdown] = useState(false);
  const [loading, setLoading] = useState(false);
  const searchRef = useRef<HTMLDivElement>(null);

  // Fetch suggestions when user types
  useEffect(() => {
    if (!showSuggestions || !value.trim() || value.length < 2) {
      setSuggestions([]);
      setShowDropdown(false);
      return;
    }

    const fetchSuggestions = async () => {
      setLoading(true);
      try {
        const response = await fetch(`/api/companies?q=${encodeURIComponent(value.trim())}&limit=5`);
        const data = await response.json();

        if (data.success && data.data?.companies) {
          setSuggestions(data.data.companies);
          setShowDropdown(true);
        }
      } catch (error) {
        console.error('Error fetching suggestions:', error);
        setSuggestions([]);
      } finally {
        setLoading(false);
      }
    };

    const timeoutId = setTimeout(fetchSuggestions, 300); // Debounce
    return () => clearTimeout(timeoutId);
  }, [value, showSuggestions]);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (searchRef.current && !searchRef.current.contains(event.target as Node)) {
        setShowDropdown(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log('SearchBar: Form submitted with value:', value);
    console.log('SearchBar: autoNavigate:', autoNavigate);

    setShowDropdown(false);

    // If custom onSubmit is provided, use it
    if (onSubmit) {
      console.log('SearchBar: Using custom onSubmit');
      onSubmit();
      return;
    }

    // If autoNavigate is enabled and there's a search query, navigate to search page
    if (autoNavigate && value.trim()) {
      console.log('SearchBar: Navigating to search page...');
      const searchParams = new URLSearchParams();
      searchParams.set('q', value.trim());
      const url = `/search?${searchParams.toString()}`;
      console.log('SearchBar: Navigation URL:', url);
      router.push(url);
    } else {
      console.log('SearchBar: No navigation - autoNavigate:', autoNavigate, 'value:', value);
    }
  };

  const handleSuggestionClick = (company: Company) => {
    setShowDropdown(false);
    router.push(`/companies/${company.company_id}`);
  };

  const clearSearch = () => {
    onChange('');
    setShowDropdown(false);
    setSuggestions([]);
  };

  return (
    <div ref={searchRef} className="relative w-full">
      <form onSubmit={handleSubmit} className="relative">
        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <Search className="h-5 w-5 text-text-secondary" />
        </div>
        <input
          type="text"
          value={value}
          onChange={(e) => onChange(e.target.value)}
          className="block w-full pl-10 pr-20 py-3 border border-border-primary rounded-medium bg-surface-primary placeholder-text-secondary focus:outline-none focus:ring-2 focus:ring-accent-primary focus:border-accent-primary text-text-primary transition-all duration-200"
          placeholder={placeholder}
          autoComplete="off"
        />

        {/* Clear button */}
        {value && (
          <button
            type="button"
            onClick={clearSearch}
            className="absolute inset-y-0 right-12 flex items-center text-text-secondary hover:text-text-primary transition-colors duration-200"
            aria-label="Clear search"
          >
            <X className="h-4 w-4" />
          </button>
        )}

        {/* Search button */}
        <button
          type="submit"
          className="absolute inset-y-0 right-0 flex items-center justify-center w-12 bg-accent-primary hover:bg-accent-secondary text-text-on-accent rounded-r-medium transition-colors duration-200"
          aria-label="Search"
          onClick={(e) => {
            console.log('SearchBar: Button clicked');
            // Let the form handle the submission
          }}
        >
          <Search className="h-5 w-5" />
        </button>
      </form>

      {/* Search suggestions dropdown */}
      {showSuggestions && showDropdown && (
        <div className="absolute top-full left-0 right-0 mt-1 bg-surface-primary border border-border-primary rounded-medium shadow-lg z-50 max-h-80 overflow-y-auto">
          {loading && (
            <div className="p-4 text-center text-text-secondary">
              <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-accent-primary mx-auto"></div>
              <span className="ml-2">Searching...</span>
            </div>
          )}

          {!loading && suggestions.length === 0 && value.trim().length >= 2 && (
            <div className="p-4 text-center text-text-secondary">
              No companies found for "{value}"
            </div>
          )}

          {!loading && suggestions.length > 0 && (
            <>
              {suggestions.map((company) => (
                <button
                  key={company.company_id}
                  onClick={() => handleSuggestionClick(company)}
                  className="w-full text-left p-3 hover:bg-surface-secondary transition-colors duration-200 border-b border-border-primary last:border-b-0"
                >
                  <div className="font-medium text-text-primary">{company.name}</div>
                  <div className="text-sm text-text-secondary">
                    {company.industry} • {company.location}
                  </div>
                </button>
              ))}

              {/* View all results link */}
              <Link
                href={`/search?q=${encodeURIComponent(value.trim())}`}
                className="block w-full text-center p-3 text-accent-primary hover:bg-surface-secondary transition-colors duration-200 font-medium"
                onClick={() => setShowDropdown(false)}
              >
                View all results for "{value}"
              </Link>
            </>
          )}
        </div>
      )}
    </div>
  );
};

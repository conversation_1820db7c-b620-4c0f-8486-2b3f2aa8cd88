(()=>{var e={};e.id=377,e.ids=[377],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8919:(e,s,t)=>{"use strict";t.d(s,{I:()=>g});var r=t(60687),a=t(43210),i=t(85814),n=t.n(i),l=t(30474),o=t(97992),c=t(64398),d=t(41312),m=t(5336),x=t(43649);class p extends a.Component{static getDerivedStateFromError(e){return{hasError:!0,error:e}}componentDidCatch(e,s){console.error("ErrorBoundary caught an error:",e,s)}render(){return this.state.hasError?this.props.fallback||(0,r.jsx)("div",{className:"w-full h-full bg-surface-secondary rounded-lg flex items-center justify-center",children:(0,r.jsx)("div",{className:"text-text-secondary text-sm",children:"Unable to load"})}):this.props.children}constructor(...e){super(...e),this.state={hasError:!1}}}let u=({children:e})=>(0,r.jsx)(p,{fallback:(0,r.jsx)("div",{className:"w-full h-full bg-surface-secondary rounded-lg flex items-center justify-center",children:(0,r.jsx)("div",{className:"w-6 h-6 bg-surface-tertiary rounded"})}),children:e}),h=({src:e,alt:s,className:t})=>{let[i,n]=(0,a.useState)(!1),[o,c]=(0,a.useState)(!1),[d,m]=(0,a.useState)(e);return((0,a.useEffect)(()=>{n(!0),m(e),c(!1)},[e]),i)?o&&"/placeholder-company.svg"===d?(0,r.jsx)("div",{className:"w-full h-full bg-surface-secondary rounded-lg flex items-center justify-center",children:(0,r.jsx)("div",{className:"w-6 h-6 bg-surface-tertiary rounded"})}):(0,r.jsx)(l.default,{src:d,alt:s,fill:!0,className:t,sizes:"48px",priority:!1,onError:()=>{console.warn(`Image failed to load: ${d}`),c(!0),m("/placeholder-company.svg")},placeholder:"blur",blurDataURL:"data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAhEAACAQMDBQAAAAAAAAAAAAABAgMABAUGIWGRkqGx0f/EABUBAQEAAAAAAAAAAAAAAAAAAAMF/8QAGhEAAgIDAAAAAAAAAAAAAAAAAAECEgMRkf/aAAwDAQACEQMRAD8AltJagyeH0AthI5xdrLcNM91BF5pX2HaH9bcfaSXWGaRmknyJckliyjqTzSlT54b6bk+h0R+Kcp/9k="}):(0,r.jsx)("div",{className:"w-full h-full bg-surface-secondary animate-pulse rounded-lg flex items-center justify-center",children:(0,r.jsx)("div",{className:"w-6 h-6 bg-surface-tertiary rounded"})})},g=({company:e,delay:s=0})=>{let t=e=>e>=4?"text-green-400":e>=3?"text-yellow-400":"text-red-400",a=e.company_id||e.id,i=`/companies/${a}`;return(0,r.jsx)(n(),{href:i,children:(0,r.jsxs)("div",{className:"bg-surface-primary border border-border-primary rounded-medium p-6 hover:border-accent-primary transition-all duration-200 animate-slide-up group cursor-pointer",style:{animationDelay:`${s}ms`},children:[(0,r.jsxs)("div",{className:"flex items-start space-x-4 mb-4",children:[(0,r.jsx)("div",{className:"relative w-12 h-12 rounded-lg overflow-hidden bg-surface-secondary flex-shrink-0",children:(0,r.jsx)(u,{children:(0,r.jsx)(h,{src:e.logo_url||"/placeholder-company.svg",alt:`${e.name} logo`,className:"object-cover"})})}),(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-text-primary group-hover:text-accent-primary transition-colors duration-200 truncate",children:e.name}),(0,r.jsxs)("div",{className:"flex items-center space-x-4 text-sm text-text-secondary",children:[e.industry&&(0,r.jsx)("span",{children:e.industry}),e.location&&(0,r.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,r.jsx)(o.A,{className:"w-3 h-3"}),(0,r.jsx)("span",{children:e.location})]})]})]})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("div",{className:`px-3 py-1 rounded-lg ${(e=>e>=4?"bg-green-400/20":e>=3?"bg-yellow-400/20":"bg-red-400/20")(e.haq_score||0)}`,children:(0,r.jsx)("span",{className:`font-bold ${t(e.haq_score||0)}`,children:(e.haq_score||0).toFixed(1)})}),(0,r.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,r.jsx)(c.A,{className:`w-4 h-4 ${t(e.haq_score||0)}`,fill:"currentColor"}),(0,r.jsx)("span",{className:"text-text-secondary text-sm",children:"Haq Score"})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-1 text-text-secondary text-sm",children:[(0,r.jsx)(d.A,{className:"w-4 h-4"}),(0,r.jsxs)("span",{children:[e.total_reviews||0," reviews"]})]})]}),(0,r.jsxs)("div",{className:"space-y-3",children:[e.greenFlags&&e.greenFlags.length>0&&(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[(0,r.jsx)(m.A,{className:"w-4 h-4 text-green-400"}),(0,r.jsx)("span",{className:"text-sm font-medium text-text-primary",children:"Positives"})]}),(0,r.jsxs)("div",{className:"flex flex-wrap gap-1",children:[e.greenFlags.slice(0,2).map((e,s)=>(0,r.jsx)("span",{className:"px-2 py-1 bg-green-400/20 text-green-400 text-xs rounded-lg",children:e},s)),e.greenFlags.length>2&&(0,r.jsxs)("span",{className:"px-2 py-1 bg-surface-secondary text-text-secondary text-xs rounded-lg",children:["+",e.greenFlags.length-2," more"]})]})]}),e.redFlags&&e.redFlags.length>0&&(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[(0,r.jsx)(x.A,{className:"w-4 h-4 text-red-400"}),(0,r.jsx)("span",{className:"text-sm font-medium text-text-primary",children:"Issues"})]}),(0,r.jsxs)("div",{className:"flex flex-wrap gap-1",children:[e.redFlags.slice(0,2).map((e,s)=>(0,r.jsx)("span",{className:"px-2 py-1 bg-red-400/20 text-red-400 text-xs rounded-lg",children:e},s)),e.redFlags.length>2&&(0,r.jsxs)("span",{className:"px-2 py-1 bg-surface-secondary text-text-secondary text-xs rounded-lg",children:["+",e.redFlags.length-2," more"]})]})]})]}),(0,r.jsx)("div",{className:"mt-4 pt-4 border-t border-border-primary",children:(0,r.jsx)("span",{className:"text-accent-primary text-sm font-medium group-hover:underline",children:"View Details →"})})]})})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},14952:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},17313:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("building-2",[["path",{d:"M6 22V4a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v18Z",key:"1b4qmf"}],["path",{d:"M6 12H4a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2h2",key:"i71pzd"}],["path",{d:"M18 9h2a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2h-2",key:"10jefs"}],["path",{d:"M10 6h4",key:"1itunk"}],["path",{d:"M10 10h4",key:"tcdvrf"}],["path",{d:"M10 14h4",key:"kelpxr"}],["path",{d:"M10 18h4",key:"1ulq68"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},24492:(e,s,t)=>{Promise.resolve().then(t.bind(t,65028))},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32066:(e,s,t)=>{"use strict";t.d(s,{default:()=>x});var r=t(60687),a=t(43210),i=t(16189),n=t(47033),l=t(14952),o=t(99270),c=t(80462),d=t(17313),m=t(8919);let x=()=>{let e=(0,i.useRouter)(),s=(0,i.useSearchParams)(),[t,x]=(0,a.useState)(null),[p,u]=(0,a.useState)(!0),[h,g]=(0,a.useState)(s.get("q")||""),[f,b]=(0,a.useState)(!1),A=parseInt(s.get("page")||"1"),y=s.get("q")||"",v=async(e=1,s="")=>{u(!0);try{let t=new URLSearchParams;t.append("page",e.toString()),t.append("limit","12"),s.trim()&&t.append("q",s.trim());let r=await fetch(`/api/companies?${t}`),a=await r.json();a.success?x(a.data):(console.error("Failed to fetch companies:",a.message),x(null))}catch(e){console.error("Error fetching companies:",e),x(null)}finally{u(!1)}};(0,a.useEffect)(()=>{v(A,y)},[A,y]),(0,a.useEffect)(()=>{g(y)},[y]);let j=t=>{let r=new URLSearchParams(s);r.set("page",t.toString()),e.push(`/companies?${r.toString()}`)};return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm p-6",children:[(0,r.jsx)("form",{onSubmit:t=>{t.preventDefault();let r=new URLSearchParams(s);h.trim()?r.set("q",h.trim()):r.delete("q"),r.delete("page"),e.push(`/companies?${r.toString()}`)},className:"mb-4",children:(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(o.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400"}),(0,r.jsx)("input",{type:"text",placeholder:"Search companies by name or industry...",value:h,onChange:e=>g(e.target.value),className:"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]})}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("button",{onClick:()=>b(!f),className:"flex items-center space-x-2 px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors",children:[(0,r.jsx)(c.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{children:"Filters"})]}),t&&(0,r.jsxs)("div",{className:"text-sm text-gray-600",children:[t.pagination.total," companies found",t.search&&(0,r.jsxs)("span",{children:[' for "',t.search,'"']})]})]})]}),p?(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:Array.from({length:6}).map((e,s)=>(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm p-6 animate-pulse",children:[(0,r.jsxs)("div",{className:"flex items-start space-x-4 mb-4",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-gray-200 rounded-lg"}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded w-3/4 mb-2"}),(0,r.jsx)("div",{className:"h-3 bg-gray-200 rounded w-1/2"})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("div",{className:"h-3 bg-gray-200 rounded w-full"}),(0,r.jsx)("div",{className:"h-3 bg-gray-200 rounded w-2/3"})]})]},s))}):t&&t.companies.length>0?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:t.companies.map((e,s)=>(0,r.jsx)(m.I,{company:e,delay:50*s},e.company_id))}),(()=>{if(!t?.pagination||t.pagination.totalPages<=1)return null;let{pagination:e}=t,s=[],a=Math.max(1,e.page-Math.floor(2.5)),i=Math.min(e.totalPages,a+5-1);i-a+1<5&&(a=Math.max(1,i-5+1));for(let e=a;e<=i;e++)s.push(e);return(0,r.jsxs)("div",{className:"flex items-center justify-between border-t border-gray-200 bg-white px-4 py-3 sm:px-6 mt-8",children:[(0,r.jsxs)("div",{className:"flex flex-1 justify-between sm:hidden",children:[(0,r.jsx)("button",{onClick:()=>j(e.page-1),disabled:!e.hasPrev,className:"relative inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed",children:"Previous"}),(0,r.jsx)("button",{onClick:()=>j(e.page+1),disabled:!e.hasNext,className:"relative ml-3 inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed",children:"Next"})]}),(0,r.jsxs)("div",{className:"hidden sm:flex sm:flex-1 sm:items-center sm:justify-between",children:[(0,r.jsx)("div",{children:(0,r.jsxs)("p",{className:"text-sm text-gray-700",children:["Showing"," ",(0,r.jsx)("span",{className:"font-medium",children:(e.page-1)*e.limit+1})," ","to"," ",(0,r.jsx)("span",{className:"font-medium",children:Math.min(e.page*e.limit,e.total)})," ","of"," ",(0,r.jsx)("span",{className:"font-medium",children:e.total})," results"]})}),(0,r.jsx)("div",{children:(0,r.jsxs)("nav",{className:"isolate inline-flex -space-x-px rounded-md shadow-sm","aria-label":"Pagination",children:[(0,r.jsx)("button",{onClick:()=>j(e.page-1),disabled:!e.hasPrev,className:"relative inline-flex items-center rounded-l-md px-2 py-2 text-gray-400 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0 disabled:opacity-50 disabled:cursor-not-allowed",children:(0,r.jsx)(n.A,{className:"h-5 w-5"})}),s.map(s=>(0,r.jsx)("button",{onClick:()=>j(s),className:`relative inline-flex items-center px-4 py-2 text-sm font-semibold ${s===e.page?"z-10 bg-blue-600 text-white focus:z-20 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-blue-600":"text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0"}`,children:s},s)),(0,r.jsx)("button",{onClick:()=>j(e.page+1),disabled:!e.hasNext,className:"relative inline-flex items-center rounded-r-md px-2 py-2 text-gray-400 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0 disabled:opacity-50 disabled:cursor-not-allowed",children:(0,r.jsx)(l.A,{className:"h-5 w-5"})})]})})]})]})})()]}):(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm p-12 text-center",children:[(0,r.jsx)(d.A,{className:"mx-auto h-12 w-12 text-gray-400 mb-4"}),(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No companies found"}),(0,r.jsx)("p",{className:"text-gray-600 mb-6",children:t?.search?`No companies match your search for "${t.search}".`:"No companies are available at the moment."}),t?.search&&(0,r.jsx)("button",{onClick:()=>{g(""),e.push("/companies")},className:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700",children:"Clear Search"})]})]})}},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},47033:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("chevron-left",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]])},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65028:(e,s,t)=>{"use strict";t.d(s,{default:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\Haq website v1\\\\haq-frontend-nextjs\\\\src\\\\components\\\\companies\\\\CompaniesListClient.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Haq website v1\\haq-frontend-nextjs\\src\\components\\companies\\CompaniesListClient.tsx","default")},70440:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});var r=t(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},80462:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]])},81630:e=>{"use strict";e.exports=require("http")},84904:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>i,metadata:()=>n});var r=t(37413);t(61120);var a=t(65028);function i(){return(0,r.jsx)("div",{className:"min-h-screen bg-gray-50",children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,r.jsxs)("div",{className:"text-center mb-8",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900 sm:text-4xl",children:"Browse Companies"}),(0,r.jsx)("p",{className:"mt-4 text-lg text-gray-600 max-w-2xl mx-auto",children:"Discover companies and read authentic employee reviews to make informed career decisions."})]}),(0,r.jsx)(a.default,{})]})})}let n={title:"Browse Companies - HAQ",description:"Browse companies and read employee reviews to make informed career decisions. Find the best workplaces in Pakistan.",openGraph:{title:"Browse Companies - HAQ",description:"Browse companies and read employee reviews to make informed career decisions."}}},87218:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>n.a,__next_app__:()=>m,pages:()=>d,routeModule:()=>x,tree:()=>c});var r=t(65239),a=t(48088),i=t(88170),n=t.n(i),l=t(30893),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);t.d(s,o);let c={children:["",{children:["companies",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,84904)),"D:\\Haq website v1\\haq-frontend-nextjs\\src\\app\\companies\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,82893)),"D:\\Haq website v1\\haq-frontend-nextjs\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["D:\\Haq website v1\\haq-frontend-nextjs\\src\\app\\companies\\page.tsx"],m={require:t,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/companies/page",pathname:"/companies",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},88044:(e,s,t)=>{Promise.resolve().then(t.bind(t,32066))},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[447,942,658,188,647],()=>t(87218));module.exports=r})();
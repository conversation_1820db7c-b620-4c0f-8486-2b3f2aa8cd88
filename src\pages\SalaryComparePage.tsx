import React, { useState } from 'react';
import { TrendingUp, Filter, MapPin, Briefcase, DollarSign } from 'lucide-react';

interface SalaryData {
  id: number;
  position: string;
  company: string;
  location: string;
  experience: string;
  salary: number;
  industry: string;
  reports: number;
}

const salaryData: SalaryData[] = [
  {
    id: 1,
    position: 'Software Engineer',
    company: 'TechFlow Solutions',
    location: 'Karachi',
    experience: '1-3 years',
    salary: 85000,
    industry: 'Technology',
    reports: 12
  },
  {
    id: 2,
    position: 'Product Manager',
    company: 'Innovate Marketing',
    location: 'Lahore',
    experience: '5+ years',
    salary: 180000,
    industry: 'Marketing',
    reports: 8
  },
  {
    id: 3,
    position: 'Data Analyst',
    company: 'FinTech Solutions',
    location: 'Islamabad',
    experience: '2-4 years',
    salary: 95000,
    industry: 'Financial Technology',
    reports: 15
  },
  {
    id: 4,
    position: 'UI/UX Designer',
    company: 'Creative Digital',
    location: 'Karachi',
    experience: '2-4 years',
    salary: 75000,
    industry: 'Design',
    reports: 10
  },
  {
    id: 5,
    position: 'Senior Software Engineer',
    company: 'TechFlow Solutions',
    location: 'Karachi',
    experience: '3-5 years',
    salary: 140000,
    industry: 'Technology',
    reports: 18
  },
  {
    id: 6,
    position: 'Marketing Manager',
    company: 'Healthcare Plus',
    location: 'Lahore',
    experience: '5+ years',
    salary: 160000,
    industry: 'Healthcare',
    reports: 7
  },
  {
    id: 7,
    position: 'DevOps Engineer',
    company: 'Prime Logistics',
    location: 'Islamabad',
    experience: '3-5 years',
    salary: 125000,
    industry: 'Technology',
    reports: 9
  },
  {
    id: 8,
    position: 'Business Analyst',
    company: 'FinTech Solutions',
    location: 'Karachi',
    experience: '2-4 years',
    salary: 105000,
    industry: 'Financial Technology',
    reports: 13
  }
];

const positions = [...new Set(salaryData.map(s => s.position))].sort();
const locations = [...new Set(salaryData.map(s => s.location))].sort();
const industries = [...new Set(salaryData.map(s => s.industry))].sort();
const experienceLevels = ['1-3 years', '2-4 years', '3-5 years', '5+ years'];

export const SalaryComparePage: React.FC = () => {
  const [selectedPosition, setSelectedPosition] = useState('All Positions');
  const [selectedLocation, setSelectedLocation] = useState('All Locations');
  const [selectedIndustry, setSelectedIndustry] = useState('All Industries');
  const [selectedExperience, setSelectedExperience] = useState('All Experience');
  const [showFilters, setShowFilters] = useState(false);

  const filteredData = salaryData.filter(item => {
    return (
      (selectedPosition === 'All Positions' || item.position === selectedPosition) &&
      (selectedLocation === 'All Locations' || item.location === selectedLocation) &&
      (selectedIndustry === 'All Industries' || item.industry === selectedIndustry) &&
      (selectedExperience === 'All Experience' || item.experience === selectedExperience)
    );
  });

  const averageSalary = filteredData.length > 0 
    ? Math.round(filteredData.reduce((sum, item) => sum + item.salary, 0) / filteredData.length)
    : 0;

  const minSalary = filteredData.length > 0 ? Math.min(...filteredData.map(item => item.salary)) : 0;
  const maxSalary = filteredData.length > 0 ? Math.max(...filteredData.map(item => item.salary)) : 0;
  const totalReports = filteredData.reduce((sum, item) => sum + item.reports, 0);

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center mb-8">
            <div className="flex items-center justify-center space-x-2 mb-4">
              <TrendingUp className="w-8 h-8 text-primary-600" />
              <h1 className="text-3xl font-bold text-gray-900">Salary Insights</h1>
            </div>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Compare salaries across companies, positions, and cities in Pakistan. 
              All data is anonymous and contributed by real employees.
            </p>
          </div>

          {/* Quick Stats */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8">
            <div className="bg-primary-50 rounded-lg p-4 text-center">
              <div className="text-2xl font-bold text-primary-600">
                PKR {averageSalary.toLocaleString()}
              </div>
              <div className="text-sm text-primary-800">Average Salary</div>
            </div>
            <div className="bg-green-50 rounded-lg p-4 text-center">
              <div className="text-2xl font-bold text-green-600">
                PKR {minSalary.toLocaleString()}
              </div>
              <div className="text-sm text-green-800">Minimum</div>
            </div>
            <div className="bg-blue-50 rounded-lg p-4 text-center">
              <div className="text-2xl font-bold text-blue-600">
                PKR {maxSalary.toLocaleString()}
              </div>
              <div className="text-sm text-blue-800">Maximum</div>
            </div>
            <div className="bg-secondary-50 rounded-lg p-4 text-center">
              <div className="text-2xl font-bold text-secondary-600">
                {totalReports}
              </div>
              <div className="text-sm text-secondary-800">Total Reports</div>
            </div>
          </div>

          {/* Filters */}
          <div className="flex items-center justify-between mb-6">
            <button
              onClick={() => setShowFilters(!showFilters)}
              className="flex items-center space-x-2 px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors duration-200"
            >
              <Filter className="w-4 h-4" />
              <span>Filter Results</span>
            </button>
            <span className="text-sm text-gray-600">
              {filteredData.length} salary {filteredData.length === 1 ? 'report' : 'reports'} found
            </span>
          </div>

          {showFilters && (
            <div className="bg-gray-50 rounded-lg p-6 border border-gray-200">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Position</label>
                  <select
                    value={selectedPosition}
                    onChange={(e) => setSelectedPosition(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                  >
                    <option value="All Positions">All Positions</option>
                    {positions.map(position => (
                      <option key={position} value={position}>{position}</option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Location</label>
                  <select
                    value={selectedLocation}
                    onChange={(e) => setSelectedLocation(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                  >
                    <option value="All Locations">All Locations</option>
                    {locations.map(location => (
                      <option key={location} value={location}>{location}</option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Industry</label>
                  <select
                    value={selectedIndustry}
                    onChange={(e) => setSelectedIndustry(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                  >
                    <option value="All Industries">All Industries</option>
                    {industries.map(industry => (
                      <option key={industry} value={industry}>{industry}</option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Experience</label>
                  <select
                    value={selectedExperience}
                    onChange={(e) => setSelectedExperience(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                  >
                    <option value="All Experience">All Experience</option>
                    {experienceLevels.map(level => (
                      <option key={level} value={level}>{level}</option>
                    ))}
                  </select>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Results */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {filteredData.length > 0 ? (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
            <div className="px-6 py-4 border-b border-gray-200">
              <h2 className="text-lg font-semibold text-gray-900">Salary Reports</h2>
            </div>
            
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Position
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Company
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Location
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Experience
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Salary
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Reports
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {filteredData.map((item) => (
                    <tr key={item.id} className="hover:bg-gray-50 transition-colors duration-200">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <Briefcase className="w-4 h-4 text-gray-400 mr-2" />
                          <div className="text-sm font-medium text-gray-900">{item.position}</div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600">
                        {item.company}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center text-sm text-gray-600">
                          <MapPin className="w-4 h-4 text-gray-400 mr-1" />
                          {item.location}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600">
                        {item.experience}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center text-sm font-semibold text-primary-600">
                          <DollarSign className="w-4 h-4 mr-1" />
                          PKR {item.salary.toLocaleString()}/month
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600">
                        {item.reports} reports
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        ) : (
          <div className="text-center py-12">
            <div className="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <TrendingUp className="w-12 h-12 text-gray-400" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">No salary data found</h3>
            <p className="text-gray-600 mb-6">
              Try adjusting your filters to see more salary reports.
            </p>
            <button
              onClick={() => {
                setSelectedPosition('All Positions');
                setSelectedLocation('All Locations');
                setSelectedIndustry('All Industries');
                setSelectedExperience('All Experience');
              }}
              className="bg-primary-600 hover:bg-primary-700 text-white px-6 py-2 rounded-lg font-medium transition-colors duration-200"
            >
              Clear All Filters
            </button>
          </div>
        )}
      </div>
    </div>
  );
};
'use client';

import React from 'react';
import { 
  Building2, 
  MapPin, 
  Users, 
  Calendar, 
  ExternalLink, 
  Star,
  CheckCircle,
  TrendingUp
} from 'lucide-react';

interface Company {
  company_id: string;
  name: string;
  slug: string;
  industry?: string;
  location?: string;
  website_url?: string;
  logo_url?: string;
  employee_count_range?: string;
  founded_year?: number;
  description?: string;
  haq_score: number;
  total_reviews: number;
  average_rating: number;
  is_verified: boolean;
  rating_distribution: {
    1: number;
    2: number;
    3: number;
    4: number;
    5: number;
  };
}

interface CompanyDetailsProps {
  company: Company;
}

const CompanyDetails: React.FC<CompanyDetailsProps> = ({ company }) => {
  const formatEmployeeCount = (range?: string) => {
    if (!range) return 'Unknown';
    return range.replace('-', ' - ') + ' employees';
  };

  const formatWebsiteUrl = (url?: string) => {
    if (!url) return null;
    // Ensure URL has protocol
    if (!url.startsWith('http://') && !url.startsWith('https://')) {
      return `https://${url}`;
    }
    return url;
  };

  const getDisplayUrl = (url?: string) => {
    if (!url) return null;
    return url.replace(/^https?:\/\//, '').replace(/\/$/, '');
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, index) => (
      <Star
        key={index}
        className={`h-5 w-5 ${
          index < Math.floor(rating)
            ? 'text-yellow-400 fill-current'
            : index < rating
            ? 'text-yellow-400 fill-current opacity-50'
            : 'text-gray-300'
        }`}
      />
    ));
  };

  const renderRatingDistribution = () => {
    const total = Object.values(company.rating_distribution).reduce((sum, count) => sum + count, 0);
    
    if (total === 0) {
      return (
        <div className="text-center text-gray-500 py-4">
          No reviews yet
        </div>
      );
    }

    return (
      <div className="space-y-2">
        {[5, 4, 3, 2, 1].map((rating) => {
          const count = company.rating_distribution[rating as keyof typeof company.rating_distribution];
          const percentage = total > 0 ? (count / total) * 100 : 0;
          
          return (
            <div key={rating} className="flex items-center space-x-3">
              <div className="flex items-center space-x-1 w-12">
                <span className="text-sm font-medium">{rating}</span>
                <Star className="h-3 w-3 text-yellow-400 fill-current" />
              </div>
              <div className="flex-1 bg-gray-200 rounded-full h-2">
                <div
                  className="bg-yellow-400 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${percentage}%` }}
                />
              </div>
              <span className="text-sm text-gray-600 w-8 text-right">{count}</span>
            </div>
          );
        })}
      </div>
    );
  };

  return (
    <div className="bg-white">
      <div className="flex flex-col lg:flex-row lg:items-start lg:space-x-8">
        {/* Company Logo and Basic Info */}
        <div className="flex items-start space-x-6 mb-6 lg:mb-0">
          <div className="flex-shrink-0">
            {company.logo_url ? (
              <img
                src={company.logo_url}
                alt={`${company.name} logo`}
                className="w-20 h-20 lg:w-24 lg:h-24 rounded-lg object-cover border border-gray-200"
              />
            ) : (
              <div className="w-20 h-20 lg:w-24 lg:h-24 rounded-lg bg-gray-100 flex items-center justify-center border border-gray-200">
                <Building2 className="h-10 w-10 lg:h-12 lg:w-12 text-gray-400" />
              </div>
            )}
          </div>

          <div className="flex-1 min-w-0">
            <div className="flex items-center space-x-3 mb-2">
              <h1 className="text-2xl lg:text-3xl font-bold text-gray-900 truncate">
                {company.name}
              </h1>
              {company.is_verified && (
                <CheckCircle className="h-6 w-6 text-blue-500 flex-shrink-0" title="Verified Company" />
              )}
            </div>

            <div className="flex flex-wrap items-center gap-4 text-sm text-gray-600 mb-4">
              {company.industry && (
                <div className="flex items-center space-x-1">
                  <Building2 className="h-4 w-4" />
                  <span>{company.industry}</span>
                </div>
              )}
              {company.location && (
                <div className="flex items-center space-x-1">
                  <MapPin className="h-4 w-4" />
                  <span>{company.location}</span>
                </div>
              )}
              {company.employee_count_range && (
                <div className="flex items-center space-x-1">
                  <Users className="h-4 w-4" />
                  <span>{formatEmployeeCount(company.employee_count_range)}</span>
                </div>
              )}
              {company.founded_year && (
                <div className="flex items-center space-x-1">
                  <Calendar className="h-4 w-4" />
                  <span>Founded {company.founded_year}</span>
                </div>
              )}
            </div>

            {company.website_url && (
              <a
                href={formatWebsiteUrl(company.website_url)}
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center space-x-1 text-blue-600 hover:text-blue-800 text-sm"
              >
                <ExternalLink className="h-4 w-4" />
                <span>{getDisplayUrl(company.website_url)}</span>
              </a>
            )}
          </div>
        </div>

        {/* Rating and HAQ Score */}
        <div className="lg:flex-shrink-0 lg:w-80">
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-1 gap-6">
            {/* Overall Rating */}
            <div className="bg-gray-50 rounded-lg p-6">
              <div className="text-center mb-4">
                <div className="text-3xl font-bold text-gray-900 mb-1">
                  {company.average_rating > 0 ? company.average_rating.toFixed(1) : 'N/A'}
                </div>
                <div className="flex justify-center space-x-1 mb-2">
                  {renderStars(company.average_rating)}
                </div>
                <div className="text-sm text-gray-600">
                  Based on {company.total_reviews} review{company.total_reviews !== 1 ? 's' : ''}
                </div>
              </div>
              
              {/* Rating Distribution */}
              <div className="mt-4">
                {renderRatingDistribution()}
              </div>
            </div>

            {/* HAQ Score */}
            <div className="bg-blue-50 rounded-lg p-6">
              <div className="text-center">
                <div className="flex items-center justify-center space-x-2 mb-2">
                  <TrendingUp className="h-5 w-5 text-blue-600" />
                  <span className="text-sm font-medium text-blue-900">HAQ Score</span>
                </div>
                <div className="text-3xl font-bold text-blue-900 mb-1">
                  {company.haq_score}
                </div>
                <div className="text-xs text-blue-700">
                  Workplace Quality Index
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Company Description */}
      {company.description && (
        <div className="mt-8 pt-8 border-t border-gray-200">
          <h2 className="text-lg font-semibold text-gray-900 mb-3">About {company.name}</h2>
          <p className="text-gray-700 leading-relaxed">{company.description}</p>
        </div>
      )}

      {/* Quick Actions */}
      <div className="mt-8 pt-8 border-t border-gray-200">
        <div className="flex flex-col sm:flex-row gap-4">
          <a
            href="/review/submit"
            className="inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 transition-colors"
          >
            Write a Review
          </a>
          <button
            type="button"
            className="inline-flex items-center justify-center px-6 py-3 border border-gray-300 text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 transition-colors"
          >
            Follow Company
          </button>
        </div>
      </div>
    </div>
  );
};

export default CompanyDetails;

"use strict";exports.id=605,exports.ids=[605],exports.modules={27605:(e,t,r)=>{r.d(t,{Gb:()=>L,Jt:()=>p,hZ:()=>V,mN:()=>eF,xI:()=>O});var a=r(43210),s=e=>"checkbox"===e.type,i=e=>e instanceof Date,l=e=>null==e;let u=e=>"object"==typeof e;var n=e=>!l(e)&&!Array.isArray(e)&&u(e)&&!i(e),o=e=>n(e)&&e.target?s(e.target)?e.target.checked:e.target.value:e,d=e=>e.substring(0,e.search(/\.\d+(\.|$)/))||e,f=(e,t)=>e.has(d(t)),c=e=>{let t=e.constructor&&e.constructor.prototype;return n(t)&&t.hasOwnProperty("isPrototypeOf")},y="undefined"!=typeof window&&void 0!==window.HTMLElement&&"undefined"!=typeof document;function m(e){let t,r=Array.isArray(e),a="undefined"!=typeof FileList&&e instanceof FileList;if(e instanceof Date)t=new Date(e);else if(e instanceof Set)t=new Set(e);else if(!(!(y&&(e instanceof Blob||a))&&(r||n(e))))return e;else if(t=r?[]:{},r||c(e))for(let r in e)e.hasOwnProperty(r)&&(t[r]=m(e[r]));else t=e;return t}var h=e=>/^\w*$/.test(e),v=e=>void 0===e,b=e=>Array.isArray(e)?e.filter(Boolean):[],g=e=>b(e.replace(/["|']|\]/g,"").split(/\.|\[/)),p=(e,t,r)=>{if(!t||!n(e))return r;let a=(h(t)?[t]:g(t)).reduce((e,t)=>l(e)?e:e[t],e);return v(a)||a===e?v(e[t])?r:e[t]:a},_=e=>"boolean"==typeof e,V=(e,t,r)=>{let a=-1,s=h(t)?[t]:g(t),i=s.length,l=i-1;for(;++a<i;){let t=s[a],i=r;if(a!==l){let r=e[t];i=n(r)||Array.isArray(r)?r:isNaN(+s[a+1])?{}:[]}if("__proto__"===t||"constructor"===t||"prototype"===t)return;e[t]=i,e=e[t]}};let F={BLUR:"blur",FOCUS_OUT:"focusout",CHANGE:"change"},A={onBlur:"onBlur",onChange:"onChange",onSubmit:"onSubmit",onTouched:"onTouched",all:"all"},w={max:"max",min:"min",maxLength:"maxLength",minLength:"minLength",pattern:"pattern",required:"required",validate:"validate"},S=a.createContext(null);S.displayName="HookFormContext";let x=()=>a.useContext(S);var k=(e,t,r,a=!0)=>{let s={defaultValues:t._defaultValues};for(let i in e)Object.defineProperty(s,i,{get:()=>(t._proxyFormState[i]!==A.all&&(t._proxyFormState[i]=!a||A.all),r&&(r[i]=!0),e[i])});return s};let D="undefined"!=typeof window?a.useLayoutEffect:a.useEffect;var C=e=>"string"==typeof e,E=(e,t,r,a,s)=>C(e)?(a&&t.watch.add(e),p(r,e,s)):Array.isArray(e)?e.map(e=>(a&&t.watch.add(e),p(r,e))):(a&&(t.watchAll=!0),r);let O=e=>e.render(function(e){let t=x(),{name:r,disabled:s,control:i=t.control,shouldUnregister:l}=e,u=f(i._names.array,r),n=function(e){let t=x(),{control:r=t.control,name:s,defaultValue:i,disabled:l,exact:u}=e||{},n=a.useRef(i),[o,d]=a.useState(r._getWatch(s,n.current));return D(()=>r._subscribe({name:s,formState:{values:!0},exact:u,callback:e=>!l&&d(E(s,r._names,e.values||r._formValues,!1,n.current))}),[s,r,l,u]),a.useEffect(()=>r._removeUnmounted()),o}({control:i,name:r,defaultValue:p(i._formValues,r,p(i._defaultValues,r,e.defaultValue)),exact:!0}),d=function(e){let t=x(),{control:r=t.control,disabled:s,name:i,exact:l}=e||{},[u,n]=a.useState(r._formState),o=a.useRef({isDirty:!1,isLoading:!1,dirtyFields:!1,touchedFields:!1,validatingFields:!1,isValidating:!1,isValid:!1,errors:!1});return D(()=>r._subscribe({name:i,formState:o.current,exact:l,callback:e=>{s||n({...r._formState,...e})}}),[i,s,l]),a.useEffect(()=>{o.current.isValid&&r._setValid(!0)},[r]),a.useMemo(()=>k(u,r,o.current,!1),[u,r])}({control:i,name:r,exact:!0}),c=a.useRef(e),y=a.useRef(i.register(r,{...e.rules,value:n,..._(e.disabled)?{disabled:e.disabled}:{}})),h=a.useMemo(()=>Object.defineProperties({},{invalid:{enumerable:!0,get:()=>!!p(d.errors,r)},isDirty:{enumerable:!0,get:()=>!!p(d.dirtyFields,r)},isTouched:{enumerable:!0,get:()=>!!p(d.touchedFields,r)},isValidating:{enumerable:!0,get:()=>!!p(d.validatingFields,r)},error:{enumerable:!0,get:()=>p(d.errors,r)}}),[d,r]),b=a.useCallback(e=>y.current.onChange({target:{value:o(e),name:r},type:F.CHANGE}),[r]),g=a.useCallback(()=>y.current.onBlur({target:{value:p(i._formValues,r),name:r},type:F.BLUR}),[r,i._formValues]),A=a.useCallback(e=>{let t=p(i._fields,r);t&&e&&(t._f.ref={focus:()=>e.focus&&e.focus(),select:()=>e.select&&e.select(),setCustomValidity:t=>e.setCustomValidity(t),reportValidity:()=>e.reportValidity()})},[i._fields,r]),w=a.useMemo(()=>({name:r,value:n,..._(s)||d.disabled?{disabled:d.disabled||s}:{},onChange:b,onBlur:g,ref:A}),[r,s,d.disabled,b,g,A,n]);return a.useEffect(()=>{let e=i._options.shouldUnregister||l;i.register(r,{...c.current.rules,..._(c.current.disabled)?{disabled:c.current.disabled}:{}});let t=(e,t)=>{let r=p(i._fields,e);r&&r._f&&(r._f.mount=t)};if(t(r,!0),e){let e=m(p(i._options.defaultValues,r));V(i._defaultValues,r,e),v(p(i._formValues,r))&&V(i._formValues,r,e)}return u||i.register(r),()=>{(u?e&&!i._state.action:e)?i.unregister(r):t(r,!1)}},[r,i,u,l]),a.useEffect(()=>{i._setDisabledField({disabled:s,name:r})},[s,r,i]),a.useMemo(()=>({field:w,formState:d,fieldState:h}),[w,d,h])}(e));var L=(e,t,r,a,s)=>t?{...r[e],types:{...r[e]&&r[e].types?r[e].types:{},[a]:s||!0}}:{},T=e=>Array.isArray(e)?e:[e],U=()=>{let e=[];return{get observers(){return e},next:t=>{for(let r of e)r.next&&r.next(t)},subscribe:t=>(e.push(t),{unsubscribe:()=>{e=e.filter(e=>e!==t)}}),unsubscribe:()=>{e=[]}}},B=e=>l(e)||!u(e);function M(e,t){if(B(e)||B(t))return e===t;if(i(e)&&i(t))return e.getTime()===t.getTime();let r=Object.keys(e),a=Object.keys(t);if(r.length!==a.length)return!1;for(let s of r){let r=e[s];if(!a.includes(s))return!1;if("ref"!==s){let e=t[s];if(i(r)&&i(e)||n(r)&&n(e)||Array.isArray(r)&&Array.isArray(e)?!M(r,e):r!==e)return!1}}return!0}var N=e=>n(e)&&!Object.keys(e).length,R=e=>"file"===e.type,j=e=>"function"==typeof e,q=e=>{if(!y)return!1;let t=e?e.ownerDocument:0;return e instanceof(t&&t.defaultView?t.defaultView.HTMLElement:HTMLElement)},P=e=>"select-multiple"===e.type,H=e=>"radio"===e.type,I=e=>H(e)||s(e),W=e=>q(e)&&e.isConnected;function G(e,t){let r=Array.isArray(t)?t:h(t)?[t]:g(t),a=1===r.length?e:function(e,t){let r=t.slice(0,-1).length,a=0;for(;a<r;)e=v(e)?a++:e[t[a++]];return e}(e,r),s=r.length-1,i=r[s];return a&&delete a[i],0!==s&&(n(a)&&N(a)||Array.isArray(a)&&function(e){for(let t in e)if(e.hasOwnProperty(t)&&!v(e[t]))return!1;return!0}(a))&&G(e,r.slice(0,-1)),e}var $=e=>{for(let t in e)if(j(e[t]))return!0;return!1};function z(e,t={}){let r=Array.isArray(e);if(n(e)||r)for(let r in e)Array.isArray(e[r])||n(e[r])&&!$(e[r])?(t[r]=Array.isArray(e[r])?[]:{},z(e[r],t[r])):l(e[r])||(t[r]=!0);return t}var J=(e,t)=>(function e(t,r,a){let s=Array.isArray(t);if(n(t)||s)for(let s in t)Array.isArray(t[s])||n(t[s])&&!$(t[s])?v(r)||B(a[s])?a[s]=Array.isArray(t[s])?z(t[s],[]):{...z(t[s])}:e(t[s],l(r)?{}:r[s],a[s]):a[s]=!M(t[s],r[s]);return a})(e,t,z(t));let Z={value:!1,isValid:!1},K={value:!0,isValid:!0};var Q=e=>{if(Array.isArray(e)){if(e.length>1){let t=e.filter(e=>e&&e.checked&&!e.disabled).map(e=>e.value);return{value:t,isValid:!!t.length}}return e[0].checked&&!e[0].disabled?e[0].attributes&&!v(e[0].attributes.value)?v(e[0].value)||""===e[0].value?K:{value:e[0].value,isValid:!0}:K:Z}return Z},X=(e,{valueAsNumber:t,valueAsDate:r,setValueAs:a})=>v(e)?e:t?""===e?NaN:e?+e:e:r&&C(e)?new Date(e):a?a(e):e;let Y={isValid:!1,value:null};var ee=e=>Array.isArray(e)?e.reduce((e,t)=>t&&t.checked&&!t.disabled?{isValid:!0,value:t.value}:e,Y):Y;function et(e){let t=e.ref;return R(t)?t.files:H(t)?ee(e.refs).value:P(t)?[...t.selectedOptions].map(({value:e})=>e):s(t)?Q(e.refs).value:X(v(t.value)?e.ref.value:t.value,e)}var er=(e,t,r,a)=>{let s={};for(let r of e){let e=p(t,r);e&&V(s,r,e._f)}return{criteriaMode:r,names:[...e],fields:s,shouldUseNativeValidation:a}},ea=e=>e instanceof RegExp,es=e=>v(e)?e:ea(e)?e.source:n(e)?ea(e.value)?e.value.source:e.value:e,ei=e=>({isOnSubmit:!e||e===A.onSubmit,isOnBlur:e===A.onBlur,isOnChange:e===A.onChange,isOnAll:e===A.all,isOnTouch:e===A.onTouched});let el="AsyncFunction";var eu=e=>!!e&&!!e.validate&&!!(j(e.validate)&&e.validate.constructor.name===el||n(e.validate)&&Object.values(e.validate).find(e=>e.constructor.name===el)),en=e=>e.mount&&(e.required||e.min||e.max||e.maxLength||e.minLength||e.pattern||e.validate),eo=(e,t,r)=>!r&&(t.watchAll||t.watch.has(e)||[...t.watch].some(t=>e.startsWith(t)&&/^\.\w+/.test(e.slice(t.length))));let ed=(e,t,r,a)=>{for(let s of r||Object.keys(e)){let r=p(e,s);if(r){let{_f:e,...i}=r;if(e){if(e.refs&&e.refs[0]&&t(e.refs[0],s)&&!a)return!0;else if(e.ref&&t(e.ref,e.name)&&!a)return!0;else if(ed(i,t))break}else if(n(i)&&ed(i,t))break}}};function ef(e,t,r){let a=p(e,r);if(a||h(r))return{error:a,name:r};let s=r.split(".");for(;s.length;){let a=s.join("."),i=p(t,a),l=p(e,a);if(i&&!Array.isArray(i)&&r!==a)break;if(l&&l.type)return{name:a,error:l};if(l&&l.root&&l.root.type)return{name:`${a}.root`,error:l.root};s.pop()}return{name:r}}var ec=(e,t,r,a)=>{r(e);let{name:s,...i}=e;return N(i)||Object.keys(i).length>=Object.keys(t).length||Object.keys(i).find(e=>t[e]===(!a||A.all))},ey=(e,t,r)=>!e||!t||e===t||T(e).some(e=>e&&(r?e===t:e.startsWith(t)||t.startsWith(e))),em=(e,t,r,a,s)=>!s.isOnAll&&(!r&&s.isOnTouch?!(t||e):(r?a.isOnBlur:s.isOnBlur)?!e:(r?!a.isOnChange:!s.isOnChange)||e),eh=(e,t)=>!b(p(e,t)).length&&G(e,t),ev=(e,t,r)=>{let a=T(p(e,r));return V(a,"root",t[r]),V(e,r,a),e},eb=e=>C(e);function eg(e,t,r="validate"){if(eb(e)||Array.isArray(e)&&e.every(eb)||_(e)&&!e)return{type:r,message:eb(e)?e:"",ref:t}}var ep=e=>n(e)&&!ea(e)?e:{value:e,message:""},e_=async(e,t,r,a,i,u)=>{let{ref:o,refs:d,required:f,maxLength:c,minLength:y,min:m,max:h,pattern:b,validate:g,name:V,valueAsNumber:F,mount:A}=e._f,S=p(r,V);if(!A||t.has(V))return{};let x=d?d[0]:o,k=e=>{i&&x.reportValidity&&(x.setCustomValidity(_(e)?"":e||""),x.reportValidity())},D={},E=H(o),O=s(o),T=(F||R(o))&&v(o.value)&&v(S)||q(o)&&""===o.value||""===S||Array.isArray(S)&&!S.length,U=L.bind(null,V,a,D),B=(e,t,r,a=w.maxLength,s=w.minLength)=>{let i=e?t:r;D[V]={type:e?a:s,message:i,ref:o,...U(e?a:s,i)}};if(u?!Array.isArray(S)||!S.length:f&&(!(E||O)&&(T||l(S))||_(S)&&!S||O&&!Q(d).isValid||E&&!ee(d).isValid)){let{value:e,message:t}=eb(f)?{value:!!f,message:f}:ep(f);if(e&&(D[V]={type:w.required,message:t,ref:x,...U(w.required,t)},!a))return k(t),D}if(!T&&(!l(m)||!l(h))){let e,t,r=ep(h),s=ep(m);if(l(S)||isNaN(S)){let a=o.valueAsDate||new Date(S),i=e=>new Date(new Date().toDateString()+" "+e),l="time"==o.type,u="week"==o.type;C(r.value)&&S&&(e=l?i(S)>i(r.value):u?S>r.value:a>new Date(r.value)),C(s.value)&&S&&(t=l?i(S)<i(s.value):u?S<s.value:a<new Date(s.value))}else{let a=o.valueAsNumber||(S?+S:S);l(r.value)||(e=a>r.value),l(s.value)||(t=a<s.value)}if((e||t)&&(B(!!e,r.message,s.message,w.max,w.min),!a))return k(D[V].message),D}if((c||y)&&!T&&(C(S)||u&&Array.isArray(S))){let e=ep(c),t=ep(y),r=!l(e.value)&&S.length>+e.value,s=!l(t.value)&&S.length<+t.value;if((r||s)&&(B(r,e.message,t.message),!a))return k(D[V].message),D}if(b&&!T&&C(S)){let{value:e,message:t}=ep(b);if(ea(e)&&!S.match(e)&&(D[V]={type:w.pattern,message:t,ref:o,...U(w.pattern,t)},!a))return k(t),D}if(g){if(j(g)){let e=eg(await g(S,r),x);if(e&&(D[V]={...e,...U(w.validate,e.message)},!a))return k(e.message),D}else if(n(g)){let e={};for(let t in g){if(!N(e)&&!a)break;let s=eg(await g[t](S,r),x,t);s&&(e={...s,...U(t,s.message)},k(s.message),a&&(D[V]=e))}if(!N(e)&&(D[V]={ref:x,...e},!a))return D}}return k(!0),D};let eV={mode:A.onSubmit,reValidateMode:A.onChange,shouldFocusError:!0};function eF(e={}){let t=a.useRef(void 0),r=a.useRef(void 0),[u,d]=a.useState({isDirty:!1,isValidating:!1,isLoading:j(e.defaultValues),isSubmitted:!1,isSubmitting:!1,isSubmitSuccessful:!1,isValid:!1,submitCount:0,dirtyFields:{},touchedFields:{},validatingFields:{},errors:e.errors||{},disabled:e.disabled||!1,isReady:!1,defaultValues:j(e.defaultValues)?void 0:e.defaultValues});if(!t.current)if(e.formControl)t.current={...e.formControl,formState:u},e.defaultValues&&!j(e.defaultValues)&&e.formControl.reset(e.defaultValues,e.resetOptions);else{let{formControl:r,...a}=function(e={}){let t,r={...eV,...e},a={submitCount:0,isDirty:!1,isReady:!1,isLoading:j(r.defaultValues),isValidating:!1,isSubmitted:!1,isSubmitting:!1,isSubmitSuccessful:!1,isValid:!1,touchedFields:{},dirtyFields:{},validatingFields:{},errors:r.errors||{},disabled:r.disabled||!1},u={},d=(n(r.defaultValues)||n(r.values))&&m(r.defaultValues||r.values)||{},c=r.shouldUnregister?{}:m(d),h={action:!1,mount:!1,watch:!1},g={mount:new Set,disabled:new Set,unMount:new Set,array:new Set,watch:new Set},w=0,S={isDirty:!1,dirtyFields:!1,validatingFields:!1,touchedFields:!1,isValidating:!1,isValid:!1,errors:!1},x={...S},k={array:U(),state:U()},D=r.criteriaMode===A.all,O=e=>t=>{clearTimeout(w),w=setTimeout(e,t)},L=async e=>{if(!r.disabled&&(S.isValid||x.isValid||e)){let e=r.resolver?N((await K()).errors):await Y(u,!0);e!==a.isValid&&k.state.next({isValid:e})}},B=(e,t)=>{!r.disabled&&(S.isValidating||S.validatingFields||x.isValidating||x.validatingFields)&&((e||Array.from(g.mount)).forEach(e=>{e&&(t?V(a.validatingFields,e,t):G(a.validatingFields,e))}),k.state.next({validatingFields:a.validatingFields,isValidating:!N(a.validatingFields)}))},H=(e,t)=>{V(a.errors,e,t),k.state.next({errors:a.errors})},$=(e,t,r,a)=>{let s=p(u,e);if(s){let i=p(c,e,v(r)?p(d,e):r);v(i)||a&&a.defaultChecked||t?V(c,e,t?i:et(s._f)):el(e,i),h.mount&&L()}},z=(e,t,s,i,l)=>{let u=!1,n=!1,o={name:e};if(!r.disabled){if(!s||i){(S.isDirty||x.isDirty)&&(n=a.isDirty,a.isDirty=o.isDirty=ee(),u=n!==o.isDirty);let r=M(p(d,e),t);n=!!p(a.dirtyFields,e),r?G(a.dirtyFields,e):V(a.dirtyFields,e,!0),o.dirtyFields=a.dirtyFields,u=u||(S.dirtyFields||x.dirtyFields)&&!r!==n}if(s){let t=p(a.touchedFields,e);t||(V(a.touchedFields,e,s),o.touchedFields=a.touchedFields,u=u||(S.touchedFields||x.touchedFields)&&t!==s)}u&&l&&k.state.next(o)}return u?o:{}},Z=(e,s,i,l)=>{let u=p(a.errors,e),n=(S.isValid||x.isValid)&&_(s)&&a.isValid!==s;if(r.delayError&&i?(t=O(()=>H(e,i)))(r.delayError):(clearTimeout(w),t=null,i?V(a.errors,e,i):G(a.errors,e)),(i?!M(u,i):u)||!N(l)||n){let t={...l,...n&&_(s)?{isValid:s}:{},errors:a.errors,name:e};a={...a,...t},k.state.next(t)}},K=async e=>{B(e,!0);let t=await r.resolver(c,r.context,er(e||g.mount,u,r.criteriaMode,r.shouldUseNativeValidation));return B(e),t},Q=async e=>{let{errors:t}=await K(e);if(e)for(let r of e){let e=p(t,r);e?V(a.errors,r,e):G(a.errors,r)}else a.errors=t;return t},Y=async(e,t,s={valid:!0})=>{for(let i in e){let l=e[i];if(l){let{_f:e,...u}=l;if(e){let u=g.array.has(e.name),n=l._f&&eu(l._f);n&&S.validatingFields&&B([i],!0);let o=await e_(l,g.disabled,c,D,r.shouldUseNativeValidation&&!t,u);if(n&&S.validatingFields&&B([i]),o[e.name]&&(s.valid=!1,t))break;t||(p(o,e.name)?u?ev(a.errors,o,e.name):V(a.errors,e.name,o[e.name]):G(a.errors,e.name))}N(u)||await Y(u,t,s)}}return s.valid},ee=(e,t)=>!r.disabled&&(e&&t&&V(c,e,t),!M(ew(),d)),ea=(e,t,r)=>E(e,g,{...h.mount?c:v(t)?d:C(e)?{[e]:t}:t},r,t),el=(e,t,r={})=>{let a=p(u,e),i=t;if(a){let r=a._f;r&&(r.disabled||V(c,e,X(t,r)),i=q(r.ref)&&l(t)?"":t,P(r.ref)?[...r.ref.options].forEach(e=>e.selected=i.includes(e.value)):r.refs?s(r.ref)?r.refs.forEach(e=>{e.defaultChecked&&e.disabled||(Array.isArray(i)?e.checked=!!i.find(t=>t===e.value):e.checked=i===e.value||!!i)}):r.refs.forEach(e=>e.checked=e.value===i):R(r.ref)?r.ref.value="":(r.ref.value=i,r.ref.type||k.state.next({name:e,values:m(c)})))}(r.shouldDirty||r.shouldTouch)&&z(e,i,r.shouldTouch,r.shouldDirty,!0),r.shouldValidate&&eA(e)},eb=(e,t,r)=>{for(let a in t){if(!t.hasOwnProperty(a))return;let s=t[a],l=e+"."+a,o=p(u,l);(g.array.has(e)||n(s)||o&&!o._f)&&!i(s)?eb(l,s,r):el(l,s,r)}},eg=(e,t,r={})=>{let s=p(u,e),i=g.array.has(e),n=m(t);V(c,e,n),i?(k.array.next({name:e,values:m(c)}),(S.isDirty||S.dirtyFields||x.isDirty||x.dirtyFields)&&r.shouldDirty&&k.state.next({name:e,dirtyFields:J(d,c),isDirty:ee(e,n)})):!s||s._f||l(n)?el(e,n,r):eb(e,n,r),eo(e,g)&&k.state.next({...a}),k.state.next({name:h.mount?e:void 0,values:m(c)})},ep=async e=>{h.mount=!0;let s=e.target,l=s.name,n=!0,d=p(u,l),f=e=>{n=Number.isNaN(e)||i(e)&&isNaN(e.getTime())||M(e,p(c,l,e))},y=ei(r.mode),v=ei(r.reValidateMode);if(d){let i,h,b=s.type?et(d._f):o(e),_=e.type===F.BLUR||e.type===F.FOCUS_OUT,A=!en(d._f)&&!r.resolver&&!p(a.errors,l)&&!d._f.deps||em(_,p(a.touchedFields,l),a.isSubmitted,v,y),w=eo(l,g,_);V(c,l,b),_?(d._f.onBlur&&d._f.onBlur(e),t&&t(0)):d._f.onChange&&d._f.onChange(e);let C=z(l,b,_),E=!N(C)||w;if(_||k.state.next({name:l,type:e.type,values:m(c)}),A)return(S.isValid||x.isValid)&&("onBlur"===r.mode?_&&L():_||L()),E&&k.state.next({name:l,...w?{}:C});if(!_&&w&&k.state.next({...a}),r.resolver){let{errors:e}=await K([l]);if(f(b),n){let t=ef(a.errors,u,l),r=ef(e,u,t.name||l);i=r.error,l=r.name,h=N(e)}}else B([l],!0),i=(await e_(d,g.disabled,c,D,r.shouldUseNativeValidation))[l],B([l]),f(b),n&&(i?h=!1:(S.isValid||x.isValid)&&(h=await Y(u,!0)));n&&(d._f.deps&&eA(d._f.deps),Z(l,h,i,C))}},eF=(e,t)=>{if(p(a.errors,t)&&e.focus)return e.focus(),1},eA=async(e,t={})=>{let s,i,l=T(e);if(r.resolver){let t=await Q(v(e)?e:l);s=N(t),i=e?!l.some(e=>p(t,e)):s}else e?((i=(await Promise.all(l.map(async e=>{let t=p(u,e);return await Y(t&&t._f?{[e]:t}:t)}))).every(Boolean))||a.isValid)&&L():i=s=await Y(u);return k.state.next({...!C(e)||(S.isValid||x.isValid)&&s!==a.isValid?{}:{name:e},...r.resolver||!e?{isValid:s}:{},errors:a.errors}),t.shouldFocus&&!i&&ed(u,eF,e?l:g.mount),i},ew=e=>{let t={...h.mount?c:d};return v(e)?t:C(e)?p(t,e):e.map(e=>p(t,e))},eS=(e,t)=>({invalid:!!p((t||a).errors,e),isDirty:!!p((t||a).dirtyFields,e),error:p((t||a).errors,e),isValidating:!!p(a.validatingFields,e),isTouched:!!p((t||a).touchedFields,e)}),ex=(e,t,r)=>{let s=(p(u,e,{_f:{}})._f||{}).ref,{ref:i,message:l,type:n,...o}=p(a.errors,e)||{};V(a.errors,e,{...o,...t,ref:s}),k.state.next({name:e,errors:a.errors,isValid:!1}),r&&r.shouldFocus&&s&&s.focus&&s.focus()},ek=e=>k.state.subscribe({next:t=>{ey(e.name,t.name,e.exact)&&ec(t,e.formState||S,eB,e.reRenderRoot)&&e.callback({values:{...c},...a,...t})}}).unsubscribe,eD=(e,t={})=>{for(let s of e?T(e):g.mount)g.mount.delete(s),g.array.delete(s),t.keepValue||(G(u,s),G(c,s)),t.keepError||G(a.errors,s),t.keepDirty||G(a.dirtyFields,s),t.keepTouched||G(a.touchedFields,s),t.keepIsValidating||G(a.validatingFields,s),r.shouldUnregister||t.keepDefaultValue||G(d,s);k.state.next({values:m(c)}),k.state.next({...a,...!t.keepDirty?{}:{isDirty:ee()}}),t.keepIsValid||L()},eC=({disabled:e,name:t})=>{(_(e)&&h.mount||e||g.disabled.has(t))&&(e?g.disabled.add(t):g.disabled.delete(t))},eE=(e,t={})=>{let a=p(u,e),s=_(t.disabled)||_(r.disabled);return V(u,e,{...a||{},_f:{...a&&a._f?a._f:{ref:{name:e}},name:e,mount:!0,...t}}),g.mount.add(e),a?eC({disabled:_(t.disabled)?t.disabled:r.disabled,name:e}):$(e,!0,t.value),{...s?{disabled:t.disabled||r.disabled}:{},...r.progressive?{required:!!t.required,min:es(t.min),max:es(t.max),minLength:es(t.minLength),maxLength:es(t.maxLength),pattern:es(t.pattern)}:{},name:e,onChange:ep,onBlur:ep,ref:s=>{if(s){eE(e,t),a=p(u,e);let r=v(s.value)&&s.querySelectorAll&&s.querySelectorAll("input,select,textarea")[0]||s,i=I(r),l=a._f.refs||[];(i?l.find(e=>e===r):r===a._f.ref)||(V(u,e,{_f:{...a._f,...i?{refs:[...l.filter(W),r,...Array.isArray(p(d,e))?[{}]:[]],ref:{type:r.type,name:e}}:{ref:r}}}),$(e,!1,void 0,r))}else(a=p(u,e,{}))._f&&(a._f.mount=!1),(r.shouldUnregister||t.shouldUnregister)&&!(f(g.array,e)&&h.action)&&g.unMount.add(e)}}},eO=()=>r.shouldFocusError&&ed(u,eF,g.mount),eL=(e,t)=>async s=>{let i;s&&(s.preventDefault&&s.preventDefault(),s.persist&&s.persist());let l=m(c);if(k.state.next({isSubmitting:!0}),r.resolver){let{errors:e,values:t}=await K();a.errors=e,l=t}else await Y(u);if(g.disabled.size)for(let e of g.disabled)V(l,e,void 0);if(G(a.errors,"root"),N(a.errors)){k.state.next({errors:{}});try{await e(l,s)}catch(e){i=e}}else t&&await t({...a.errors},s),eO(),setTimeout(eO);if(k.state.next({isSubmitted:!0,isSubmitting:!1,isSubmitSuccessful:N(a.errors)&&!i,submitCount:a.submitCount+1,errors:a.errors}),i)throw i},eT=(e,t={})=>{let s=e?m(e):d,i=m(s),l=N(e),n=l?d:i;if(t.keepDefaultValues||(d=s),!t.keepValues){if(t.keepDirtyValues)for(let e of Array.from(new Set([...g.mount,...Object.keys(J(d,c))])))p(a.dirtyFields,e)?V(n,e,p(c,e)):eg(e,p(n,e));else{if(y&&v(e))for(let e of g.mount){let t=p(u,e);if(t&&t._f){let e=Array.isArray(t._f.refs)?t._f.refs[0]:t._f.ref;if(q(e)){let t=e.closest("form");if(t){t.reset();break}}}}for(let e of g.mount)eg(e,p(n,e))}c=m(n),k.array.next({values:{...n}}),k.state.next({values:{...n}})}g={mount:t.keepDirtyValues?g.mount:new Set,unMount:new Set,array:new Set,disabled:new Set,watch:new Set,watchAll:!1,focus:""},h.mount=!S.isValid||!!t.keepIsValid||!!t.keepDirtyValues,h.watch=!!r.shouldUnregister,k.state.next({submitCount:t.keepSubmitCount?a.submitCount:0,isDirty:!l&&(t.keepDirty?a.isDirty:!!(t.keepDefaultValues&&!M(e,d))),isSubmitted:!!t.keepIsSubmitted&&a.isSubmitted,dirtyFields:l?{}:t.keepDirtyValues?t.keepDefaultValues&&c?J(d,c):a.dirtyFields:t.keepDefaultValues&&e?J(d,e):t.keepDirty?a.dirtyFields:{},touchedFields:t.keepTouched?a.touchedFields:{},errors:t.keepErrors?a.errors:{},isSubmitSuccessful:!!t.keepIsSubmitSuccessful&&a.isSubmitSuccessful,isSubmitting:!1})},eU=(e,t)=>eT(j(e)?e(c):e,t),eB=e=>{a={...a,...e}},eM={control:{register:eE,unregister:eD,getFieldState:eS,handleSubmit:eL,setError:ex,_subscribe:ek,_runSchema:K,_focusError:eO,_getWatch:ea,_getDirty:ee,_setValid:L,_setFieldArray:(e,t=[],s,i,l=!0,n=!0)=>{if(i&&s&&!r.disabled){if(h.action=!0,n&&Array.isArray(p(u,e))){let t=s(p(u,e),i.argA,i.argB);l&&V(u,e,t)}if(n&&Array.isArray(p(a.errors,e))){let t=s(p(a.errors,e),i.argA,i.argB);l&&V(a.errors,e,t),eh(a.errors,e)}if((S.touchedFields||x.touchedFields)&&n&&Array.isArray(p(a.touchedFields,e))){let t=s(p(a.touchedFields,e),i.argA,i.argB);l&&V(a.touchedFields,e,t)}(S.dirtyFields||x.dirtyFields)&&(a.dirtyFields=J(d,c)),k.state.next({name:e,isDirty:ee(e,t),dirtyFields:a.dirtyFields,errors:a.errors,isValid:a.isValid})}else V(c,e,t)},_setDisabledField:eC,_setErrors:e=>{a.errors=e,k.state.next({errors:a.errors,isValid:!1})},_getFieldArray:e=>b(p(h.mount?c:d,e,r.shouldUnregister?p(d,e,[]):[])),_reset:eT,_resetDefaultValues:()=>j(r.defaultValues)&&r.defaultValues().then(e=>{eU(e,r.resetOptions),k.state.next({isLoading:!1})}),_removeUnmounted:()=>{for(let e of g.unMount){let t=p(u,e);t&&(t._f.refs?t._f.refs.every(e=>!W(e)):!W(t._f.ref))&&eD(e)}g.unMount=new Set},_disableForm:e=>{_(e)&&(k.state.next({disabled:e}),ed(u,(t,r)=>{let a=p(u,r);a&&(t.disabled=a._f.disabled||e,Array.isArray(a._f.refs)&&a._f.refs.forEach(t=>{t.disabled=a._f.disabled||e}))},0,!1))},_subjects:k,_proxyFormState:S,get _fields(){return u},get _formValues(){return c},get _state(){return h},set _state(value){h=value},get _defaultValues(){return d},get _names(){return g},set _names(value){g=value},get _formState(){return a},get _options(){return r},set _options(value){r={...r,...value}}},subscribe:e=>(h.mount=!0,x={...x,...e.formState},ek({...e,formState:x})),trigger:eA,register:eE,handleSubmit:eL,watch:(e,t)=>j(e)?k.state.subscribe({next:r=>e(ea(void 0,t),r)}):ea(e,t,!0),setValue:eg,getValues:ew,reset:eU,resetField:(e,t={})=>{p(u,e)&&(v(t.defaultValue)?eg(e,m(p(d,e))):(eg(e,t.defaultValue),V(d,e,m(t.defaultValue))),t.keepTouched||G(a.touchedFields,e),t.keepDirty||(G(a.dirtyFields,e),a.isDirty=t.defaultValue?ee(e,m(p(d,e))):ee()),!t.keepError&&(G(a.errors,e),S.isValid&&L()),k.state.next({...a}))},clearErrors:e=>{e&&T(e).forEach(e=>G(a.errors,e)),k.state.next({errors:e?a.errors:{}})},unregister:eD,setError:ex,setFocus:(e,t={})=>{let r=p(u,e),a=r&&r._f;if(a){let e=a.refs?a.refs[0]:a.ref;e.focus&&(e.focus(),t.shouldSelect&&j(e.select)&&e.select())}},getFieldState:eS};return{...eM,formControl:eM}}(e);t.current={...a,formState:u}}let c=t.current.control;return c._options=e,D(()=>{let e=c._subscribe({formState:c._proxyFormState,callback:()=>d({...c._formState}),reRenderRoot:!0});return d(e=>({...e,isReady:!0})),c._formState.isReady=!0,e},[c]),a.useEffect(()=>c._disableForm(e.disabled),[c,e.disabled]),a.useEffect(()=>{e.mode&&(c._options.mode=e.mode),e.reValidateMode&&(c._options.reValidateMode=e.reValidateMode)},[c,e.mode,e.reValidateMode]),a.useEffect(()=>{e.errors&&(c._setErrors(e.errors),c._focusError())},[c,e.errors]),a.useEffect(()=>{e.shouldUnregister&&c._subjects.state.next({values:c._getWatch()})},[c,e.shouldUnregister]),a.useEffect(()=>{if(c._proxyFormState.isDirty){let e=c._getDirty();e!==u.isDirty&&c._subjects.state.next({isDirty:e})}},[c,u.isDirty]),a.useEffect(()=>{e.values&&!M(e.values,r.current)?(c._reset(e.values,c._options.resetOptions),r.current=e.values,d(e=>({...e}))):c._resetDefaultValues()},[c,e.values]),a.useEffect(()=>{c._state.mount||(c._setValid(),c._state.mount=!0),c._state.watch&&(c._state.watch=!1,c._subjects.state.next({...c._formState})),c._removeUnmounted()}),t.current.formState=k(u,c),t.current}}};
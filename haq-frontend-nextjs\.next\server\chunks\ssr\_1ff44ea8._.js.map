{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 24, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Haq%20website%20v1/haq-frontend-nextjs/src/components/companies/CompanyDetails.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/companies/CompanyDetails.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/companies/CompanyDetails.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA+S,GAC5U,6EACA", "debugId": null}}, {"offset": {"line": 38, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Haq%20website%20v1/haq-frontend-nextjs/src/components/companies/CompanyDetails.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/companies/CompanyDetails.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/companies/CompanyDetails.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA2R,GACxT,yDACA", "debugId": null}}, {"offset": {"line": 52, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 62, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Haq%20website%20v1/haq-frontend-nextjs/src/components/companies/CompanyReviews.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/companies/CompanyReviews.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/companies/CompanyReviews.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA+S,GAC5U,6EACA", "debugId": null}}, {"offset": {"line": 76, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Haq%20website%20v1/haq-frontend-nextjs/src/components/companies/CompanyReviews.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/companies/CompanyReviews.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/companies/CompanyReviews.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA2R,GACxT,yDACA", "debugId": null}}, {"offset": {"line": 90, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 100, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Haq%20website%20v1/haq-frontend-nextjs/src/app/companies/%5BcompanyId%5D/page.tsx"], "sourcesContent": ["import React from 'react';\nimport { notFound } from 'next/navigation';\nimport CompanyDetails from '@/components/companies/CompanyDetails';\nimport CompanyReviews from '@/components/companies/CompanyReviews';\n\ninterface CompanyPageProps {\n  params: {\n    companyId: string;\n  };\n  searchParams: {\n    page?: string;\n    sort?: string;\n  };\n}\n\n// Server-side data fetching for company details\nasync function getCompanyData(companyId: string) {\n  try {\n    const response = await fetch(`${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'}/api/companies/${companyId}`, {\n      next: { revalidate: 300 }, // 5 minutes cache (CDN_SHORT policy)\n      headers: {\n        'Accept': 'application/json',\n        'User-Agent': 'HAQ-NextJS-SSR'\n      }\n    });\n\n    if (!response.ok) {\n      if (response.status === 404) {\n        return null; // Company not found\n      }\n      throw new Error('Failed to fetch company data');\n    }\n\n    const result = await response.json();\n    return result.success ? result.data.company : null;\n  } catch (error) {\n    console.error('Error fetching company data:', error);\n    return null;\n  }\n}\n\n// Server-side data fetching for company reviews\nasync function getCompanyReviews(companyId: string, page: number = 1, sort: string = 'newest') {\n  try {\n    const params = new URLSearchParams({\n      page: page.toString(),\n      limit: '10',\n      sort\n    });\n\n    const response = await fetch(`${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'}/api/companies/${companyId}/reviews?${params}`, {\n      next: { revalidate: 300 }, // 5 minutes cache (CDN_SHORT policy)\n      headers: {\n        'Accept': 'application/json',\n        'User-Agent': 'HAQ-NextJS-SSR'\n      }\n    });\n\n    if (!response.ok) {\n      throw new Error('Failed to fetch company reviews');\n    }\n\n    const result = await response.json();\n    return result.success ? result.data : null;\n  } catch (error) {\n    console.error('Error fetching company reviews:', error);\n    return null;\n  }\n}\n\nexport default async function CompanyPage({ params, searchParams }: CompanyPageProps) {\n  const { companyId } = params;\n  const page = parseInt(searchParams.page || '1');\n  const sort = searchParams.sort || 'newest';\n\n  // Validate UUID format\n  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;\n  if (!uuidRegex.test(companyId)) {\n    notFound();\n  }\n\n  // Fetch company data and reviews in parallel\n  const [companyData, reviewsData] = await Promise.all([\n    getCompanyData(companyId),\n    getCompanyReviews(companyId, page, sort)\n  ]);\n\n  // If company not found, show 404\n  if (!companyData) {\n    notFound();\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Company Header */}\n      <div className=\"bg-white shadow-sm\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n          <CompanyDetails company={companyData} />\n        </div>\n      </div>\n\n      {/* Company Reviews Section */}\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        <CompanyReviews \n          companyId={companyId}\n          companyName={companyData.name}\n          initialReviewsData={reviewsData}\n          currentPage={page}\n          currentSort={sort}\n        />\n      </div>\n    </div>\n  );\n}\n\n// Generate metadata for SEO\nexport async function generateMetadata({ params }: { params: { companyId: string } }) {\n  const companyData = await getCompanyData(params.companyId);\n  \n  if (!companyData) {\n    return {\n      title: 'Company Not Found - HAQ',\n      description: 'The requested company could not be found.'\n    };\n  }\n\n  return {\n    title: `${companyData.name} Reviews & Company Info - HAQ`,\n    description: `Read employee reviews and company information for ${companyData.name}. ${companyData.total_reviews} reviews with an average rating of ${companyData.average_rating}/5.`,\n    openGraph: {\n      title: `${companyData.name} - Employee Reviews`,\n      description: `${companyData.total_reviews} employee reviews for ${companyData.name}`,\n      images: companyData.logo_url ? [companyData.logo_url] : [],\n    },\n  };\n}\n"], "names": [], "mappings": ";;;;;AACA;AAAA;AACA;AACA;;;;;AAYA,gDAAgD;AAChD,eAAe,eAAe,SAAiB;IAC7C,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,6DAAmC,wBAAwB,eAAe,EAAE,WAAW,EAAE;YACvH,MAAM;gBAAE,YAAY;YAAI;YACxB,SAAS;gBACP,UAAU;gBACV,cAAc;YAChB;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,IAAI,SAAS,MAAM,KAAK,KAAK;gBAC3B,OAAO,MAAM,oBAAoB;YACnC;YACA,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,SAAS,MAAM,SAAS,IAAI;QAClC,OAAO,OAAO,OAAO,GAAG,OAAO,IAAI,CAAC,OAAO,GAAG;IAChD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gCAAgC;QAC9C,OAAO;IACT;AACF;AAEA,gDAAgD;AAChD,eAAe,kBAAkB,SAAiB,EAAE,OAAe,CAAC,EAAE,OAAe,QAAQ;IAC3F,IAAI;QACF,MAAM,SAAS,IAAI,gBAAgB;YACjC,MAAM,KAAK,QAAQ;YACnB,OAAO;YACP;QACF;QAEA,MAAM,WAAW,MAAM,MAAM,GAAG,6DAAmC,wBAAwB,eAAe,EAAE,UAAU,SAAS,EAAE,QAAQ,EAAE;YACzI,MAAM;gBAAE,YAAY;YAAI;YACxB,SAAS;gBACP,UAAU;gBACV,cAAc;YAChB;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,SAAS,MAAM,SAAS,IAAI;QAClC,OAAO,OAAO,OAAO,GAAG,OAAO,IAAI,GAAG;IACxC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mCAAmC;QACjD,OAAO;IACT;AACF;AAEe,eAAe,YAAY,EAAE,MAAM,EAAE,YAAY,EAAoB;IAClF,MAAM,EAAE,SAAS,EAAE,GAAG;IACtB,MAAM,OAAO,SAAS,aAAa,IAAI,IAAI;IAC3C,MAAM,OAAO,aAAa,IAAI,IAAI;IAElC,uBAAuB;IACvB,MAAM,YAAY;IAClB,IAAI,CAAC,UAAU,IAAI,CAAC,YAAY;QAC9B,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD;IACT;IAEA,6CAA6C;IAC7C,MAAM,CAAC,aAAa,YAAY,GAAG,MAAM,QAAQ,GAAG,CAAC;QACnD,eAAe;QACf,kBAAkB,WAAW,MAAM;KACpC;IAED,iCAAiC;IACjC,IAAI,CAAC,aAAa;QAChB,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD;IACT;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,iJAAA,CAAA,UAAc;wBAAC,SAAS;;;;;;;;;;;;;;;;0BAK7B,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,iJAAA,CAAA,UAAc;oBACb,WAAW;oBACX,aAAa,YAAY,IAAI;oBAC7B,oBAAoB;oBACpB,aAAa;oBACb,aAAa;;;;;;;;;;;;;;;;;AAKvB;AAGO,eAAe,iBAAiB,EAAE,MAAM,EAAqC;IAClF,MAAM,cAAc,MAAM,eAAe,OAAO,SAAS;IAEzD,IAAI,CAAC,aAAa;QAChB,OAAO;YACL,OAAO;YACP,aAAa;QACf;IACF;IAEA,OAAO;QACL,OAAO,GAAG,YAAY,IAAI,CAAC,6BAA6B,CAAC;QACzD,aAAa,CAAC,kDAAkD,EAAE,YAAY,IAAI,CAAC,EAAE,EAAE,YAAY,aAAa,CAAC,mCAAmC,EAAE,YAAY,cAAc,CAAC,GAAG,CAAC;QACrL,WAAW;YACT,OAAO,GAAG,YAAY,IAAI,CAAC,mBAAmB,CAAC;YAC/C,aAAa,GAAG,YAAY,aAAa,CAAC,sBAAsB,EAAE,YAAY,IAAI,EAAE;YACpF,QAAQ,YAAY,QAAQ,GAAG;gBAAC,YAAY,QAAQ;aAAC,GAAG,EAAE;QAC5D;IACF;AACF", "debugId": null}}]}
'use client';

import React, { Component, ErrorInfo, ReactNode } from 'react';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
}

interface State {
  hasError: boolean;
  error?: Error;
}

export class ErrorBoundary extends Component<Props, State> {
  public state: State = {
    hasError: false
  };

  public static getDerivedStateFromError(error: Error): State {
    // Update state so the next render will show the fallback UI
    return { hasError: true, error };
  }

  public componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    // Log error details but don't spam console in development
    if (process.env.NODE_ENV === 'production') {
      console.error('ErrorBoundary caught an error:', error, errorInfo);
    }
  }

  public render() {
    if (this.state.hasError) {
      // Return custom fallback UI or default
      return this.props.fallback || (
        <div className="w-full h-full bg-surface-secondary rounded-lg flex items-center justify-center">
          <div className="text-text-secondary text-sm">Unable to load</div>
        </div>
      );
    }

    return this.props.children;
  }
}

// Simple wrapper for images
export const SafeImageWrapper: React.FC<{ children: ReactNode }> = ({ children }) => {
  return (
    <ErrorBoundary 
      fallback={
        <div className="w-full h-full bg-surface-secondary rounded-lg flex items-center justify-center">
          <div className="w-6 h-6 bg-surface-tertiary rounded"></div>
        </div>
      }
    >
      {children}
    </ErrorBoundary>
  );
};

(()=>{var e={};e.id=758,e.ids=[758],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},12909:(e,t,s)=>{"use strict";s.d(t,{DU:()=>c,Qi:()=>l,h8:()=>u,ne:()=>d});var r=s(85663),a=s(43205),n=s.n(a);let i=()=>{let e=process.env.JWT_SECRET;if(!e)throw Error("JWT_SECRET environment variable is required");return e},o=process.env.JWT_EXPIRES_IN||"7d";class u{static{this.SALT_ROUNDS=12}static async hashPassword(e){try{let t=await r.Ay.genSalt(this.SALT_ROUNDS);return await r.Ay.hash(e,t)}catch(e){throw Error("Failed to hash password")}}static async verifyPassword(e,t){try{return await r.Ay.compare(e,t)}catch(e){throw Error("Failed to verify password")}}}class c{static generateToken(e){try{return n().sign(e,i(),{expiresIn:o,algorithm:"HS256"})}catch(e){throw Error("Failed to generate JWT token")}}static verifyToken(e){try{return n().verify(e,i(),{algorithms:["HS256"]})}catch(e){if(e instanceof n().TokenExpiredError)throw Error("Token has expired");if(e instanceof n().JsonWebTokenError)throw Error("Invalid token");throw Error("Token verification failed")}}static extractTokenFromHeader(e){return e&&e.startsWith("Bearer ")?e.substring(7):null}}let l={NAME:"haq_auth_token",OPTIONS:{httpOnly:!0,secure:!0,sameSite:"strict",maxAge:604800,path:"/"}};class d{static isValidEmail(e){return/^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/.test(e)&&e.length<=255}static isValidUsername(e){return/^[a-zA-Z0-9_]{3,50}$/.test(e)}static validatePassword(e){let t=[];return e.length<8&&t.push("Password must be at least 8 characters long"),e.length>128&&t.push("Password must be less than 128 characters"),/[a-z]/.test(e)||t.push("Password must contain at least one lowercase letter"),/[A-Z]/.test(e)||t.push("Password must contain at least one uppercase letter"),/[0-9]/.test(e)||t.push("Password must contain at least one number"),/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(e)||t.push("Password must contain at least one special character"),{isValid:0===t.length,errors:t}}static sanitizeInput(e){return e.trim().replace(/[<>]/g,"").substring(0,1e3)}}},16857:(e,t,s)=>{"use strict";s.r(t),s.d(t,{patchFetch:()=>y,routeModule:()=>g,serverHooks:()=>x,workAsyncStorage:()=>f,workUnitAsyncStorage:()=>w});var r={};s.r(r),s.d(r,{DELETE:()=>m,GET:()=>p,POST:()=>d,PUT:()=>h});var a=s(96559),n=s(48088),i=s(37719),o=s(32190),u=s(12909),c=s(51641),l=s(56621);async function d(e){try{let{email:t,password:s}=await e.json();if(!t||!s)return o.NextResponse.json({success:!1,message:"Email and password are required"},{status:400});let r=u.ne.sanitizeInput(t);if(!u.ne.isValidEmail(r))return o.NextResponse.json({success:!1,message:"Invalid email format"},{status:400});e.headers.get("user-agent");let a=e.headers.get("x-forwarded-for")||e.headers.get("x-real-ip")||"unknown",{data:n,error:i}=await l.ND.from("users").select("user_id, username, email, password_hash, role, created_at, updated_at").eq("email",r).limit(1);if(i)return console.error("Database error fetching user:",i),o.NextResponse.json({success:!1,message:"Internal server error"},{status:500});if(!n||0===n.length)return o.NextResponse.json({success:!1,message:"Invalid email or password"},{status:401});let d=n[0];if(!await u.h8.verifyPassword(s,d.password_hash))return console.warn(`Failed login attempt for email: ${r} from IP: ${a}`),o.NextResponse.json({success:!1,message:"Invalid email or password"},{status:401});let{error:p}=await l.ND.from("users").update({updated_at:new Date().toISOString()}).eq("user_id",d.user_id);p&&console.warn("Failed to update last login timestamp:",p);let h=u.DU.generateToken({user_id:d.user_id,role:d.role});return await c.L.setAuthCookie(h),console.info(`Successful login for user: ${d.username} (${d.user_id}) from IP: ${a}`),o.NextResponse.json({success:!0,message:"Login successful",user:{user_id:d.user_id,username:d.username,email:d.email,role:d.role}},{status:200})}catch(e){if(console.error("Login error:",e),e instanceof SyntaxError)return o.NextResponse.json({success:!1,message:"Invalid JSON in request body"},{status:400});if(e instanceof Error&&e.message.includes("password"))return o.NextResponse.json({success:!1,message:"Authentication failed"},{status:401});if(e instanceof Error&&e.message.includes("JWT"))return o.NextResponse.json({success:!1,message:"Authentication setup failed"},{status:500});return o.NextResponse.json({success:!1,message:"Internal server error"},{status:500})}}async function p(){return o.NextResponse.json({success:!1,message:"Method not allowed"},{status:405})}async function h(){return o.NextResponse.json({success:!1,message:"Method not allowed"},{status:405})}async function m(){return o.NextResponse.json({success:!1,message:"Method not allowed"},{status:405})}let g=new a.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/auth/login/route",pathname:"/api/auth/login",filename:"route",bundlePath:"app/api/auth/login/route"},resolvedPagePath:"D:\\Haq website v1\\haq-frontend-nextjs\\src\\app\\api\\auth\\login\\route.ts",nextConfigOutput:"",userland:r}),{workAsyncStorage:f,workUnitAsyncStorage:w,serverHooks:x}=g;function y(){return(0,i.patchFetch)({workAsyncStorage:f,workUnitAsyncStorage:w})}},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},39727:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},51641:(e,t,s)=>{"use strict";s.d(t,{L:()=>i,b:()=>o});var r=s(44999),a=s(12909),n=s(56621);class i{static async setAuthCookie(e){(await (0,r.UL)()).set(a.Qi.NAME,e,a.Qi.OPTIONS)}static async getAuthToken(){try{let e=(await (0,r.UL)()).get(a.Qi.NAME);return e?.value||null}catch(e){return null}}static async removeAuthCookie(){(await (0,r.UL)()).delete(a.Qi.NAME)}}class o{static async getCurrentUser(){try{let e=await i.getAuthToken();if(!e)return null;let t=a.DU.verifyToken(e),{data:s,error:r}=await n.ND.from("users").select("user_id, username, email, role, created_at, updated_at").eq("user_id",t.user_id).limit(1);if(r||!s||0===s.length)return null;return s[0]}catch(e){return null}}static async isAuthenticated(){return null!==await this.getCurrentUser()}static async isAdmin(){try{let e=await i.getAuthToken();if(!e)return!1;let t=a.DU.verifyToken(e);return"admin"===t.role}catch(e){return!1}}static async verifyTokenAndGetUser(e){try{let t=a.DU.verifyToken(e),{data:s,error:r}=await n.ND.from("users").select("user_id, username, email, role, created_at, updated_at").eq("user_id",t.user_id).limit(1);if(r||!s||0===s.length)return null;return s[0]}catch(e){return null}}}},51906:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=51906,e.exports=t},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56621:(e,t,s)=>{"use strict";s.d(t,{ND:()=>n});var r=s(39398);s(98766);let a=null,n=a=(0,r.createClient)("https://wqbuilazpyxpwyuwuqpi.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6IndxYnVpbGF6cHl4cHd5dXd1cXBpIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTA1NTYyNDMsImV4cCI6MjA2NjEzMjI0M30.GeRI54Rskwdbfm9_lRxy1-7YQ8vA74JWblNF2GRpzrI")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[447,580,398,766,358],()=>s(16857));module.exports=r})();
// Test script for search API functionality
const { createClient } = require('@supabase/supabase-js');

// Load environment variables
require('dotenv').config({ path: '.env.local' });

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

async function testSearchAPI() {
  console.log('🧪 Testing Search API Functionality...\n');

  try {
    // Test 1: Basic search functionality
    console.log('Test 1: Basic search functionality');
    const { data: companies, error: searchError, count } = await supabase
      .from('companies')
      .select(`
        company_id,
        name,
        industry,
        hq_location,
        website,
        logo_url,
        description,
        haq_score,
        created_at
      `, { count: 'exact' })
      .ilike('name', '%tech%')
      .range(0, 9)
      .order('name', { ascending: true });

    if (searchError) {
      console.error('❌ Search error:', searchError);
      return;
    }

    console.log(`✅ Found ${count} companies matching "tech"`);
    console.log(`✅ Retrieved ${companies?.length || 0} companies in first page`);
    
    if (companies && companies.length > 0) {
      console.log('Sample company:', {
        name: companies[0].name,
        industry: companies[0].industry,
        location: companies[0].hq_location
      });
    }

    // Test 2: SQL Injection Protection
    console.log('\nTest 2: SQL Injection Protection');
    const maliciousQuery = "'; DROP TABLE companies; --";
    const { data: injectionTest, error: injectionError } = await supabase
      .from('companies')
      .select('company_id, name')
      .ilike('name', `%${maliciousQuery}%`)
      .limit(1);

    if (injectionError) {
      console.log('✅ SQL injection attempt properly handled (error expected)');
    } else {
      console.log('✅ SQL injection attempt safely processed (no results expected)');
      console.log(`Results: ${injectionTest?.length || 0} companies`);
    }

    // Test 3: Case-insensitive search
    console.log('\nTest 3: Case-insensitive search');
    const { data: upperCase } = await supabase
      .from('companies')
      .select('name')
      .ilike('name', '%TECH%')
      .limit(3);

    const { data: lowerCase } = await supabase
      .from('companies')
      .select('name')
      .ilike('name', '%tech%')
      .limit(3);

    console.log(`✅ Uppercase search: ${upperCase?.length || 0} results`);
    console.log(`✅ Lowercase search: ${lowerCase?.length || 0} results`);
    console.log('✅ Case-insensitive search working correctly');

    // Test 4: Pagination
    console.log('\nTest 4: Pagination');
    const { data: page1, count: totalCount } = await supabase
      .from('companies')
      .select('company_id, name', { count: 'exact' })
      .range(0, 4); // First 5 items

    const { data: page2 } = await supabase
      .from('companies')
      .select('company_id, name')
      .range(5, 9); // Next 5 items

    console.log(`✅ Total companies: ${totalCount}`);
    console.log(`✅ Page 1: ${page1?.length || 0} companies`);
    console.log(`✅ Page 2: ${page2?.length || 0} companies`);

    // Test 5: Empty search handling
    console.log('\nTest 5: Empty search handling');
    const { data: emptySearch } = await supabase
      .from('companies')
      .select('company_id, name')
      .ilike('name', '%nonexistentcompanyname123%')
      .limit(5);

    console.log(`✅ Empty search results: ${emptySearch?.length || 0} companies`);

    console.log('\n🎉 All search API tests completed successfully!');
    console.log('\n📋 Test Summary:');
    console.log('✅ Basic search functionality');
    console.log('✅ SQL injection protection');
    console.log('✅ Case-insensitive search');
    console.log('✅ Pagination support');
    console.log('✅ Empty result handling');

  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

// Run the tests
testSearchAPI();

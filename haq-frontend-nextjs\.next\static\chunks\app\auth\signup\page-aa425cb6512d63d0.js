(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[778],{1780:(e,a,s)=>{Promise.resolve().then(s.bind(s,4820))},4820:(e,a,s)=>{"use strict";s.r(a),s.d(a,{default:()=>d});var r=s(5155),t=s(2115),n=s(2177),l=s(5695),o=s(6874),c=s.n(o),i=s(7297);function d(){let e=(0,l.useRouter)(),[a,s]=(0,t.useState)(!1),[o,d]=(0,t.useState)(null),[m,u]=(0,t.useState)(null),{register:p,handleSubmit:x,watch:h,formState:{errors:f},setError:b,clearErrors:y}=(0,n.mN)({mode:"onBlur",criteriaMode:"all"}),g=h("password"),w=async a=>{s(!0),d(null),u(null),y();try{if(a.password!==a.confirmPassword){b("confirmPassword",{type:"manual",message:"Passwords do not match"}),s(!1);return}let r=await fetch("/api/auth/register",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({username:a.username,email:a.email,password:a.password})}),t=await r.json();t.success?(u("Account created successfully! Redirecting..."),setTimeout(()=>{e.push("/")},2e3)):(d(t.message||"Registration failed. Please try again."),t.message.includes("email")?b("email",{type:"manual",message:t.message}):t.message.includes("username")&&b("username",{type:"manual",message:t.message}))}catch(e){console.error("Registration error:",e),d("Network error. Please check your connection and try again.")}finally{s(!1)}};return(0,r.jsx)("div",{className:"min-h-screen bg-background-primary flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8",children:(0,r.jsxs)("div",{className:"max-w-md w-full space-y-8",children:[(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"flex justify-center mb-6",children:(0,r.jsx)("div",{className:"w-12 h-12 bg-gradient-to-br from-accent-primary to-accent-secondary rounded-xl flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-white font-bold text-xl",children:"H"})})}),(0,r.jsx)("h2",{className:"text-3xl font-bold text-text-primary",children:"Create your account"}),(0,r.jsx)("p",{className:"mt-2 text-sm text-text-secondary",children:"Join HAQ to discover and review companies"})]}),m&&(0,r.jsx)("div",{className:"bg-green-50 border border-green-200 rounded-medium p-4",children:(0,r.jsxs)("div",{className:"flex",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)("svg",{className:"h-5 w-5 text-green-400",viewBox:"0 0 20 20",fill:"currentColor",children:(0,r.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",clipRule:"evenodd"})})}),(0,r.jsx)("div",{className:"ml-3",children:(0,r.jsx)("p",{className:"text-sm font-medium text-green-800",children:m})})]})}),o&&(0,r.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-medium p-4",children:(0,r.jsxs)("div",{className:"flex",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)("svg",{className:"h-5 w-5 text-red-400",viewBox:"0 0 20 20",fill:"currentColor",children:(0,r.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})})}),(0,r.jsx)("div",{className:"ml-3",children:(0,r.jsx)("p",{className:"text-sm font-medium text-red-800",children:o})})]})}),(0,r.jsxs)("form",{className:"mt-8 space-y-6",onSubmit:x(w),children:[(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"username",className:"block text-sm font-medium text-text-primary mb-2",children:"Username"}),(0,r.jsx)("input",{id:"username",type:"text",autoComplete:"username","aria-invalid":f.username?"true":"false",className:"\n                  appearance-none relative block w-full px-3 py-3 border rounded-medium placeholder-text-secondary text-text-primary \n                  focus:outline-none focus:ring-2 focus:ring-accent-primary focus:border-accent-primary focus:z-10 sm:text-sm\n                  ".concat(f.username?"border-red-300 bg-red-50":"border-border-primary bg-surface-primary hover:border-border-secondary","\n                "),placeholder:"Enter your username",...p("username",{required:"Username is required",minLength:{value:3,message:"Username must be at least 3 characters long"},maxLength:{value:50,message:"Username must be less than 50 characters"},pattern:{value:/^[a-zA-Z0-9_]+$/,message:"Username can only contain letters, numbers, and underscores"},validate:e=>!!i.n.isValidUsername(e)||"Username must be 3-50 characters and contain only letters, numbers, and underscores"})}),f.username&&(0,r.jsx)("p",{className:"mt-1 text-sm text-red-600",role:"alert",children:f.username.message})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-text-primary mb-2",children:"Email address"}),(0,r.jsx)("input",{id:"email",type:"email",autoComplete:"email","aria-invalid":f.email?"true":"false",className:"\n                  appearance-none relative block w-full px-3 py-3 border rounded-medium placeholder-text-secondary text-text-primary \n                  focus:outline-none focus:ring-2 focus:ring-accent-primary focus:border-accent-primary focus:z-10 sm:text-sm\n                  ".concat(f.email?"border-red-300 bg-red-50":"border-border-primary bg-surface-primary hover:border-border-secondary","\n                "),placeholder:"Enter your email",...p("email",{required:"Email is required",maxLength:{value:255,message:"Email must be less than 255 characters"},pattern:{value:/^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/,message:"Please enter a valid email address"},validate:e=>!!i.n.isValidEmail(e)||"Please enter a valid email address"})}),f.email&&(0,r.jsx)("p",{className:"mt-1 text-sm text-red-600",role:"alert",children:f.email.message})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-text-primary mb-2",children:"Password"}),(0,r.jsx)("input",{id:"password",type:"password",autoComplete:"new-password","aria-invalid":f.password?"true":"false",className:"\n                  appearance-none relative block w-full px-3 py-3 border rounded-medium placeholder-text-secondary text-text-primary \n                  focus:outline-none focus:ring-2 focus:ring-accent-primary focus:border-accent-primary focus:z-10 sm:text-sm\n                  ".concat(f.password?"border-red-300 bg-red-50":"border-border-primary bg-surface-primary hover:border-border-secondary","\n                "),placeholder:"Create a strong password",...p("password",{required:"Password is required",minLength:{value:8,message:"Password must be at least 8 characters long"},maxLength:{value:128,message:"Password must be less than 128 characters"},validate:e=>{let a=i.n.validatePassword(e);return!!a.isValid||a.errors[0]}})}),f.password&&(0,r.jsx)("p",{className:"mt-1 text-sm text-red-600",role:"alert",children:f.password.message}),(0,r.jsx)("div",{className:"mt-2 text-xs text-text-secondary",children:"Password must contain at least 8 characters with uppercase, lowercase, number, and special character."})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"confirmPassword",className:"block text-sm font-medium text-text-primary mb-2",children:"Confirm Password"}),(0,r.jsx)("input",{id:"confirmPassword",type:"password",autoComplete:"new-password","aria-invalid":f.confirmPassword?"true":"false",className:"\n                  appearance-none relative block w-full px-3 py-3 border rounded-medium placeholder-text-secondary text-text-primary \n                  focus:outline-none focus:ring-2 focus:ring-accent-primary focus:border-accent-primary focus:z-10 sm:text-sm\n                  ".concat(f.confirmPassword?"border-red-300 bg-red-50":"border-border-primary bg-surface-primary hover:border-border-secondary","\n                "),placeholder:"Confirm your password",...p("confirmPassword",{required:"Please confirm your password",validate:e=>e===g||"Passwords do not match"})}),f.confirmPassword&&(0,r.jsx)("p",{className:"mt-1 text-sm text-red-600",role:"alert",children:f.confirmPassword.message})]})]}),(0,r.jsx)("div",{children:(0,r.jsx)("button",{type:"submit",disabled:a,className:"\n                group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-medium text-white \n                focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-accent-primary\n                ".concat(a?"bg-gray-400 cursor-not-allowed":"bg-accent-primary hover:bg-accent-secondary","\n              "),children:a?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("svg",{className:"animate-spin -ml-1 mr-3 h-5 w-5 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,r.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,r.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"Creating account..."]}):"Create account"})}),(0,r.jsx)("div",{className:"text-center",children:(0,r.jsxs)("p",{className:"text-sm text-text-secondary",children:["Already have an account?"," ",(0,r.jsx)(c(),{href:"/auth/login",className:"font-medium text-accent-primary hover:text-accent-secondary transition-colors",children:"Sign in"})]})})]})]})})}},7297:(e,a,s)=>{"use strict";s.d(a,{n:()=>r});class r{static isValidEmail(e){return/^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/.test(e)&&e.length<=255}static isValidUsername(e){return/^[a-zA-Z0-9_]{3,50}$/.test(e)}static validatePassword(e){let a=[];return e.length<8&&a.push("Password must be at least 8 characters long"),e.length>128&&a.push("Password must be less than 128 characters"),/[a-z]/.test(e)||a.push("Password must contain at least one lowercase letter"),/[A-Z]/.test(e)||a.push("Password must contain at least one uppercase letter"),/[0-9]/.test(e)||a.push("Password must contain at least one number"),/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(e)||a.push("Password must contain at least one special character"),{isValid:0===a.length,errors:a}}static sanitizeInput(e){return e.trim().replace(/[<>]/g,"").substring(0,1e3)}}}},e=>{var a=a=>e(e.s=a);e.O(0,[244,558,441,684,358],()=>a(1780)),_N_E=e.O()}]);
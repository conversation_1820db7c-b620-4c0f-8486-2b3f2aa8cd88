import { NextRequest, NextResponse } from 'next/server';
import { ServerAuthHelpers } from './auth-server';

/**
 * Admin Authentication Middleware
 * Verifies JW<PERSON> token and ensures user has admin role
 * Following HAQ-rules.md ADMIN group requirements
 */

export interface AdminAuthResult {
  success: boolean;
  user?: any;
  error?: string;
  status?: number;
}

/**
 * Verify admin authentication for API routes
 * @param request - Next.js request object
 * @returns AdminAuthResult with success status and user data or error
 */
export async function verifyAdminAuth(request: NextRequest): Promise<AdminAuthResult> {
  try {
    // Check if user is authenticated
    const isAuthenticated = await ServerAuthHelpers.isAuthenticated();
    if (!isAuthenticated) {
      return {
        success: false,
        error: 'User is not authenticated',
        status: 401
      };
    }

    // Check if user has admin role
    const isAdmin = await ServerAuthHelpers.isAdmin();
    if (!isAdmin) {
      return {
        success: false,
        error: 'Unauthorized access: User does not have admin privileges',
        status: 401
      };
    }

    // Get current user data
    const user = await ServerAuthHelpers.getCurrentUser();
    if (!user) {
      return {
        success: false,
        error: 'Failed to retrieve user data',
        status: 401
      };
    }

    return {
      success: true,
      user
    };
  } catch (error) {
    console.error('Admin auth verification error:', error);
    return {
      success: false,
      error: 'Internal authentication error',
      status: 500
    };
  }
}

/**
 * Higher-order function to wrap API route handlers with admin authentication
 * @param handler - The API route handler function
 * @returns Wrapped handler with admin auth check
 */
export function withAdminAuth(
  handler: (request: NextRequest, context: any, user: any) => Promise<NextResponse>
) {
  return async (request: NextRequest, context: any = {}): Promise<NextResponse> => {
    // Verify admin authentication
    const authResult = await verifyAdminAuth(request);
    
    if (!authResult.success) {
      return NextResponse.json(
        { 
          success: false, 
          message: authResult.error || 'Authentication failed' 
        },
        { status: authResult.status || 401 }
      );
    }

    // Call the original handler with authenticated user
    try {
      return await handler(request, context, authResult.user);
    } catch (error) {
      console.error('Admin API handler error:', error);
      return NextResponse.json(
        { 
          success: false, 
          message: 'Internal server error' 
        },
        { status: 500 }
      );
    }
  };
}

/**
 * Middleware function for admin route protection
 * Can be used in middleware.ts for route-level protection
 */
export async function adminRouteMiddleware(request: NextRequest): Promise<NextResponse | null> {
  // Only apply to admin routes
  if (!request.nextUrl.pathname.startsWith('/admin')) {
    return null; // Continue to next middleware
  }

  const authResult = await verifyAdminAuth(request);
  
  if (!authResult.success) {
    // Redirect to login page with return URL
    const loginUrl = new URL('/auth/login', request.url);
    loginUrl.searchParams.set('returnUrl', request.nextUrl.pathname);
    loginUrl.searchParams.set('error', 'admin_required');
    
    return NextResponse.redirect(loginUrl);
  }

  // Continue to the admin route
  return NextResponse.next();
}

/**
 * Utility function to check admin status in server components
 * @returns Promise<boolean> - True if current user is admin
 */
export async function isCurrentUserAdmin(): Promise<boolean> {
  try {
    return await ServerAuthHelpers.isAdmin();
  } catch (error) {
    console.error('Error checking admin status:', error);
    return false;
  }
}

/**
 * Utility function to get current admin user in server components
 * @returns Promise<any | null> - Admin user data or null
 */
export async function getCurrentAdminUser(): Promise<any | null> {
  try {
    const isAdmin = await ServerAuthHelpers.isAdmin();
    if (!isAdmin) {
      return null;
    }
    
    return await ServerAuthHelpers.getCurrentUser();
  } catch (error) {
    console.error('Error getting admin user:', error);
    return null;
  }
}

import React, { useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import { Search, Shield, Users, Star, TrendingUp, AlertTriangle, CheckCircle, ArrowRight, MessageCircle, DollarSign } from 'lucide-react';
import { SearchBar } from '../components/common/SearchBar';
import { CompanyCard } from '../components/common/CompanyCard';
import { StatsCard } from '../components/common/StatsCard';

// Mock data
const featuredCompanies = [
  {
    id: 1,
    name: 'TechFlow Solutions',
    logo: 'https://images.pexels.com/photos/3184398/pexels-photo-3184398.jpeg?auto=compress&cs=tinysrgb&w=100',
    haqScore: 4.2,
    totalReviews: 156,
    industry: 'Technology',
    location: 'Karachi',
    redFlags: ['Overtime Issues'],
    greenFlags: ['Good Benefits', 'Learning Opportunities']
  },
  {
    id: 2,
    name: 'Innovate Marketing',
    logo: 'https://images.pexels.com/photos/3184291/pexels-photo-3184291.jpeg?auto=compress&cs=tinysrgb&w=100',
    haqScore: 3.8,
    totalReviews: 89,
    industry: 'Marketing',
    location: 'Lahore',
    redFlags: ['Work-Life Balance'],
    greenFlags: ['Creative Environment', 'Fair Pay']
  },
  {
    id: 3,
    name: 'Prime Logistics',
    logo: 'https://images.pexels.com/photos/3184357/pexels-photo-3184357.jpeg?auto=compress&cs=tinysrgb&w=100',
    haqScore: 4.5,
    totalReviews: 234,
    industry: 'Logistics',
    location: 'Islamabad',
    redFlags: [],
    greenFlags: ['Excellent Management', 'Timely Payments', 'Growth Opportunities']
  }
];

const stats = [
  { label: 'Company Reviews', value: '12,456', icon: Shield, color: 'primary' },
  { label: 'Anonymous Users', value: '8,234', icon: Users, color: 'secondary' },
  { label: 'Salary Reports', value: '5,678', icon: TrendingUp, color: 'warning' },
  { label: 'Issues Reported', value: '1,234', icon: AlertTriangle, color: 'danger' }
] as const;

export const HomePage: React.FC = () => {
  const [searchQuery, setSearchQuery] = useState('');

  return (
    <div className="min-h-screen bg-background-primary">
      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-background-primary via-background-secondary to-background-primary text-text-primary overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute top-20 left-10 w-32 h-32 bg-accent-primary rounded-full blur-3xl"></div>
          <div className="absolute bottom-20 right-10 w-40 h-40 bg-accent-secondary rounded-full blur-3xl"></div>
        </div>
        
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
          <div className="text-center max-w-4xl mx-auto">
            <h1 className="text-4xl md:text-display-title font-bold mb-6 animate-fade-in uppercase tracking-wider">
              Your Rights, Your Voice
              <span className="block text-2xl md:text-3xl font-normal mt-2 text-accent-primary font-urdu">
                آپ کے حقوق، آپ کی آواز
              </span>
            </h1>
            <p className="text-lg md:text-xl text-text-secondary mb-8 animate-slide-up max-w-3xl mx-auto">
              Empowering Pakistani employees through transparency, anonymous reviews, and workplace accountability. 
              Combat exploitation with honest feedback from real employees.
            </p>
            
            {/* Search Bar */}
            <div className="max-w-2xl mx-auto mb-8 animate-slide-up">
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Search className="h-5 w-5 text-text-secondary" />
                </div>
                <input
                  type="text"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="block w-full pl-10 pr-20 py-4 border border-border-primary rounded-medium bg-surface-primary placeholder-text-secondary focus:outline-none focus:ring-2 focus:ring-accent-primary focus:border-accent-primary text-text-primary"
                  placeholder="Search companies, reviews, or salaries..."
                />
                <button className="absolute right-2 top-2 bottom-2 bg-accent-primary hover:bg-accent-secondary text-text-on-accent px-6 rounded-lg font-semibold transition-all duration-200 hover:shadow-glow">
                  Search
                </button>
              </div>
            </div>

            {/* Quick Actions */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center animate-slide-up">
              <Link
                to="/review/submit"
                className="bg-accent-primary hover:bg-accent-secondary text-text-on-accent px-6 py-3 rounded-lg font-semibold transition-all duration-200 flex items-center space-x-2 group hover:shadow-glow-lg transform hover:-translate-y-1"
              >
                <Users className="w-5 h-5" />
                <span>Write Anonymous Review</span>
                <ArrowRight className="w-4 h-4 group-hover:translate-x-1 transition-transform" />
              </Link>
              <Link
                to="/companies"
                className="border-2 border-accent-primary text-accent-primary hover:bg-accent-primary hover:text-text-on-accent px-6 py-3 rounded-lg font-semibold transition-all duration-200 flex items-center space-x-2"
              >
                <Search className="w-5 h-5" />
                <span>Browse Companies</span>
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-16 bg-surface-primary">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
            {stats.map((stat, index) => (
              <div
                key={stat.label}
                className="bg-background-secondary rounded-medium p-6 border border-border-primary hover:border-accent-primary transition-all duration-200 animate-slide-up text-center group"
                style={{ animationDelay: `${index * 100}ms` }}
              >
                <div className="flex items-center justify-center mb-4">
                  <div className="p-3 rounded-lg bg-surface-secondary group-hover:bg-accent-primary/20 transition-colors duration-200">
                    <stat.icon className="w-6 h-6 text-accent-primary" />
                  </div>
                </div>
                <p className="text-data-readout font-bold text-text-primary mb-1">{stat.value}</p>
                <p className="text-body-label text-text-secondary">{stat.label}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Featured Companies */}
      <section className="py-16 bg-background-primary">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-module-header font-semibold text-accent-primary mb-4 uppercase tracking-wider">
              Featured Companies
            </h2>
            <p className="text-lg text-text-secondary max-w-2xl mx-auto">
              Discover companies with the highest Haq Scores and positive employee feedback across Pakistan.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
            {featuredCompanies.map((company, index) => (
              <CompanyCard key={company.id} company={company} delay={index * 100} />
            ))}
          </div>

          <div className="text-center">
            <Link
              to="/companies"
              className="inline-flex items-center space-x-2 bg-accent-primary hover:bg-accent-secondary text-text-on-accent px-6 py-3 rounded-lg font-semibold transition-all duration-200 hover:shadow-glow transform hover:-translate-y-0.5"
            >
              <span>View All Companies</span>
              <ArrowRight className="w-5 h-5" />
            </Link>
          </div>
        </div>
      </section>

      {/* How It Works Section */}
      <section className="py-16 bg-surface-primary">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-module-header font-semibold text-accent-primary mb-4 uppercase tracking-wider">
              How Haq Works
            </h2>
            <p className="text-lg text-text-secondary max-w-2xl mx-auto">
              Built specifically for Pakistani employees to combat workplace exploitation and promote transparency.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div className="text-center p-6 group">
              <div className="w-20 h-20 bg-surface-secondary rounded-medium flex items-center justify-center mx-auto mb-4 group-hover:bg-accent-primary/20 transition-all duration-200 relative overflow-hidden">
                <div className="absolute inset-0 bg-gradient-to-br from-accent-primary/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-200"></div>
                <Search className="w-8 h-8 text-accent-primary relative z-10" />
              </div>
              <h3 className="text-lg font-semibold text-text-primary mb-3">Search & Discover</h3>
              <p className="text-text-secondary text-sm">
                Find company reviews, salary data, and insights to make informed career decisions.
              </p>
            </div>

            <div className="text-center p-6 group">
              <div className="w-20 h-20 bg-surface-secondary rounded-medium flex items-center justify-center mx-auto mb-4 group-hover:bg-accent-primary/20 transition-all duration-200 relative overflow-hidden">
                <div className="absolute inset-0 bg-gradient-to-br from-accent-primary/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-200"></div>
                <Users className="w-8 h-8 text-accent-primary relative z-10" />
              </div>
              <h3 className="text-lg font-semibold text-text-primary mb-3">Share Your Experience</h3>
              <p className="text-text-secondary text-sm">
                Anonymously submit your review or report workplace issues. Your voice matters.
              </p>
            </div>

            <div className="text-center p-6 group">
              <div className="w-20 h-20 bg-surface-secondary rounded-medium flex items-center justify-center mx-auto mb-4 group-hover:bg-accent-primary/20 transition-all duration-200 relative overflow-hidden">
                <div className="absolute inset-0 bg-gradient-to-br from-accent-primary/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-200"></div>
                <MessageCircle className="w-8 h-8 text-accent-primary relative z-10" />
              </div>
              <h3 className="text-lg font-semibold text-text-primary mb-3">Join the Discussion</h3>
              <p className="text-text-secondary text-sm">
                Participate in our community forum to share advice and discuss workplace rights.
              </p>
            </div>

            <div className="text-center p-6 group">
              <div className="w-20 h-20 bg-surface-secondary rounded-medium flex items-center justify-center mx-auto mb-4 group-hover:bg-accent-primary/20 transition-all duration-200 relative overflow-hidden">
                <div className="absolute inset-0 bg-gradient-to-br from-accent-primary/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-200"></div>
                <CheckCircle className="w-8 h-8 text-accent-primary relative z-10" />
              </div>
              <h3 className="text-lg font-semibold text-text-primary mb-3">Build Trust</h3>
              <p className="text-text-secondary text-sm">
                Promote transparency and encourage ethical workplaces for everyone.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-gradient-to-r from-accent-primary to-accent-secondary text-text-on-accent relative overflow-hidden">
        <div className="absolute inset-0 bg-black/20"></div>
        <div className="relative max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl font-bold mb-4 uppercase tracking-wide">
            Ready to Fight Exploitation?
          </h2>
          <p className="text-lg mb-8 opacity-90">
            Join thousands of Pakistani employees who are building a more transparent and fair workplace culture.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              to="/review/submit"
              className="bg-text-on-accent text-accent-primary hover:bg-gray-100 px-8 py-3 rounded-lg font-semibold transition-all duration-200 flex items-center justify-center space-x-2 transform hover:-translate-y-1"
            >
              <Users className="w-5 h-5" />
              <span>Share Your Experience</span>
            </Link>
            <Link
              to="/community"
              className="border-2 border-text-on-accent text-text-on-accent hover:bg-text-on-accent hover:text-accent-primary px-8 py-3 rounded-lg font-semibold transition-all duration-200 flex items-center justify-center space-x-2"
            >
              <MessageCircle className="w-5 h-5" />
              <span>Join Community</span>
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
};
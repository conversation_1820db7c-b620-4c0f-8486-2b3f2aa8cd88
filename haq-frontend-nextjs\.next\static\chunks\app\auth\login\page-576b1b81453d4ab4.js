(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[859],{4177:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>d});var r=a(5155),t=a(2115),n=a(2177),l=a(5695),i=a(6874),c=a.n(i),o=a(7297);function d(){let e=(0,l.useRouter)(),[s,a]=(0,t.useState)(!1),[i,d]=(0,t.useState)(null),[m,u]=(0,t.useState)(null),{register:x,handleSubmit:p,formState:{errors:h},setError:y,clearErrors:f}=(0,n.mN)({mode:"onBlur"}),g=async s=>{a(!0),d(null),u(null),f();try{let a=await fetch("/api/auth/login",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:s.email,password:s.password})}),r=await a.json();r.success?(u("Login successful! Redirecting..."),setTimeout(()=>{e.push("/")},1500)):(d(r.message||"Login failed. Please try again."),(r.message.includes("email")||r.message.includes("password"))&&(y("email",{type:"manual",message:"Invalid email or password"}),y("password",{type:"manual",message:"Invalid email or password"})))}catch(e){console.error("Login error:",e),d("Network error. Please check your connection and try again.")}finally{a(!1)}};return(0,r.jsx)("div",{className:"min-h-screen bg-background-primary flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8",children:(0,r.jsxs)("div",{className:"max-w-md w-full space-y-8",children:[(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"flex justify-center mb-6",children:(0,r.jsx)("div",{className:"w-12 h-12 bg-gradient-to-br from-accent-primary to-accent-secondary rounded-xl flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-white font-bold text-xl",children:"H"})})}),(0,r.jsx)("h2",{className:"text-3xl font-bold text-text-primary",children:"Sign in to your account"}),(0,r.jsx)("p",{className:"mt-2 text-sm text-text-secondary",children:"Welcome back to HAQ"})]}),m&&(0,r.jsx)("div",{className:"bg-green-50 border border-green-200 rounded-medium p-4",children:(0,r.jsxs)("div",{className:"flex",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)("svg",{className:"h-5 w-5 text-green-400",viewBox:"0 0 20 20",fill:"currentColor",children:(0,r.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",clipRule:"evenodd"})})}),(0,r.jsx)("div",{className:"ml-3",children:(0,r.jsx)("p",{className:"text-sm font-medium text-green-800",children:m})})]})}),i&&(0,r.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-medium p-4",children:(0,r.jsxs)("div",{className:"flex",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)("svg",{className:"h-5 w-5 text-red-400",viewBox:"0 0 20 20",fill:"currentColor",children:(0,r.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})})}),(0,r.jsx)("div",{className:"ml-3",children:(0,r.jsx)("p",{className:"text-sm font-medium text-red-800",children:i})})]})}),(0,r.jsxs)("form",{className:"mt-8 space-y-6",onSubmit:p(g),children:[(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-text-primary mb-2",children:"Email address"}),(0,r.jsx)("input",{id:"email",type:"email",autoComplete:"email","aria-invalid":h.email?"true":"false",className:"\n                  appearance-none relative block w-full px-3 py-3 border rounded-medium placeholder-text-secondary text-text-primary \n                  focus:outline-none focus:ring-2 focus:ring-accent-primary focus:border-accent-primary focus:z-10 sm:text-sm\n                  ".concat(h.email?"border-red-300 bg-red-50":"border-border-primary bg-surface-primary hover:border-border-secondary","\n                "),placeholder:"Enter your email",...x("email",{required:"Email is required",maxLength:{value:255,message:"Email must be less than 255 characters"},pattern:{value:/^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/,message:"Please enter a valid email address"},validate:e=>!!o.n.isValidEmail(e)||"Please enter a valid email address"})}),h.email&&(0,r.jsx)("p",{className:"mt-1 text-sm text-red-600",role:"alert",children:h.email.message})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-text-primary mb-2",children:"Password"}),(0,r.jsx)("input",{id:"password",type:"password",autoComplete:"current-password","aria-invalid":h.password?"true":"false",className:"\n                  appearance-none relative block w-full px-3 py-3 border rounded-medium placeholder-text-secondary text-text-primary \n                  focus:outline-none focus:ring-2 focus:ring-accent-primary focus:border-accent-primary focus:z-10 sm:text-sm\n                  ".concat(h.password?"border-red-300 bg-red-50":"border-border-primary bg-surface-primary hover:border-border-secondary","\n                "),placeholder:"Enter your password",...x("password",{required:"Password is required",minLength:{value:1,message:"Password is required"}})}),h.password&&(0,r.jsx)("p",{className:"mt-1 text-sm text-red-600",role:"alert",children:h.password.message})]})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("input",{id:"rememberMe",type:"checkbox",className:"h-4 w-4 text-accent-primary focus:ring-accent-primary border-border-primary rounded",...x("rememberMe")}),(0,r.jsx)("label",{htmlFor:"rememberMe",className:"ml-2 block text-sm text-text-secondary",children:"Remember me"})]}),(0,r.jsx)("div",{className:"text-sm",children:(0,r.jsx)(c(),{href:"/auth/forgot-password",className:"font-medium text-accent-primary hover:text-accent-secondary transition-colors",children:"Forgot your password?"})})]}),(0,r.jsx)("div",{children:(0,r.jsx)("button",{type:"submit",disabled:s,className:"\n                group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-medium text-white \n                focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-accent-primary\n                ".concat(s?"bg-gray-400 cursor-not-allowed":"bg-accent-primary hover:bg-accent-secondary","\n              "),children:s?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("svg",{className:"animate-spin -ml-1 mr-3 h-5 w-5 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,r.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,r.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"Signing in..."]}):"Sign in"})}),(0,r.jsx)("div",{className:"text-center",children:(0,r.jsxs)("p",{className:"text-sm text-text-secondary",children:["Don't have an account?"," ",(0,r.jsx)(c(),{href:"/auth/signup",className:"font-medium text-accent-primary hover:text-accent-secondary transition-colors",children:"Create one now"})]})})]}),(0,r.jsx)("div",{className:"mt-6 text-center",children:(0,r.jsxs)("p",{className:"text-xs text-text-secondary",children:["By signing in, you agree to our"," ",(0,r.jsx)(c(),{href:"/privacy",className:"text-accent-primary hover:text-accent-secondary",children:"Privacy Policy"})," ","and"," ",(0,r.jsx)(c(),{href:"/terms",className:"text-accent-primary hover:text-accent-secondary",children:"Terms of Service"})]})})]})})}},7297:(e,s,a)=>{"use strict";a.d(s,{n:()=>r});class r{static isValidEmail(e){return/^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/.test(e)&&e.length<=255}static isValidUsername(e){return/^[a-zA-Z0-9_]{3,50}$/.test(e)}static validatePassword(e){let s=[];return e.length<8&&s.push("Password must be at least 8 characters long"),e.length>128&&s.push("Password must be less than 128 characters"),/[a-z]/.test(e)||s.push("Password must contain at least one lowercase letter"),/[A-Z]/.test(e)||s.push("Password must contain at least one uppercase letter"),/[0-9]/.test(e)||s.push("Password must contain at least one number"),/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(e)||s.push("Password must contain at least one special character"),{isValid:0===s.length,errors:s}}static sanitizeInput(e){return e.trim().replace(/[<>]/g,"").substring(0,1e3)}}},9475:(e,s,a)=>{Promise.resolve().then(a.bind(a,4177))}},e=>{var s=s=>e(e.s=s);e.O(0,[244,558,441,684,358],()=>s(9475)),_N_E=e.O()}]);
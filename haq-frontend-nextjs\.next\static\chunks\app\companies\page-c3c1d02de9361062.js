(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[377],{444:(e,s,a)=>{"use strict";a.d(s,{default:()=>h});var r=a(5155),t=a(2115),l=a(5695),n=a(2355),i=a(3052),c=a(7924),d=a(6932),o=a(3227),m=a(5304);let h=()=>{let e=(0,l.useRouter)(),s=(0,l.useSearchParams)(),[a,h]=(0,t.useState)(null),[x,g]=(0,t.useState)(!0),[u,p]=(0,t.useState)(s.get("q")||""),[f,A]=(0,t.useState)(!1),y=parseInt(s.get("page")||"1"),b=s.get("q")||"",j=async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1,s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";g(!0);try{let a=new URLSearchParams;a.append("page",e.toString()),a.append("limit","12"),s.trim()&&a.append("q",s.trim());let r=await fetch("/api/companies?".concat(a)),t=await r.json();t.success?h(t.data):(console.error("Failed to fetch companies:",t.message),h(null))}catch(e){console.error("Error fetching companies:",e),h(null)}finally{g(!1)}};(0,t.useEffect)(()=>{j(y,b)},[y,b]),(0,t.useEffect)(()=>{p(b)},[b]);let v=a=>{let r=new URLSearchParams(s);r.set("page",a.toString()),e.push("/companies?".concat(r.toString()))};return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm p-6",children:[(0,r.jsx)("form",{onSubmit:a=>{a.preventDefault();let r=new URLSearchParams(s);u.trim()?r.set("q",u.trim()):r.delete("q"),r.delete("page"),e.push("/companies?".concat(r.toString()))},className:"mb-4",children:(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(c.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400"}),(0,r.jsx)("input",{type:"text",placeholder:"Search companies by name or industry...",value:u,onChange:e=>p(e.target.value),className:"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]})}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("button",{onClick:()=>A(!f),className:"flex items-center space-x-2 px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors",children:[(0,r.jsx)(d.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{children:"Filters"})]}),a&&(0,r.jsxs)("div",{className:"text-sm text-gray-600",children:[a.pagination.total," companies found",a.search&&(0,r.jsxs)("span",{children:[' for "',a.search,'"']})]})]})]}),x?(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:Array.from({length:6}).map((e,s)=>(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm p-6 animate-pulse",children:[(0,r.jsxs)("div",{className:"flex items-start space-x-4 mb-4",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-gray-200 rounded-lg"}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded w-3/4 mb-2"}),(0,r.jsx)("div",{className:"h-3 bg-gray-200 rounded w-1/2"})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("div",{className:"h-3 bg-gray-200 rounded w-full"}),(0,r.jsx)("div",{className:"h-3 bg-gray-200 rounded w-2/3"})]})]},s))}):a&&a.companies.length>0?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:a.companies.map((e,s)=>(0,r.jsx)(m.I,{company:e,delay:50*s},e.company_id))}),(()=>{if(!(null==a?void 0:a.pagination)||a.pagination.totalPages<=1)return null;let{pagination:e}=a,s=[],t=Math.max(1,e.page-Math.floor(2.5)),l=Math.min(e.totalPages,t+5-1);l-t+1<5&&(t=Math.max(1,l-5+1));for(let e=t;e<=l;e++)s.push(e);return(0,r.jsxs)("div",{className:"flex items-center justify-between border-t border-gray-200 bg-white px-4 py-3 sm:px-6 mt-8",children:[(0,r.jsxs)("div",{className:"flex flex-1 justify-between sm:hidden",children:[(0,r.jsx)("button",{onClick:()=>v(e.page-1),disabled:!e.hasPrev,className:"relative inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed",children:"Previous"}),(0,r.jsx)("button",{onClick:()=>v(e.page+1),disabled:!e.hasNext,className:"relative ml-3 inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed",children:"Next"})]}),(0,r.jsxs)("div",{className:"hidden sm:flex sm:flex-1 sm:items-center sm:justify-between",children:[(0,r.jsx)("div",{children:(0,r.jsxs)("p",{className:"text-sm text-gray-700",children:["Showing"," ",(0,r.jsx)("span",{className:"font-medium",children:(e.page-1)*e.limit+1})," ","to"," ",(0,r.jsx)("span",{className:"font-medium",children:Math.min(e.page*e.limit,e.total)})," ","of"," ",(0,r.jsx)("span",{className:"font-medium",children:e.total})," results"]})}),(0,r.jsx)("div",{children:(0,r.jsxs)("nav",{className:"isolate inline-flex -space-x-px rounded-md shadow-sm","aria-label":"Pagination",children:[(0,r.jsx)("button",{onClick:()=>v(e.page-1),disabled:!e.hasPrev,className:"relative inline-flex items-center rounded-l-md px-2 py-2 text-gray-400 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0 disabled:opacity-50 disabled:cursor-not-allowed",children:(0,r.jsx)(n.A,{className:"h-5 w-5"})}),s.map(s=>(0,r.jsx)("button",{onClick:()=>v(s),className:"relative inline-flex items-center px-4 py-2 text-sm font-semibold ".concat(s===e.page?"z-10 bg-blue-600 text-white focus:z-20 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-blue-600":"text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0"),children:s},s)),(0,r.jsx)("button",{onClick:()=>v(e.page+1),disabled:!e.hasNext,className:"relative inline-flex items-center rounded-r-md px-2 py-2 text-gray-400 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0 disabled:opacity-50 disabled:cursor-not-allowed",children:(0,r.jsx)(i.A,{className:"h-5 w-5"})})]})})]})]})})()]}):(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm p-12 text-center",children:[(0,r.jsx)(o.A,{className:"mx-auto h-12 w-12 text-gray-400 mb-4"}),(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No companies found"}),(0,r.jsx)("p",{className:"text-gray-600 mb-6",children:(null==a?void 0:a.search)?'No companies match your search for "'.concat(a.search,'".'):"No companies are available at the moment."}),(null==a?void 0:a.search)&&(0,r.jsx)("button",{onClick:()=>{p(""),e.push("/companies")},className:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700",children:"Clear Search"})]})]})}},2355:(e,s,a)=>{"use strict";a.d(s,{A:()=>r});let r=(0,a(9946).A)("chevron-left",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]])},3052:(e,s,a)=>{"use strict";a.d(s,{A:()=>r});let r=(0,a(9946).A)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},3227:(e,s,a)=>{"use strict";a.d(s,{A:()=>r});let r=(0,a(9946).A)("building-2",[["path",{d:"M6 22V4a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v18Z",key:"1b4qmf"}],["path",{d:"M6 12H4a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2h2",key:"i71pzd"}],["path",{d:"M18 9h2a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2h-2",key:"10jefs"}],["path",{d:"M10 6h4",key:"1itunk"}],["path",{d:"M10 10h4",key:"tcdvrf"}],["path",{d:"M10 14h4",key:"kelpxr"}],["path",{d:"M10 18h4",key:"1ulq68"}]])},5304:(e,s,a)=>{"use strict";a.d(s,{I:()=>p});var r=a(5155),t=a(2115),l=a(6874),n=a.n(l),i=a(6766),c=a(4516),d=a(8564),o=a(7580),m=a(646),h=a(1243);class x extends t.Component{static getDerivedStateFromError(e){return{hasError:!0,error:e}}componentDidCatch(e,s){console.error("ErrorBoundary caught an error:",e,s)}render(){return this.state.hasError?this.props.fallback||(0,r.jsx)("div",{className:"w-full h-full bg-surface-secondary rounded-lg flex items-center justify-center",children:(0,r.jsx)("div",{className:"text-text-secondary text-sm",children:"Unable to load"})}):this.props.children}constructor(...e){super(...e),this.state={hasError:!1}}}let g=e=>{let{children:s}=e;return(0,r.jsx)(x,{fallback:(0,r.jsx)("div",{className:"w-full h-full bg-surface-secondary rounded-lg flex items-center justify-center",children:(0,r.jsx)("div",{className:"w-6 h-6 bg-surface-tertiary rounded"})}),children:s})},u=e=>{let{src:s,alt:a,className:l}=e,[n,c]=(0,t.useState)(!1),[d,o]=(0,t.useState)(!1),[m,h]=(0,t.useState)(s);return((0,t.useEffect)(()=>{c(!0),h(s),o(!1)},[s]),n)?d&&"/placeholder-company.svg"===m?(0,r.jsx)("div",{className:"w-full h-full bg-surface-secondary rounded-lg flex items-center justify-center",children:(0,r.jsx)("div",{className:"w-6 h-6 bg-surface-tertiary rounded"})}):(0,r.jsx)(i.default,{src:m,alt:a,fill:!0,className:l,sizes:"48px",priority:!1,onError:()=>{console.warn("Image failed to load: ".concat(m)),o(!0),h("/placeholder-company.svg")},placeholder:"blur",blurDataURL:"data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAhEAACAQMDBQAAAAAAAAAAAAABAgMABAUGIWGRkqGx0f/EABUBAQEAAAAAAAAAAAAAAAAAAAMF/8QAGhEAAgIDAAAAAAAAAAAAAAAAAAECEgMRkf/aAAwDAQACEQMRAD8AltJagyeH0AthI5xdrLcNM91BF5pX2HaH9bcfaSXWGaRmknyJckliyjqTzSlT54b6bk+h0R+Kcp/9k="}):(0,r.jsx)("div",{className:"w-full h-full bg-surface-secondary animate-pulse rounded-lg flex items-center justify-center",children:(0,r.jsx)("div",{className:"w-6 h-6 bg-surface-tertiary rounded"})})},p=e=>{let{company:s,delay:a=0}=e,t=e=>e>=4?"text-green-400":e>=3?"text-yellow-400":"text-red-400",l=s.company_id||s.id;return(0,r.jsx)(n(),{href:"/companies/".concat(l),children:(0,r.jsxs)("div",{className:"bg-surface-primary border border-border-primary rounded-medium p-6 hover:border-accent-primary transition-all duration-200 animate-slide-up group cursor-pointer",style:{animationDelay:"".concat(a,"ms")},children:[(0,r.jsxs)("div",{className:"flex items-start space-x-4 mb-4",children:[(0,r.jsx)("div",{className:"relative w-12 h-12 rounded-lg overflow-hidden bg-surface-secondary flex-shrink-0",children:(0,r.jsx)(g,{children:(0,r.jsx)(u,{src:s.logo_url||"/placeholder-company.svg",alt:"".concat(s.name," logo"),className:"object-cover"})})}),(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-text-primary group-hover:text-accent-primary transition-colors duration-200 truncate",children:s.name}),(0,r.jsxs)("div",{className:"flex items-center space-x-4 text-sm text-text-secondary",children:[s.industry&&(0,r.jsx)("span",{children:s.industry}),s.location&&(0,r.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,r.jsx)(c.A,{className:"w-3 h-3"}),(0,r.jsx)("span",{children:s.location})]})]})]})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("div",{className:"px-3 py-1 rounded-lg ".concat((e=>e>=4?"bg-green-400/20":e>=3?"bg-yellow-400/20":"bg-red-400/20")(s.haq_score||0)),children:(0,r.jsx)("span",{className:"font-bold ".concat(t(s.haq_score||0)),children:(s.haq_score||0).toFixed(1)})}),(0,r.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,r.jsx)(d.A,{className:"w-4 h-4 ".concat(t(s.haq_score||0)),fill:"currentColor"}),(0,r.jsx)("span",{className:"text-text-secondary text-sm",children:"Haq Score"})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-1 text-text-secondary text-sm",children:[(0,r.jsx)(o.A,{className:"w-4 h-4"}),(0,r.jsxs)("span",{children:[s.total_reviews||0," reviews"]})]})]}),(0,r.jsxs)("div",{className:"space-y-3",children:[s.greenFlags&&s.greenFlags.length>0&&(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[(0,r.jsx)(m.A,{className:"w-4 h-4 text-green-400"}),(0,r.jsx)("span",{className:"text-sm font-medium text-text-primary",children:"Positives"})]}),(0,r.jsxs)("div",{className:"flex flex-wrap gap-1",children:[s.greenFlags.slice(0,2).map((e,s)=>(0,r.jsx)("span",{className:"px-2 py-1 bg-green-400/20 text-green-400 text-xs rounded-lg",children:e},s)),s.greenFlags.length>2&&(0,r.jsxs)("span",{className:"px-2 py-1 bg-surface-secondary text-text-secondary text-xs rounded-lg",children:["+",s.greenFlags.length-2," more"]})]})]}),s.redFlags&&s.redFlags.length>0&&(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[(0,r.jsx)(h.A,{className:"w-4 h-4 text-red-400"}),(0,r.jsx)("span",{className:"text-sm font-medium text-text-primary",children:"Issues"})]}),(0,r.jsxs)("div",{className:"flex flex-wrap gap-1",children:[s.redFlags.slice(0,2).map((e,s)=>(0,r.jsx)("span",{className:"px-2 py-1 bg-red-400/20 text-red-400 text-xs rounded-lg",children:e},s)),s.redFlags.length>2&&(0,r.jsxs)("span",{className:"px-2 py-1 bg-surface-secondary text-text-secondary text-xs rounded-lg",children:["+",s.redFlags.length-2," more"]})]})]})]}),(0,r.jsx)("div",{className:"mt-4 pt-4 border-t border-border-primary",children:(0,r.jsx)("span",{className:"text-accent-primary text-sm font-medium group-hover:underline",children:"View Details →"})})]})})}},6116:(e,s,a)=>{Promise.resolve().then(a.bind(a,444))},6932:(e,s,a)=>{"use strict";a.d(s,{A:()=>r});let r=(0,a(9946).A)("funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]])},7580:(e,s,a)=>{"use strict";a.d(s,{A:()=>r});let r=(0,a(9946).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},9946:(e,s,a)=>{"use strict";a.d(s,{A:()=>m});var r=a(2115);let t=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),l=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,s,a)=>a?a.toUpperCase():s.toLowerCase()),n=e=>{let s=l(e);return s.charAt(0).toUpperCase()+s.slice(1)},i=function(){for(var e=arguments.length,s=Array(e),a=0;a<e;a++)s[a]=arguments[a];return s.filter((e,s,a)=>!!e&&""!==e.trim()&&a.indexOf(e)===s).join(" ").trim()},c=e=>{for(let s in e)if(s.startsWith("aria-")||"role"===s||"title"===s)return!0};var d={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let o=(0,r.forwardRef)((e,s)=>{let{color:a="currentColor",size:t=24,strokeWidth:l=2,absoluteStrokeWidth:n,className:o="",children:m,iconNode:h,...x}=e;return(0,r.createElement)("svg",{ref:s,...d,width:t,height:t,stroke:a,strokeWidth:n?24*Number(l)/Number(t):l,className:i("lucide",o),...!m&&!c(x)&&{"aria-hidden":"true"},...x},[...h.map(e=>{let[s,a]=e;return(0,r.createElement)(s,a)}),...Array.isArray(m)?m:[m]])}),m=(e,s)=>{let a=(0,r.forwardRef)((a,l)=>{let{className:c,...d}=a;return(0,r.createElement)(o,{ref:l,iconNode:s,className:i("lucide-".concat(t(n(e))),"lucide-".concat(e),c),...d})});return a.displayName=n(e),a}}},e=>{var s=s=>e(e.s=s);e.O(0,[244,168,441,684,358],()=>s(6116)),_N_E=e.O()}]);
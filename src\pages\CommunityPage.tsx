import React, { useState } from 'react';
import { MessageCircle, Users, TrendingUp, Clock, ThumbsUp, MessageSquare, Plus } from 'lucide-react';

interface ForumPost {
  id: number;
  title: string;
  content: string;
  author: string;
  category: string;
  createdAt: string;
  replies: number;
  likes: number;
  views: number;
  isHot: boolean;
}

const forumPosts: ForumPost[] = [
  {
    id: 1,
    title: 'How to negotiate salary in Pakistan job market?',
    content: 'I\'m planning to switch jobs and want to know effective strategies for salary negotiation. What has worked for you?',
    author: 'Anonymous_User_001',
    category: 'Career Advice',
    createdAt: '2024-01-15T10:30:00Z',
    replies: 23,
    likes: 45,
    views: 234,
    isHot: true
  },
  {
    id: 2,
    title: 'Red flags during interviews - Share your experiences',
    content: 'What are some warning signs you\'ve noticed during job interviews that indicated the company might not be a good fit?',
    author: 'Anonymous_User_002',
    category: 'Interview Tips',
    createdAt: '2024-01-14T15:45:00Z',
    replies: 18,
    likes: 32,
    views: 189,
    isHot: true
  },
  {
    id: 3,
    title: 'Unpaid overtime - Is this legal in Pakistan?',
    content: 'My company expects us to work 10-12 hours daily but only pays for 8 hours. What are the labor laws regarding overtime pay?',
    author: 'Anonymous_User_003',
    category: 'Labor Rights',
    createdAt: '2024-01-14T09:20:00Z',
    replies: 31,
    likes: 67,
    views: 342,
    isHot: true
  },
  {
    id: 4,
    title: 'Best companies for fresh graduates in Karachi',
    content: 'Looking for recommendations for entry-level positions in software development. Which companies provide good training and growth opportunities?',
    author: 'Anonymous_User_004',
    category: 'Job Search',
    createdAt: '2024-01-13T14:15:00Z',
    replies: 12,
    likes: 28,
    views: 156,
    isHot: false
  },
  {
    id: 5,
    title: 'Provident Fund not being paid - What to do?',
    content: 'My employer deducts PF from salary but hasn\'t been depositing it. How can I check and what legal actions can I take?',
    author: 'Anonymous_User_005',
    category: 'Labor Rights',
    createdAt: '2024-01-13T11:30:00Z',
    replies: 15,
    likes: 39,
    views: 198,
    isHot: false
  },
  {
    id: 6,
    title: 'Remote work policies in Pakistani companies',
    content: 'Which companies in Pakistan offer flexible remote work options? Share your experiences with WFH policies.',
    author: 'Anonymous_User_006',
    category: 'Work Culture',
    createdAt: '2024-01-12T16:45:00Z',
    replies: 8,
    likes: 22,
    views: 134,
    isHot: false
  }
];

const categories = ['All', 'Career Advice', 'Interview Tips', 'Labor Rights', 'Job Search', 'Work Culture', 'Salary Discussion'];

export const CommunityPage: React.FC = () => {
  const [selectedCategory, setSelectedCategory] = useState('All');
  const [sortBy, setSortBy] = useState('recent');

  const filteredPosts = forumPosts.filter(post => 
    selectedCategory === 'All' || post.category === selectedCategory
  );

  const sortedPosts = [...filteredPosts].sort((a, b) => {
    switch (sortBy) {
      case 'popular':
        return (b.likes + b.replies) - (a.likes + a.replies);
      case 'replies':
        return b.replies - a.replies;
      case 'recent':
      default:
        return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
    }
  });

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 1) return 'Just now';
    if (diffInHours < 24) return `${diffInHours}h ago`;
    return `${Math.floor(diffInHours / 24)}d ago`;
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center mb-8">
            <div className="flex items-center justify-center space-x-2 mb-4">
              <MessageCircle className="w-8 h-8 text-primary-600" />
              <h1 className="text-3xl font-bold text-gray-900">Community Forum</h1>
            </div>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Connect with fellow Pakistani employees, share experiences, and get advice on workplace issues. 
              All discussions are anonymous and moderated.
            </p>
          </div>

          {/* Community Stats */}
          <div className="grid grid-cols-3 gap-6 mb-8">
            <div className="text-center">
              <div className="text-2xl font-bold text-primary-600">1,234</div>
              <div className="text-sm text-gray-600">Active Members</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-secondary-600">456</div>
              <div className="text-sm text-gray-600">Discussions</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">2,890</div>
              <div className="text-sm text-gray-600">Helpful Replies</div>
            </div>
          </div>

          {/* Action Button */}
          <div className="text-center">
            <button className="bg-primary-600 hover:bg-primary-700 text-white px-6 py-3 rounded-lg font-medium transition-colors duration-200 flex items-center space-x-2 mx-auto">
              <Plus className="w-5 h-5" />
              <span>Start New Discussion</span>
            </button>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Sidebar */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 sticky top-8">
              <h3 className="font-semibold text-gray-900 mb-4">Categories</h3>
              <div className="space-y-2">
                {categories.map((category) => (
                  <button
                    key={category}
                    onClick={() => setSelectedCategory(category)}
                    className={`w-full text-left px-3 py-2 rounded-lg text-sm transition-colors duration-200 ${
                      selectedCategory === category
                        ? 'bg-primary-100 text-primary-700 font-medium'
                        : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                    }`}
                  >
                    {category}
                  </button>
                ))}
              </div>

              <div className="mt-8">
                <h4 className="font-medium text-gray-900 mb-3">Community Guidelines</h4>
                <ul className="text-xs text-gray-600 space-y-1">
                  <li>• Maintain anonymity</li>
                  <li>• Be respectful and helpful</li>
                  <li>• No personal attacks</li>
                  <li>• Share constructive advice</li>
                  <li>• Report inappropriate content</li>
                </ul>
              </div>
            </div>
          </div>

          {/* Main Content */}
          <div className="lg:col-span-3">
            {/* Sort Options */}
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-xl font-semibold text-gray-900">
                {selectedCategory === 'All' ? 'All Discussions' : selectedCategory}
              </h2>
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              >
                <option value="recent">Most Recent</option>
                <option value="popular">Most Popular</option>
                <option value="replies">Most Replies</option>
              </select>
            </div>

            {/* Forum Posts */}
            <div className="space-y-4">
              {sortedPosts.map((post) => (
                <div
                  key={post.id}
                  className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow duration-200 cursor-pointer"
                >
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex items-center space-x-2">
                      {post.isHot && (
                        <span className="bg-red-100 text-red-700 text-xs px-2 py-1 rounded-full font-medium">
                          🔥 Hot
                        </span>
                      )}
                      <span className="bg-gray-100 text-gray-700 text-xs px-2 py-1 rounded-full">
                        {post.category}
                      </span>
                    </div>
                    <div className="flex items-center text-xs text-gray-500">
                      <Clock className="w-3 h-3 mr-1" />
                      {formatDate(post.createdAt)}
                    </div>
                  </div>

                  <h3 className="text-lg font-semibold text-gray-900 mb-2 hover:text-primary-600 transition-colors">
                    {post.title}
                  </h3>
                  <p className="text-gray-600 text-sm mb-4 line-clamp-2">
                    {post.content}
                  </p>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-1 text-xs text-gray-500">
                      <Users className="w-3 h-3" />
                      <span>{post.author}</span>
                    </div>
                    
                    <div className="flex items-center space-x-4 text-xs text-gray-500">
                      <div className="flex items-center space-x-1">
                        <ThumbsUp className="w-3 h-3" />
                        <span>{post.likes}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <MessageSquare className="w-3 h-3" />
                        <span>{post.replies}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <TrendingUp className="w-3 h-3" />
                        <span>{post.views}</span>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Load More */}
            <div className="text-center mt-8">
              <button className="border border-gray-300 hover:bg-gray-50 text-gray-700 px-6 py-2 rounded-lg font-medium transition-colors duration-200">
                Load More Discussions
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
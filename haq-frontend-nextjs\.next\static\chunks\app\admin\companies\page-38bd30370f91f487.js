(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[722],{3786:(e,s,a)=>{"use strict";a.d(s,{A:()=>r});let r=(0,a(9946).A)("external-link",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]])},5152:(e,s,a)=>{Promise.resolve().then(a.bind(a,9169))},7924:(e,s,a)=>{"use strict";a.d(s,{A:()=>r});let r=(0,a(9946).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])},9169:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>x});var r=a(5155),t=a(2115),l=a(6647),n=a(9946);let o=(0,n.A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]);var d=a(7924),i=a(3227),c=a(3786);let u=(0,n.A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]]),m=(0,n.A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]]),x=()=>{let[e,s]=(0,t.useState)([]),[a,n]=(0,t.useState)(!0),[x,h]=(0,t.useState)(""),[p,g]=(0,t.useState)(""),[y,b]=(0,t.useState)(!1),[f,j]=(0,t.useState)({name:"",industry:"",hq_location:"",description:"",website_url:"",logo_url:"",employee_count_range:"",founded_year:""}),[v,N]=(0,t.useState)(!1),[w,k]=(0,t.useState)("");(0,t.useEffect)(()=>{_()},[]);let _=async()=>{try{n(!0);let e=await fetch("/api/admin/companies",{credentials:"include"});if(!e.ok)throw Error("Failed to fetch companies");let a=await e.json();a.success?s(a.data.companies):h(a.message||"Failed to load companies")}catch(e){h("Error loading companies"),console.error("Fetch companies error:",e)}finally{n(!1)}},C=async e=>{e.preventDefault(),N(!0),k("");try{let e={...f,founded_year:f.founded_year?parseInt(f.founded_year):void 0},s=await fetch("/api/admin/companies",{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify(e)}),a=await s.json();a.success?(j({name:"",industry:"",hq_location:"",description:"",website_url:"",logo_url:"",employee_count_range:"",founded_year:""}),b(!1),await _()):k(a.message||"Failed to create company")}catch(e){k("Error creating company"),console.error("Create company error:",e)}finally{N(!1)}},A=e.filter(e=>e.name.toLowerCase().includes(p.toLowerCase())||e.industry&&e.industry.toLowerCase().includes(p.toLowerCase()));return(0,r.jsxs)(l.A,{title:"Company Management",children:[(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Companies"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Manage company profiles and information"})]}),(0,r.jsxs)("button",{onClick:()=>b(!0),className:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",children:[(0,r.jsx)(o,{className:"h-4 w-4 mr-2"}),"Add Company"]})]}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,r.jsx)(d.A,{className:"h-5 w-5 text-gray-400"})}),(0,r.jsx)("input",{type:"text",placeholder:"Search companies...",value:p,onChange:e=>g(e.target.value),className:"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500"})]}),x&&(0,r.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-md p-4",children:(0,r.jsx)("p",{className:"text-red-800",children:x})}),a?(0,r.jsxs)("div",{className:"text-center py-12",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"}),(0,r.jsx)("p",{className:"mt-4 text-gray-600",children:"Loading companies..."})]}):(0,r.jsx)("div",{className:"bg-white shadow overflow-hidden sm:rounded-md",children:(0,r.jsx)("ul",{className:"divide-y divide-gray-200",children:0===A.length?(0,r.jsxs)("li",{className:"px-6 py-12 text-center",children:[(0,r.jsx)(i.A,{className:"mx-auto h-12 w-12 text-gray-400"}),(0,r.jsx)("h3",{className:"mt-2 text-sm font-medium text-gray-900",children:"No companies"}),(0,r.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:p?"No companies match your search.":"Get started by adding a new company."})]}):A.map(e=>(0,r.jsx)("li",{children:(0,r.jsxs)("div",{className:"px-6 py-4 flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"flex-shrink-0 h-12 w-12",children:e.logo_url?(0,r.jsx)("img",{className:"h-12 w-12 rounded-lg object-cover",src:e.logo_url,alt:e.name}):(0,r.jsx)("div",{className:"h-12 w-12 rounded-lg bg-gray-200 flex items-center justify-center",children:(0,r.jsx)(i.A,{className:"h-6 w-6 text-gray-400"})})}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:e.name}),e.is_verified&&(0,r.jsx)("span",{className:"ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800",children:"Verified"})]}),(0,r.jsxs)("div",{className:"text-sm text-gray-500",children:[e.industry&&(0,r.jsx)("span",{children:e.industry}),e.industry&&e.location&&(0,r.jsx)("span",{children:" • "}),e.location&&(0,r.jsx)("span",{children:e.location})]}),(0,r.jsxs)("div",{className:"text-sm text-gray-500",children:["HAQ Score: ",e.haq_score," • ",e.total_reviews," reviews"]})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[e.website_url&&(0,r.jsx)("a",{href:e.website_url,target:"_blank",rel:"noopener noreferrer",className:"text-gray-400 hover:text-gray-600",children:(0,r.jsx)(c.A,{className:"h-4 w-4"})}),(0,r.jsx)("button",{className:"text-gray-400 hover:text-gray-600",children:(0,r.jsx)(u,{className:"h-4 w-4"})}),(0,r.jsx)("button",{className:"text-gray-400 hover:text-red-600",children:(0,r.jsx)(m,{className:"h-4 w-4"})})]})]})},e.company_id))})})]}),y&&(0,r.jsx)("div",{className:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50",children:(0,r.jsx)("div",{className:"relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white",children:(0,r.jsxs)("div",{className:"mt-3",children:[(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Add New Company"}),w&&(0,r.jsx)("div",{className:"mb-4 bg-red-50 border border-red-200 rounded-md p-4",children:(0,r.jsx)("p",{className:"text-red-800",children:w})}),(0,r.jsxs)("form",{onSubmit:C,className:"space-y-4",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Company Name *"}),(0,r.jsx)("input",{type:"text",required:!0,value:f.name,onChange:e=>j({...f,name:e.target.value}),className:"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Industry"}),(0,r.jsx)("input",{type:"text",value:f.industry,onChange:e=>j({...f,industry:e.target.value}),className:"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Location"}),(0,r.jsx)("input",{type:"text",value:f.hq_location,onChange:e=>j({...f,hq_location:e.target.value}),className:"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Founded Year"}),(0,r.jsx)("input",{type:"number",min:"1800",max:new Date().getFullYear(),value:f.founded_year,onChange:e=>j({...f,founded_year:e.target.value}),className:"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Description"}),(0,r.jsx)("textarea",{rows:3,value:f.description,onChange:e=>j({...f,description:e.target.value}),className:"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Website URL"}),(0,r.jsx)("input",{type:"url",value:f.website_url,onChange:e=>j({...f,website_url:e.target.value}),className:"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Logo URL"}),(0,r.jsx)("input",{type:"url",value:f.logo_url,onChange:e=>j({...f,logo_url:e.target.value}),className:"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Employee Count Range"}),(0,r.jsxs)("select",{value:f.employee_count_range,onChange:e=>j({...f,employee_count_range:e.target.value}),className:"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500",children:[(0,r.jsx)("option",{value:"",children:"Select range"}),(0,r.jsx)("option",{value:"1-10",children:"1-10"}),(0,r.jsx)("option",{value:"11-50",children:"11-50"}),(0,r.jsx)("option",{value:"51-200",children:"51-200"}),(0,r.jsx)("option",{value:"201-500",children:"201-500"}),(0,r.jsx)("option",{value:"501-1000",children:"501-1000"}),(0,r.jsx)("option",{value:"1000+",children:"1000+"})]})]}),(0,r.jsxs)("div",{className:"flex justify-end space-x-3 pt-4",children:[(0,r.jsx)("button",{type:"button",onClick:()=>b(!1),className:"px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",children:"Cancel"}),(0,r.jsx)("button",{type:"submit",disabled:v,className:"px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50",children:v?"Creating...":"Create Company"})]})]})]})})})]})}}},e=>{var s=s=>e(e.s=s);e.O(0,[244,647,441,684,358],()=>s(5152)),_N_E=e.O()}]);
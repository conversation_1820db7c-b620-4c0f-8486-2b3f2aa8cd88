import React, { useState } from 'react';
import { Filter, MapPin, Star, Users } from 'lucide-react';
import { SearchBar } from '../components/common/SearchBar';
import { CompanyCard } from '../components/common/CompanyCard';

// Mock data
const companies = [
  {
    id: 1,
    name: 'TechFlow Solutions',
    logo: 'https://images.pexels.com/photos/3184398/pexels-photo-3184398.jpeg?auto=compress&cs=tinysrgb&w=100',
    haqScore: 4.2,
    totalReviews: 156,
    industry: 'Technology',
    location: 'Karachi',
    redFlags: ['Overtime Issues'],
    greenFlags: ['Good Benefits', 'Learning Opportunities']
  },
  {
    id: 2,
    name: 'Innovate Marketing',
    logo: 'https://images.pexels.com/photos/3184291/pexels-photo-3184291.jpeg?auto=compress&cs=tinysrgb&w=100',
    haqScore: 3.8,
    totalReviews: 89,
    industry: 'Marketing',
    location: 'Lahore',
    redFlags: ['Work-Life Balance'],
    greenFlags: ['Creative Environment', 'Fair Pay']
  },
  {
    id: 3,
    name: 'Prime Logistics',
    logo: 'https://images.pexels.com/photos/3184357/pexels-photo-3184357.jpeg?auto=compress&cs=tinysrgb&w=100',
    haqScore: 4.5,
    totalReviews: 234,
    industry: 'Logistics',
    location: 'Islamabad',
    redFlags: [],
    greenFlags: ['Excellent Management', 'Timely Payments', 'Growth Opportunities']
  },
  {
    id: 4,
    name: 'Creative Digital Agency',
    logo: 'https://images.pexels.com/photos/3183197/pexels-photo-3183197.jpeg?auto=compress&cs=tinysrgb&w=100',
    haqScore: 3.2,
    totalReviews: 67,
    industry: 'Digital Marketing',
    location: 'Karachi',
    redFlags: ['Unpaid Overtime', 'Poor Management'],
    greenFlags: ['Creative Freedom']
  },
  {
    id: 5,
    name: 'FinTech Solutions Ltd',
    logo: 'https://images.pexels.com/photos/3183150/pexels-photo-3183150.jpeg?auto=compress&cs=tinysrgb&w=100',
    haqScore: 4.1,
    totalReviews: 198,
    industry: 'Financial Technology',
    location: 'Lahore',
    redFlags: ['High Pressure'],
    greenFlags: ['Competitive Salary', 'Growth Opportunities', 'Modern Office']
  },
  {
    id: 6,
    name: 'Healthcare Plus',
    logo: 'https://images.pexels.com/photos/3182812/pexels-photo-3182812.jpeg?auto=compress&cs=tinysrgb&w=100',
    haqScore: 4.4,
    totalReviews: 312,
    industry: 'Healthcare',
    location: 'Islamabad',
    redFlags: [],
    greenFlags: ['Work-Life Balance', 'Good Benefits', 'Meaningful Work', 'Supportive Team']
  }
];

const industries = ['All Industries', 'Technology', 'Marketing', 'Logistics', 'Digital Marketing', 'Financial Technology', 'Healthcare'];
const locations = ['All Locations', 'Karachi', 'Lahore', 'Islamabad'];

export const CompanySearchPage: React.FC = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedIndustry, setSelectedIndustry] = useState('All Industries');
  const [selectedLocation, setSelectedLocation] = useState('All Locations');
  const [showFilters, setShowFilters] = useState(false);

  const filteredCompanies = companies.filter(company => {
    const matchesSearch = company.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         company.industry.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesIndustry = selectedIndustry === 'All Industries' || company.industry === selectedIndustry;
    const matchesLocation = selectedLocation === 'All Locations' || company.location === selectedLocation;
    
    return matchesSearch && matchesIndustry && matchesLocation;
  });

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-4">
              Browse Companies
            </h1>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Explore anonymous reviews and insights from Pakistani employees across various industries.
            </p>
          </div>

          {/* Search and Filters */}
          <div className="max-w-4xl mx-auto">
            <div className="mb-6">
              <SearchBar
                value={searchQuery}
                onChange={setSearchQuery}
                placeholder="Search companies by name or industry..."
              />
            </div>

            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
              <button
                onClick={() => setShowFilters(!showFilters)}
                className="flex items-center space-x-2 px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors duration-200"
              >
                <Filter className="w-4 h-4" />
                <span>Filters</span>
              </button>

              <div className="flex items-center space-x-4 text-sm text-gray-600">
                <span>{filteredCompanies.length} companies found</span>
              </div>
            </div>

            {/* Filter Panel */}
            {showFilters && (
              <div className="mt-6 p-6 bg-gray-50 rounded-lg border border-gray-200">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Industry
                    </label>
                    <select
                      value={selectedIndustry}
                      onChange={(e) => setSelectedIndustry(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                    >
                      {industries.map(industry => (
                        <option key={industry} value={industry}>
                          {industry}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Location
                    </label>
                    <select
                      value={selectedLocation}
                      onChange={(e) => setSelectedLocation(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                    >
                      {locations.map(location => (
                        <option key={location} value={location}>
                          {location}
                        </option>
                      ))}
                    </select>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Results */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {filteredCompanies.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredCompanies.map((company, index) => (
              <CompanyCard key={company.id} company={company} delay={index * 50} />
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <div className="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <Users className="w-12 h-12 text-gray-400" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">No companies found</h3>
            <p className="text-gray-600 mb-6">
              Try adjusting your search criteria or filters to find more companies.
            </p>
            <button
              onClick={() => {
                setSearchQuery('');
                setSelectedIndustry('All Industries');
                setSelectedLocation('All Locations');
              }}
              className="bg-primary-600 hover:bg-primary-700 text-white px-6 py-2 rounded-lg font-medium transition-colors duration-200"
            >
              Clear Filters
            </button>
          </div>
        )}
      </div>
    </div>
  );
};
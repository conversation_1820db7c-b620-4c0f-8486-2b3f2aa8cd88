{"kind": "FETCH", "data": {"headers": {"cache-control": "public, max-age=300, s-maxage=300", "connection": "keep-alive", "content-type": "application/json", "date": "Mon, 23 Jun 2025 20:39:01 GMT", "keep-alive": "timeout=5", "transfer-encoding": "chunked", "vary": "RSC, Next-Router-State-Tree, Next-Router-Prefetch, Next-Router-Segment-Prefetch, Accept-Encoding", "x-content-type-options": "nosniff", "x-frame-options": "DENY"}, "body": "eyJzdWNjZXNzIjp0cnVlLCJkYXRhIjp7ImNvbXBhbnkiOnsiY29tcGFueV9pZCI6ImQxZDY5ODRlLWM2OGUtNDY0OS1hYmE3LTM0MWJjZGMwZjhhYiIsIm5hbWUiOiJJbm5vdmF0ZSBNYXJrZXRpbmciLCJzbHVnIjoiaW5ub3ZhdGUtbWFya2V0aW5nIiwiaW5kdXN0cnkiOiJNYXJrZXRpbmciLCJsb2NhdGlvbiI6IkxhaG9yZSIsIndlYnNpdGVfdXJsIjoiaHR0cHM6Ly9pbm5vdmF0ZW1hcmtldGluZy5wayIsImxvZ29fdXJsIjoiaHR0cHM6Ly9pbWFnZXMucGV4ZWxzLmNvbS9waG90b3MvMzE4NDI5MS9wZXhlbHMtcGhvdG8tMzE4NDI5MS5qcGVnP2F1dG89Y29tcHJlc3MmY3M9dGlueXNyZ2Imdz0xMDAiLCJlbXBsb3llZV9jb3VudF9yYW5nZSI6IjIwLTUwIiwiZm91bmRlZF95ZWFyIjoyMDIwLCJkZXNjcmlwdGlvbiI6IkRpZ2l0YWwgbWFya2V0aW5nIGFnZW5jeSBoZWxwaW5nIGJyYW5kcyBncm93IHRoZWlyIG9ubGluZSBwcmVzZW5jZSIsImhhcV9zY29yZSI6My44LCJ0b3RhbF9yZXZpZXdzIjowLCJpc192ZXJpZmllZCI6dHJ1ZSwiY3JlYXRlZF9hdCI6IjIwMjUtMDYtMjJUMDI6Mzk6NTQuNzgyNTc4KzAwOjAwIiwidXBkYXRlZF9hdCI6IjIwMjUtMDYtMjJUMDI6Mzk6NTQuNzgyNTc4KzAwOjAwIiwiYXZlcmFnZV9yYXRpbmciOjAsInJhdGluZ19kaXN0cmlidXRpb24iOnsiMSI6MCwiMiI6MCwiMyI6MCwiNCI6MCwiNSI6MH19fX0=", "status": 200, "url": "http://localhost:3000/api/companies/d1d6984e-c68e-4649-aba7-341bcdc0f8ab"}, "revalidate": 300, "tags": []}
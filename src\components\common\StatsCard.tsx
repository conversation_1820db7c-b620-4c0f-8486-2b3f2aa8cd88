import React from 'react';
import { DivideIcon as LucideIcon } from 'lucide-react';

interface StatsCardProps {
  label: string;
  value: string;
  icon: LucideIcon;
  color: 'primary' | 'secondary' | 'warning' | 'danger';
  delay?: number;
}

export const StatsCard: React.FC<StatsCardProps> = ({ 
  label, 
  value, 
  icon: Icon, 
  color,
  delay = 0 
}) => {
  const colorClasses = {
    primary: 'from-primary-500 to-primary-600 text-primary-600',
    secondary: 'from-secondary-500 to-secondary-600 text-secondary-600',
    warning: 'from-warning-500 to-warning-600 text-warning-600',
    danger: 'from-danger-500 to-danger-600 text-danger-600'
  };

  return (
    <div
      className="bg-white rounded-lg p-6 shadow-sm border border-gray-200 hover:shadow-md transition-all duration-200 animate-slide-up"
      style={{ animationDelay: `${delay}ms` }}
    >
      <div className="flex items-center">
        <div className={`p-3 rounded-lg bg-gradient-to-br ${colorClasses[color].split(' ')[0]} ${colorClasses[color].split(' ')[1]}`}>
          <Icon className="w-6 h-6 text-white" />
        </div>
        <div className="ml-4">
          <p className="text-2xl font-bold text-gray-900">{value}</p>
          <p className={`text-sm font-medium ${colorClasses[color].split(' ')[2]}`}>{label}</p>
        </div>
      </div>
    </div>
  );
};
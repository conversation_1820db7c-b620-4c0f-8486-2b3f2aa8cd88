import React from 'react';
import { notFound } from 'next/navigation';
import CompanyDetails from '@/components/companies/CompanyDetails';
import CompanyReviews from '@/components/companies/CompanyReviews';

interface CompanyPageProps {
  params: {
    companyId: string;
  };
  searchParams: {
    page?: string;
    sort?: string;
  };
}

// Server-side data fetching for company details
async function getCompanyData(companyId: string) {
  try {
    const response = await fetch(`${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'}/api/companies/${companyId}`, {
      next: { revalidate: 300 }, // 5 minutes cache (CDN_SHORT policy)
      headers: {
        'Accept': 'application/json',
        'User-Agent': 'HAQ-NextJS-SSR'
      }
    });

    if (!response.ok) {
      if (response.status === 404) {
        return null; // Company not found
      }
      throw new Error('Failed to fetch company data');
    }

    const result = await response.json();
    return result.success ? result.data.company : null;
  } catch (error) {
    console.error('Error fetching company data:', error);
    return null;
  }
}

// Server-side data fetching for company reviews
async function getCompanyReviews(companyId: string, page: number = 1, sort: string = 'newest') {
  try {
    const params = new URLSearchParams({
      page: page.toString(),
      limit: '10',
      sort
    });

    const response = await fetch(`${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'}/api/companies/${companyId}/reviews?${params}`, {
      next: { revalidate: 300 }, // 5 minutes cache (CDN_SHORT policy)
      headers: {
        'Accept': 'application/json',
        'User-Agent': 'HAQ-NextJS-SSR'
      }
    });

    if (!response.ok) {
      throw new Error('Failed to fetch company reviews');
    }

    const result = await response.json();
    return result.success ? result.data : null;
  } catch (error) {
    console.error('Error fetching company reviews:', error);
    return null;
  }
}

export default async function CompanyPage({ params, searchParams }: CompanyPageProps) {
  const { companyId } = params;
  const page = parseInt(searchParams.page || '1');
  const sort = searchParams.sort || 'newest';

  // Validate UUID format
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  if (!uuidRegex.test(companyId)) {
    notFound();
  }

  // Fetch company data and reviews in parallel
  const [companyData, reviewsData] = await Promise.all([
    getCompanyData(companyId),
    getCompanyReviews(companyId, page, sort)
  ]);

  // If company not found, show 404
  if (!companyData) {
    notFound();
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Company Header */}
      <div className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <CompanyDetails company={companyData} />
        </div>
      </div>

      {/* Company Reviews Section */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <CompanyReviews 
          companyId={companyId}
          companyName={companyData.name}
          initialReviewsData={reviewsData}
          currentPage={page}
          currentSort={sort}
        />
      </div>
    </div>
  );
}

// Generate metadata for SEO
export async function generateMetadata({ params }: { params: { companyId: string } }) {
  const companyData = await getCompanyData(params.companyId);
  
  if (!companyData) {
    return {
      title: 'Company Not Found - HAQ',
      description: 'The requested company could not be found.'
    };
  }

  return {
    title: `${companyData.name} Reviews & Company Info - HAQ`,
    description: `Read employee reviews and company information for ${companyData.name}. ${companyData.total_reviews} reviews with an average rating of ${companyData.average_rating}/5.`,
    openGraph: {
      title: `${companyData.name} - Employee Reviews`,
      description: `${companyData.total_reviews} employee reviews for ${companyData.name}`,
      images: companyData.logo_url ? [companyData.logo_url] : [],
    },
  };
}

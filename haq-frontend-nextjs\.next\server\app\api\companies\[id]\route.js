(()=>{var e={};e.id=127,e.ids=[127],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},15510:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>f,routeModule:()=>g,serverHooks:()=>y,workAsyncStorage:()=>v,workUnitAsyncStorage:()=>_});var s={};r.r(s),r.d(s,{DELETE:()=>m,GET:()=>p,PATCH:()=>x,POST:()=>d,PUT:()=>l});var a=r(96559),o=r(48088),i=r(37719),n=r(32190),u=r(56621),c=r(77882);async function p(e,{params:t}){try{let e=t.id;if(!c.ne.isValidUUID(e))return n.NextResponse.json({success:!1,message:"Invalid company ID format",error:"Company ID must be a valid UUID"},{status:400});let{data:r,error:s}=await u.ND.from("companies").select(`
        company_id,
        name,
        slug,
        industry,
        location,
        website_url,
        logo_url,
        employee_count_range,
        founded_year,
        description,
        haq_score,
        total_reviews,
        is_verified,
        created_at,
        updated_at
      `).eq("company_id",e).single();if(s||!r)return n.NextResponse.json({success:!1,message:"Company not found",error:"The requested company does not exist"},{status:404});let{data:a,error:o}=await u.ND.from("reviews").select("overall_rating").eq("company_id",e).eq("status","approved"),i={total_reviews:0,average_rating:0,rating_distribution:{1:0,2:0,3:0,4:0,5:0}};if(a&&a.length>0){i.total_reviews=a.length;let e=a.reduce((e,t)=>e+t.overall_rating,0);i.average_rating=Math.round(e/a.length*10)/10,a.forEach(e=>{i.rating_distribution[e.overall_rating]++})}let p={company:{...r,total_reviews:i.total_reviews,average_rating:i.average_rating,rating_distribution:i.rating_distribution}};return n.NextResponse.json({success:!0,data:p},{headers:{"Cache-Control":"public, max-age=300, s-maxage=300",Vary:"Accept-Encoding","X-Content-Type-Options":"nosniff","X-Frame-Options":"DENY"}})}catch(e){return console.error("Company details API error:",e),n.NextResponse.json({success:!1,message:"Internal server error",error:"Unexpected error occurred"},{status:500})}}async function d(){return n.NextResponse.json({success:!1,message:"Method not allowed"},{status:405,headers:{Allow:"GET"}})}async function l(){return n.NextResponse.json({success:!1,message:"Method not allowed"},{status:405,headers:{Allow:"GET"}})}async function m(){return n.NextResponse.json({success:!1,message:"Method not allowed"},{status:405,headers:{Allow:"GET"}})}async function x(){return n.NextResponse.json({success:!1,message:"Method not allowed"},{status:405,headers:{Allow:"GET"}})}let g=new a.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/companies/[id]/route",pathname:"/api/companies/[id]",filename:"route",bundlePath:"app/api/companies/[id]/route"},resolvedPagePath:"D:\\Haq website v1\\haq-frontend-nextjs\\src\\app\\api\\companies\\[id]\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:v,workUnitAsyncStorage:_,serverHooks:y}=g;function f(){return(0,i.patchFetch)({workAsyncStorage:v,workUnitAsyncStorage:_})}},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32325:e=>{"use strict";e.exports=require("jsdom")},34631:e=>{"use strict";e.exports=require("tls")},39727:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},51906:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=51906,e.exports=t},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56621:(e,t,r)=>{"use strict";r.d(t,{ND:()=>o});var s=r(39398);r(98766);let a=null,o=a=(0,s.createClient)("https://wqbuilazpyxpwyuwuqpi.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6IndxYnVpbGF6cHl4cHd5dXd1cXBpIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTA1NTYyNDMsImV4cCI6MjA2NjEzMjI0M30.GeRI54Rskwdbfm9_lRxy1-7YQ8vA74JWblNF2GRpzrI")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},77882:(e,t,r)=>{"use strict";let s;r.d(t,{ne:()=>i});var a=r(39296);{let{JSDOM:e}=r(32325),t=new e("").window;s=(0,a.A)(t)}let o={BASIC_TEXT:{ALLOWED_TAGS:[],ALLOWED_ATTR:[],KEEP_CONTENT:!0,RETURN_DOM:!1}},i={isValidUUID:e=>/^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(e),isValidRating:e=>Number.isInteger(e)&&e>=1&&e<=5,isValidTextLength:(e,t=5e3)=>"string"==typeof e&&e.length<=t}},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,580,398,766,296],()=>r(15510));module.exports=s})();
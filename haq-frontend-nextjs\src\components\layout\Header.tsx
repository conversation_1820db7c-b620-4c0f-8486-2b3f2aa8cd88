'use client'

import React, { useState } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { Search, Menu, X, Shield, Users, DollarSign, MessageCircle, LogIn, LogOut, User, Settings } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';

export const Header: React.FC = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isUserMenuOpen, setIsUserMenuOpen] = useState(false);
  const pathname = usePathname();
  const { user, isAuthenticated, isLoading, logout } = useAuth();

  const navigation = [
    { name: 'Companies', href: '/companies', icon: Shield },
    { name: 'Salaries', href: '/salaries', icon: DollarSign },
    { name: 'Community', href: '/community', icon: MessageCircle },
  ];

  const handleLogout = async () => {
    try {
      await logout();
      setIsUserMenuOpen(false);
    } catch (error) {
      console.error('Logout failed:', error);
    }
  };

  return (
    <header className="bg-background-primary border-b border-border-primary sticky top-0 z-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <Link href="/" className="flex items-center space-x-2 group">
            <div className="w-8 h-8 bg-gradient-to-br from-accent-primary to-accent-secondary rounded-lg flex items-center justify-center group-hover:scale-105 transition-transform duration-200">
              <Shield className="w-5 h-5 text-text-on-accent" />
            </div>
            <div className="flex flex-col">
              <span className="text-xl font-bold text-accent-primary">Haq</span>
              <span className="text-xs text-text-secondary -mt-1">حق</span>
            </div>
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-8">
            {navigation.map((item) => {
              const Icon = item.icon;
              const isActive = pathname === item.href;
              return (
                <Link
                  key={item.name}
                  href={item.href}
                  className={`flex items-center space-x-1 px-3 py-2 rounded-lg text-navigation-link font-medium transition-all duration-200 uppercase tracking-wide ${
                    isActive
                      ? 'text-accent-primary bg-surface-secondary'
                      : 'text-text-secondary hover:text-accent-primary hover:bg-surface-primary'
                  }`}
                >
                  <Icon className="w-4 h-4" />
                  <span>{item.name}</span>
                </Link>
              );
            })}
          </nav>

          {/* Action Buttons */}
          <div className="hidden md:flex items-center space-x-4">
            {isAuthenticated && (
              <Link
                href="/review/submit"
                className="bg-accent-primary hover:bg-accent-secondary text-text-on-accent px-4 py-2 rounded-lg text-sm font-semibold transition-all duration-200 flex items-center space-x-2 hover:shadow-glow transform hover:-translate-y-0.5"
              >
                <Users className="w-4 h-4" />
                <span>Write Review</span>
              </Link>
            )}

            {/* Authentication Section */}
            {isLoading ? (
              <div className="w-8 h-8 animate-spin rounded-full border-2 border-accent-primary border-t-transparent"></div>
            ) : isAuthenticated && user ? (
              /* User Menu */
              <div className="relative">
                <button
                  onClick={() => setIsUserMenuOpen(!isUserMenuOpen)}
                  className="flex items-center space-x-2 px-3 py-2 rounded-lg text-text-secondary hover:text-accent-primary hover:bg-surface-primary transition-all duration-200"
                >
                  <div className="w-8 h-8 bg-gradient-to-br from-accent-primary to-accent-secondary rounded-full flex items-center justify-center">
                    <User className="w-4 h-4 text-text-on-accent" />
                  </div>
                  <span className="text-sm font-medium">{user.username}</span>
                  <svg className={`w-4 h-4 transition-transform duration-200 ${isUserMenuOpen ? 'rotate-180' : ''}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                  </svg>
                </button>

                {/* User Dropdown Menu */}
                {isUserMenuOpen && (
                  <div className="absolute right-0 mt-2 w-48 bg-surface-primary border border-border-primary rounded-lg shadow-lg py-1 z-50">
                    <div className="px-4 py-2 border-b border-border-primary">
                      <p className="text-sm font-medium text-text-primary">{user.username}</p>
                      <p className="text-xs text-text-secondary">{user.email}</p>
                      <span className={`inline-block px-2 py-1 text-xs rounded-full mt-1 ${
                        user.role === 'admin'
                          ? 'bg-accent-primary text-text-on-accent'
                          : 'bg-surface-secondary text-text-secondary'
                      }`}>
                        {user.role}
                      </span>
                    </div>
                    <Link
                      href="/profile"
                      onClick={() => setIsUserMenuOpen(false)}
                      className="flex items-center space-x-2 px-4 py-2 text-sm text-text-secondary hover:text-accent-primary hover:bg-surface-secondary transition-colors duration-200"
                    >
                      <Settings className="w-4 h-4" />
                      <span>Profile Settings</span>
                    </Link>
                    <button
                      onClick={handleLogout}
                      className="w-full flex items-center space-x-2 px-4 py-2 text-sm text-text-secondary hover:text-red-600 hover:bg-surface-secondary transition-colors duration-200"
                    >
                      <LogOut className="w-4 h-4" />
                      <span>Sign Out</span>
                    </button>
                  </div>
                )}
              </div>
            ) : (
              /* Login/Register Buttons */
              <div className="flex items-center space-x-3">
                <Link
                  href="/auth/login"
                  className="flex items-center space-x-1 px-4 py-2 text-sm font-medium text-text-secondary hover:text-accent-primary transition-colors duration-200"
                >
                  <LogIn className="w-4 h-4" />
                  <span>Sign In</span>
                </Link>
                <Link
                  href="/auth/signup"
                  className="bg-accent-primary hover:bg-accent-secondary text-text-on-accent px-4 py-2 rounded-lg text-sm font-semibold transition-all duration-200 flex items-center space-x-1 hover:shadow-glow transform hover:-translate-y-0.5"
                >
                  <User className="w-4 h-4" />
                  <span>Sign Up</span>
                </Link>
              </div>
            )}
          </div>

          {/* Mobile menu button */}
          <button
            onClick={() => setIsMenuOpen(!isMenuOpen)}
            className="md:hidden p-2 rounded-lg text-text-secondary hover:text-accent-primary hover:bg-surface-primary transition-colors duration-200"
          >
            {isMenuOpen ? <X className="w-6 h-6" /> : <Menu className="w-6 h-6" />}
          </button>
        </div>
      </div>

      {/* Mobile Navigation */}
      {isMenuOpen && (
        <div className="md:hidden bg-surface-primary border-t border-border-primary">
          <div className="px-4 py-3 space-y-3">
            {navigation.map((item) => {
              const Icon = item.icon;
              const isActive = pathname === item.href;
              return (
                <Link
                  key={item.name}
                  href={item.href}
                  onClick={() => setIsMenuOpen(false)}
                  className={`flex items-center space-x-3 px-3 py-2 rounded-lg text-sm font-medium transition-colors duration-200 ${
                    isActive
                      ? 'text-accent-primary bg-surface-secondary'
                      : 'text-text-secondary hover:text-accent-primary hover:bg-surface-secondary'
                  }`}
                >
                  <Icon className="w-5 h-5" />
                  <span>{item.name}</span>
                </Link>
              );
            })}

            {/* Mobile Authentication Section */}
            {isAuthenticated ? (
              <>
                <Link
                  href="/review/submit"
                  onClick={() => setIsMenuOpen(false)}
                  className="flex items-center space-x-3 bg-accent-primary hover:bg-accent-secondary text-text-on-accent px-3 py-2 rounded-lg text-sm font-medium transition-colors duration-200"
                >
                  <Users className="w-5 h-5" />
                  <span>Write Review</span>
                </Link>

                <div className="border-t border-border-primary pt-3 mt-3">
                  <div className="flex items-center space-x-3 px-3 py-2 mb-2">
                    <div className="w-8 h-8 bg-gradient-to-br from-accent-primary to-accent-secondary rounded-full flex items-center justify-center">
                      <User className="w-4 h-4 text-text-on-accent" />
                    </div>
                    <div>
                      <p className="text-sm font-medium text-text-primary">{user?.username}</p>
                      <p className="text-xs text-text-secondary">{user?.email}</p>
                    </div>
                  </div>

                  <Link
                    href="/profile"
                    onClick={() => setIsMenuOpen(false)}
                    className="flex items-center space-x-3 px-3 py-2 rounded-lg text-sm font-medium text-text-secondary hover:text-accent-primary hover:bg-surface-secondary transition-colors duration-200"
                  >
                    <Settings className="w-5 h-5" />
                    <span>Profile Settings</span>
                  </Link>

                  <button
                    onClick={() => {
                      handleLogout();
                      setIsMenuOpen(false);
                    }}
                    className="w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-sm font-medium text-text-secondary hover:text-red-600 hover:bg-surface-secondary transition-colors duration-200"
                  >
                    <LogOut className="w-5 h-5" />
                    <span>Sign Out</span>
                  </button>
                </div>
              </>
            ) : (
              <div className="border-t border-border-primary pt-3 mt-3 space-y-2">
                <Link
                  href="/auth/login"
                  onClick={() => setIsMenuOpen(false)}
                  className="flex items-center space-x-3 px-3 py-2 rounded-lg text-sm font-medium text-text-secondary hover:text-accent-primary hover:bg-surface-secondary transition-colors duration-200"
                >
                  <LogIn className="w-5 h-5" />
                  <span>Sign In</span>
                </Link>
                <Link
                  href="/auth/signup"
                  onClick={() => setIsMenuOpen(false)}
                  className="flex items-center space-x-3 bg-accent-primary hover:bg-accent-secondary text-text-on-accent px-3 py-2 rounded-lg text-sm font-medium transition-colors duration-200"
                >
                  <User className="w-5 h-5" />
                  <span>Sign Up</span>
                </Link>
              </div>
            )}
          </div>
        </div>
      )}
    </header>
  );
};

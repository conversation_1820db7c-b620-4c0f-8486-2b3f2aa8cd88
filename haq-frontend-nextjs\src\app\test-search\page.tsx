'use client'

import React, { useState } from 'react';
import { SearchBar } from '@/components/common/SearchBar';

export default function TestSearchPage() {
  const [searchQuery, setSearchQuery] = useState('');
  const [logs, setLogs] = useState<string[]>([]);

  // Override console.log to capture logs
  React.useEffect(() => {
    const originalLog = console.log;
    console.log = (...args) => {
      originalLog(...args);
      setLogs(prev => [...prev, args.join(' ')]);
    };

    return () => {
      console.log = originalLog;
    };
  }, []);

  const clearLogs = () => {
    setLogs([]);
  };

  return (
    <div className="min-h-screen bg-background-primary p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold text-text-primary mb-8">Search Functionality Test</h1>
        
        <div className="bg-surface-primary border border-border-primary rounded-lg p-6 mb-6">
          <h2 className="text-xl font-semibold text-text-primary mb-4">Test Search Bar</h2>
          
          <div className="mb-6">
            <label className="block text-sm font-medium text-text-primary mb-2">
              Search with Auto-Navigate and Suggestions:
            </label>
            <SearchBar
              value={searchQuery}
              onChange={setSearchQuery}
              placeholder="Type 'tech' to test search..."
              autoNavigate={true}
              showSuggestions={true}
            />
          </div>
          
          <div className="text-sm text-text-secondary space-y-2">
            <p><strong>Instructions:</strong></p>
            <ul className="list-disc list-inside space-y-1">
              <li>Type "tech" in the search bar</li>
              <li>You should see suggestions dropdown after 2+ characters</li>
              <li>Press Enter or click search button to navigate to search results</li>
              <li>Click on a suggestion to go directly to that company</li>
              <li>Check console logs below for debugging info</li>
            </ul>
          </div>
        </div>
        
        <div className="bg-surface-primary border border-border-primary rounded-lg p-6">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-xl font-semibold text-text-primary">Console Logs</h2>
            <button
              onClick={clearLogs}
              className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors duration-200"
            >
              Clear Logs
            </button>
          </div>
          
          <div className="bg-surface-secondary border border-border-primary rounded-lg p-4 max-h-96 overflow-y-auto">
            {logs.length === 0 ? (
              <p className="text-text-secondary">No logs yet. Try using the search bar above.</p>
            ) : (
              <div className="space-y-1">
                {logs.map((log, index) => (
                  <div key={index} className="text-sm text-text-primary font-mono">
                    {log}
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
        
        <div className="mt-6 bg-surface-primary border border-border-primary rounded-lg p-6">
          <h2 className="text-xl font-semibold text-text-primary mb-4">Quick Tests</h2>
          <div className="space-y-4">
            <div>
              <h3 className="font-medium text-text-primary mb-2">Test API Endpoints:</h3>
              <div className="space-x-4">
                <button
                  onClick={async () => {
                    try {
                      const response = await fetch('/api/companies?q=tech&limit=3');
                      const data = await response.json();
                      console.log('Companies API Response:', data);
                    } catch (error) {
                      console.error('Companies API Error:', error);
                    }
                  }}
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200"
                >
                  Test Companies API
                </button>
                
                <button
                  onClick={async () => {
                    try {
                      const response = await fetch('/api/search/companies?q=tech');
                      const data = await response.json();
                      console.log('Search API Response:', data);
                    } catch (error) {
                      console.error('Search API Error:', error);
                    }
                  }}
                  className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors duration-200"
                >
                  Test Search API
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

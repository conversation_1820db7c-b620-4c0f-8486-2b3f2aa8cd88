{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Haq%20website%20v1/haq-frontend-nextjs/src/components/common/SearchBar.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useState, useEffect, useRef } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { Search, X } from 'lucide-react';\nimport Link from 'next/link';\n\ninterface Company {\n  company_id: string;\n  name: string;\n  industry: string;\n  location: string;\n}\n\ninterface SearchBarProps {\n  value: string;\n  onChange: (value: string) => void;\n  placeholder?: string;\n  onSubmit?: () => void;\n  autoNavigate?: boolean;\n  showSuggestions?: boolean; // New prop to enable search suggestions\n}\n\nexport const SearchBar: React.FC<SearchBarProps> = ({\n  value,\n  onChange,\n  placeholder = \"Search companies...\",\n  onSubmit,\n  autoNavigate = false,\n  showSuggestions = false\n}) => {\n  const router = useRouter();\n  const [suggestions, setSuggestions] = useState<Company[]>([]);\n  const [showDropdown, setShowDropdown] = useState(false);\n  const [loading, setLoading] = useState(false);\n  const searchRef = useRef<HTMLDivElement>(null);\n\n  // Fetch suggestions when user types\n  useEffect(() => {\n    if (!showSuggestions || !value.trim() || value.length < 2) {\n      setSuggestions([]);\n      setShowDropdown(false);\n      return;\n    }\n\n    const fetchSuggestions = async () => {\n      setLoading(true);\n      try {\n        const response = await fetch(`/api/companies?q=${encodeURIComponent(value.trim())}&limit=5`);\n        const data = await response.json();\n\n        if (data.success && data.data?.companies) {\n          setSuggestions(data.data.companies);\n          setShowDropdown(true);\n        }\n      } catch (error) {\n        console.error('Error fetching suggestions:', error);\n        setSuggestions([]);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    const timeoutId = setTimeout(fetchSuggestions, 300); // Debounce\n    return () => clearTimeout(timeoutId);\n  }, [value, showSuggestions]);\n\n  // Close dropdown when clicking outside\n  useEffect(() => {\n    const handleClickOutside = (event: MouseEvent) => {\n      if (searchRef.current && !searchRef.current.contains(event.target as Node)) {\n        setShowDropdown(false);\n      }\n    };\n\n    document.addEventListener('mousedown', handleClickOutside);\n    return () => document.removeEventListener('mousedown', handleClickOutside);\n  }, []);\n\n  const handleSubmit = (e: React.FormEvent) => {\n    e.preventDefault();\n    console.log('SearchBar: Form submitted with value:', value);\n    console.log('SearchBar: autoNavigate:', autoNavigate);\n\n    setShowDropdown(false);\n\n    // If custom onSubmit is provided, use it\n    if (onSubmit) {\n      console.log('SearchBar: Using custom onSubmit');\n      onSubmit();\n      return;\n    }\n\n    // If autoNavigate is enabled and there's a search query, navigate to search page\n    if (autoNavigate && value.trim()) {\n      console.log('SearchBar: Navigating to search page...');\n      const searchParams = new URLSearchParams();\n      searchParams.set('q', value.trim());\n      const url = `/search?${searchParams.toString()}`;\n      console.log('SearchBar: Navigation URL:', url);\n      router.push(url);\n    } else {\n      console.log('SearchBar: No navigation - autoNavigate:', autoNavigate, 'value:', value);\n    }\n  };\n\n  const handleSuggestionClick = (company: Company) => {\n    setShowDropdown(false);\n    router.push(`/companies/${company.company_id}`);\n  };\n\n  const clearSearch = () => {\n    onChange('');\n    setShowDropdown(false);\n    setSuggestions([]);\n  };\n\n  return (\n    <div ref={searchRef} className=\"relative w-full\">\n      <form onSubmit={handleSubmit} className=\"relative\">\n        <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n          <Search className=\"h-5 w-5 text-text-secondary\" />\n        </div>\n        <input\n          type=\"text\"\n          value={value}\n          onChange={(e) => onChange(e.target.value)}\n          className=\"block w-full pl-10 pr-20 py-3 border border-border-primary rounded-medium bg-surface-primary placeholder-text-secondary focus:outline-none focus:ring-2 focus:ring-accent-primary focus:border-accent-primary text-text-primary transition-all duration-200\"\n          placeholder={placeholder}\n          autoComplete=\"off\"\n        />\n\n        {/* Clear button */}\n        {value && (\n          <button\n            type=\"button\"\n            onClick={clearSearch}\n            className=\"absolute inset-y-0 right-12 flex items-center text-text-secondary hover:text-text-primary transition-colors duration-200\"\n            aria-label=\"Clear search\"\n          >\n            <X className=\"h-4 w-4\" />\n          </button>\n        )}\n\n        {/* Search button */}\n        <button\n          type=\"submit\"\n          className=\"absolute inset-y-0 right-0 flex items-center justify-center w-12 bg-accent-primary hover:bg-accent-secondary text-text-on-accent rounded-r-medium transition-colors duration-200\"\n          aria-label=\"Search\"\n          onClick={(e) => {\n            console.log('SearchBar: Button clicked');\n            // Let the form handle the submission\n          }}\n        >\n          <Search className=\"h-5 w-5\" />\n        </button>\n      </form>\n\n      {/* Search suggestions dropdown */}\n      {showSuggestions && showDropdown && (\n        <div className=\"absolute top-full left-0 right-0 mt-1 bg-surface-primary border border-border-primary rounded-medium shadow-lg z-50 max-h-80 overflow-y-auto\">\n          {loading && (\n            <div className=\"p-4 text-center text-text-secondary\">\n              <div className=\"animate-spin rounded-full h-5 w-5 border-b-2 border-accent-primary mx-auto\"></div>\n              <span className=\"ml-2\">Searching...</span>\n            </div>\n          )}\n\n          {!loading && suggestions.length === 0 && value.trim().length >= 2 && (\n            <div className=\"p-4 text-center text-text-secondary\">\n              No companies found for \"{value}\"\n            </div>\n          )}\n\n          {!loading && suggestions.length > 0 && (\n            <>\n              {suggestions.map((company) => (\n                <button\n                  key={company.company_id}\n                  onClick={() => handleSuggestionClick(company)}\n                  className=\"w-full text-left p-3 hover:bg-surface-secondary transition-colors duration-200 border-b border-border-primary last:border-b-0\"\n                >\n                  <div className=\"font-medium text-text-primary\">{company.name}</div>\n                  <div className=\"text-sm text-text-secondary\">\n                    {company.industry} • {company.location}\n                  </div>\n                </button>\n              ))}\n\n              {/* View all results link */}\n              <Link\n                href={`/search?q=${encodeURIComponent(value.trim())}`}\n                className=\"block w-full text-center p-3 text-accent-primary hover:bg-surface-secondary transition-colors duration-200 font-medium\"\n                onClick={() => setShowDropdown(false)}\n              >\n                View all results for \"{value}\"\n              </Link>\n            </>\n          )}\n        </div>\n      )}\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AACA;;;AALA;;;;;AAuBO,MAAM,YAAsC,CAAC,EAClD,KAAK,EACL,QAAQ,EACR,cAAc,qBAAqB,EACnC,QAAQ,EACR,eAAe,KAAK,EACpB,kBAAkB,KAAK,EACxB;;IACC,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IAC5D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAEzC,oCAAoC;IACpC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,IAAI,CAAC,mBAAmB,CAAC,MAAM,IAAI,MAAM,MAAM,MAAM,GAAG,GAAG;gBACzD,eAAe,EAAE;gBACjB,gBAAgB;gBAChB;YACF;YAEA,MAAM;wDAAmB;oBACvB,WAAW;oBACX,IAAI;wBACF,MAAM,WAAW,MAAM,MAAM,CAAC,iBAAiB,EAAE,mBAAmB,MAAM,IAAI,IAAI,QAAQ,CAAC;wBAC3F,MAAM,OAAO,MAAM,SAAS,IAAI;wBAEhC,IAAI,KAAK,OAAO,IAAI,KAAK,IAAI,EAAE,WAAW;4BACxC,eAAe,KAAK,IAAI,CAAC,SAAS;4BAClC,gBAAgB;wBAClB;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,+BAA+B;wBAC7C,eAAe,EAAE;oBACnB,SAAU;wBACR,WAAW;oBACb;gBACF;;YAEA,MAAM,YAAY,WAAW,kBAAkB,MAAM,WAAW;YAChE;uCAAO,IAAM,aAAa;;QAC5B;8BAAG;QAAC;QAAO;KAAgB;IAE3B,uCAAuC;IACvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,MAAM;0DAAqB,CAAC;oBAC1B,IAAI,UAAU,OAAO,IAAI,CAAC,UAAU,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAAW;wBAC1E,gBAAgB;oBAClB;gBACF;;YAEA,SAAS,gBAAgB,CAAC,aAAa;YACvC;uCAAO,IAAM,SAAS,mBAAmB,CAAC,aAAa;;QACzD;8BAAG,EAAE;IAEL,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,QAAQ,GAAG,CAAC,yCAAyC;QACrD,QAAQ,GAAG,CAAC,4BAA4B;QAExC,gBAAgB;QAEhB,yCAAyC;QACzC,IAAI,UAAU;YACZ,QAAQ,GAAG,CAAC;YACZ;YACA;QACF;QAEA,iFAAiF;QACjF,IAAI,gBAAgB,MAAM,IAAI,IAAI;YAChC,QAAQ,GAAG,CAAC;YACZ,MAAM,eAAe,IAAI;YACzB,aAAa,GAAG,CAAC,KAAK,MAAM,IAAI;YAChC,MAAM,MAAM,CAAC,QAAQ,EAAE,aAAa,QAAQ,IAAI;YAChD,QAAQ,GAAG,CAAC,8BAA8B;YAC1C,OAAO,IAAI,CAAC;QACd,OAAO;YACL,QAAQ,GAAG,CAAC,4CAA4C,cAAc,UAAU;QAClF;IACF;IAEA,MAAM,wBAAwB,CAAC;QAC7B,gBAAgB;QAChB,OAAO,IAAI,CAAC,CAAC,WAAW,EAAE,QAAQ,UAAU,EAAE;IAChD;IAEA,MAAM,cAAc;QAClB,SAAS;QACT,gBAAgB;QAChB,eAAe,EAAE;IACnB;IAEA,qBACE,6LAAC;QAAI,KAAK;QAAW,WAAU;;0BAC7B,6LAAC;gBAAK,UAAU;gBAAc,WAAU;;kCACtC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,yMAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;;;;;;kCAEpB,6LAAC;wBACC,MAAK;wBACL,OAAO;wBACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;wBACxC,WAAU;wBACV,aAAa;wBACb,cAAa;;;;;;oBAId,uBACC,6LAAC;wBACC,MAAK;wBACL,SAAS;wBACT,WAAU;wBACV,cAAW;kCAEX,cAAA,6LAAC,+LAAA,CAAA,IAAC;4BAAC,WAAU;;;;;;;;;;;kCAKjB,6LAAC;wBACC,MAAK;wBACL,WAAU;wBACV,cAAW;wBACX,SAAS,CAAC;4BACR,QAAQ,GAAG,CAAC;wBACZ,qCAAqC;wBACvC;kCAEA,cAAA,6LAAC,yMAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;;;;;;;;;;;;YAKrB,mBAAmB,8BAClB,6LAAC;gBAAI,WAAU;;oBACZ,yBACC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAK,WAAU;0CAAO;;;;;;;;;;;;oBAI1B,CAAC,WAAW,YAAY,MAAM,KAAK,KAAK,MAAM,IAAI,GAAG,MAAM,IAAI,mBAC9D,6LAAC;wBAAI,WAAU;;4BAAsC;4BAC1B;4BAAM;;;;;;;oBAIlC,CAAC,WAAW,YAAY,MAAM,GAAG,mBAChC;;4BACG,YAAY,GAAG,CAAC,CAAC,wBAChB,6LAAC;oCAEC,SAAS,IAAM,sBAAsB;oCACrC,WAAU;;sDAEV,6LAAC;4CAAI,WAAU;sDAAiC,QAAQ,IAAI;;;;;;sDAC5D,6LAAC;4CAAI,WAAU;;gDACZ,QAAQ,QAAQ;gDAAC;gDAAI,QAAQ,QAAQ;;;;;;;;mCANnC,QAAQ,UAAU;;;;;0CAY3B,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAM,CAAC,UAAU,EAAE,mBAAmB,MAAM,IAAI,KAAK;gCACrD,WAAU;gCACV,SAAS,IAAM,gBAAgB;;oCAChC;oCACwB;oCAAM;;;;;;;;;;;;;;;;;;;;;AAQ7C;GApLa;;QAQI,qIAAA,CAAA,YAAS;;;KARb", "debugId": null}}, {"offset": {"line": 307, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Haq%20website%20v1/haq-frontend-nextjs/src/app/test-search/page.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useState } from 'react';\nimport { SearchBar } from '@/components/common/SearchBar';\n\nexport default function TestSearchPage() {\n  const [searchQuery, setSearchQuery] = useState('');\n  const [logs, setLogs] = useState<string[]>([]);\n\n  // Override console.log to capture logs\n  React.useEffect(() => {\n    const originalLog = console.log;\n    console.log = (...args) => {\n      originalLog(...args);\n      setLogs(prev => [...prev, args.join(' ')]);\n    };\n\n    return () => {\n      console.log = originalLog;\n    };\n  }, []);\n\n  const clearLogs = () => {\n    setLogs([]);\n  };\n\n  return (\n    <div className=\"min-h-screen bg-background-primary p-8\">\n      <div className=\"max-w-4xl mx-auto\">\n        <h1 className=\"text-3xl font-bold text-text-primary mb-8\">Search Functionality Test</h1>\n        \n        <div className=\"bg-surface-primary border border-border-primary rounded-lg p-6 mb-6\">\n          <h2 className=\"text-xl font-semibold text-text-primary mb-4\">Test Search Bar</h2>\n          \n          <div className=\"mb-6\">\n            <label className=\"block text-sm font-medium text-text-primary mb-2\">\n              Search with Auto-Navigate and Suggestions:\n            </label>\n            <SearchBar\n              value={searchQuery}\n              onChange={setSearchQuery}\n              placeholder=\"Type 'tech' to test search...\"\n              autoNavigate={true}\n              showSuggestions={true}\n            />\n          </div>\n          \n          <div className=\"text-sm text-text-secondary space-y-2\">\n            <p><strong>Instructions:</strong></p>\n            <ul className=\"list-disc list-inside space-y-1\">\n              <li>Type \"tech\" in the search bar</li>\n              <li>You should see suggestions dropdown after 2+ characters</li>\n              <li>Press Enter or click search button to navigate to search results</li>\n              <li>Click on a suggestion to go directly to that company</li>\n              <li>Check console logs below for debugging info</li>\n            </ul>\n          </div>\n        </div>\n        \n        <div className=\"bg-surface-primary border border-border-primary rounded-lg p-6\">\n          <div className=\"flex items-center justify-between mb-4\">\n            <h2 className=\"text-xl font-semibold text-text-primary\">Console Logs</h2>\n            <button\n              onClick={clearLogs}\n              className=\"px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors duration-200\"\n            >\n              Clear Logs\n            </button>\n          </div>\n          \n          <div className=\"bg-surface-secondary border border-border-primary rounded-lg p-4 max-h-96 overflow-y-auto\">\n            {logs.length === 0 ? (\n              <p className=\"text-text-secondary\">No logs yet. Try using the search bar above.</p>\n            ) : (\n              <div className=\"space-y-1\">\n                {logs.map((log, index) => (\n                  <div key={index} className=\"text-sm text-text-primary font-mono\">\n                    {log}\n                  </div>\n                ))}\n              </div>\n            )}\n          </div>\n        </div>\n        \n        <div className=\"mt-6 bg-surface-primary border border-border-primary rounded-lg p-6\">\n          <h2 className=\"text-xl font-semibold text-text-primary mb-4\">Quick Tests</h2>\n          <div className=\"space-y-4\">\n            <div>\n              <h3 className=\"font-medium text-text-primary mb-2\">Test API Endpoints:</h3>\n              <div className=\"space-x-4\">\n                <button\n                  onClick={async () => {\n                    try {\n                      const response = await fetch('/api/companies?q=tech&limit=3');\n                      const data = await response.json();\n                      console.log('Companies API Response:', data);\n                    } catch (error) {\n                      console.error('Companies API Error:', error);\n                    }\n                  }}\n                  className=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200\"\n                >\n                  Test Companies API\n                </button>\n                \n                <button\n                  onClick={async () => {\n                    try {\n                      const response = await fetch('/api/search/companies?q=tech');\n                      const data = await response.json();\n                      console.log('Search API Response:', data);\n                    } catch (error) {\n                      console.error('Search API Error:', error);\n                    }\n                  }}\n                  className=\"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors duration-200\"\n                >\n                  Test Search API\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAKe,SAAS;;IACtB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAE7C,uCAAuC;IACvC,6JAAA,CAAA,UAAK,CAAC,SAAS;oCAAC;YACd,MAAM,cAAc,QAAQ,GAAG;YAC/B,QAAQ,GAAG;4CAAG,CAAC,GAAG;oBAChB,eAAe;oBACf;oDAAQ,CAAA,OAAQ;mCAAI;gCAAM,KAAK,IAAI,CAAC;6BAAK;;gBAC3C;;YAEA;4CAAO;oBACL,QAAQ,GAAG,GAAG;gBAChB;;QACF;mCAAG,EAAE;IAEL,MAAM,YAAY;QAChB,QAAQ,EAAE;IACZ;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAG,WAAU;8BAA4C;;;;;;8BAE1D,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAA+C;;;;;;sCAE7D,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAM,WAAU;8CAAmD;;;;;;8CAGpE,6LAAC,4IAAA,CAAA,YAAS;oCACR,OAAO;oCACP,UAAU;oCACV,aAAY;oCACZ,cAAc;oCACd,iBAAiB;;;;;;;;;;;;sCAIrB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;8CAAE,cAAA,6LAAC;kDAAO;;;;;;;;;;;8CACX,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC;sDAAG;;;;;;sDACJ,6LAAC;sDAAG;;;;;;sDACJ,6LAAC;sDAAG;;;;;;sDACJ,6LAAC;sDAAG;;;;;;sDACJ,6LAAC;sDAAG;;;;;;;;;;;;;;;;;;;;;;;;8BAKV,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAA0C;;;;;;8CACxD,6LAAC;oCACC,SAAS;oCACT,WAAU;8CACX;;;;;;;;;;;;sCAKH,6LAAC;4BAAI,WAAU;sCACZ,KAAK,MAAM,KAAK,kBACf,6LAAC;gCAAE,WAAU;0CAAsB;;;;;qDAEnC,6LAAC;gCAAI,WAAU;0CACZ,KAAK,GAAG,CAAC,CAAC,KAAK,sBACd,6LAAC;wCAAgB,WAAU;kDACxB;uCADO;;;;;;;;;;;;;;;;;;;;;8BASpB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAA+C;;;;;;sCAC7D,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAqC;;;;;;kDACnD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,SAAS;oDACP,IAAI;wDACF,MAAM,WAAW,MAAM,MAAM;wDAC7B,MAAM,OAAO,MAAM,SAAS,IAAI;wDAChC,QAAQ,GAAG,CAAC,2BAA2B;oDACzC,EAAE,OAAO,OAAO;wDACd,QAAQ,KAAK,CAAC,wBAAwB;oDACxC;gDACF;gDACA,WAAU;0DACX;;;;;;0DAID,6LAAC;gDACC,SAAS;oDACP,IAAI;wDACF,MAAM,WAAW,MAAM,MAAM;wDAC7B,MAAM,OAAO,MAAM,SAAS,IAAI;wDAChC,QAAQ,GAAG,CAAC,wBAAwB;oDACtC,EAAE,OAAO,OAAO;wDACd,QAAQ,KAAK,CAAC,qBAAqB;oDACrC;gDACF;gDACA,WAAU;0DACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUjB;GA1HwB;KAAA", "debugId": null}}, {"offset": {"line": 643, "column": 0}, "map": {"version": 3, "file": "search.js", "sources": ["file:///D:/Haq%20website%20v1/haq-frontend-nextjs/node_modules/lucide-react/src/icons/search.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'm21 21-4.34-4.34', key: '14j7rj' }],\n  ['circle', { cx: '11', cy: '11', r: '8', key: '4ej97u' }],\n];\n\n/**\n * @component @name Search\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMjEgMjEtNC4zNC00LjM0IiAvPgogIDxjaXJjbGUgY3g9IjExIiBjeT0iMTEiIHI9IjgiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/search\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Search = createLucideIcon('search', __iconNode);\n\nexport default Search;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAoB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACjD;QAAC,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA;YAAE,CAAI,CAAA,CAAA,CAAA,IAAA,CAAM;YAAA,CAAA,CAAI,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA;YAAK,CAAK,CAAA,CAAA,CAAA,CAAA,QAAA;QAAU,CAAA;KAAA;CAC1D;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAS,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAU,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}]}
import { NextRequest, NextResponse } from 'next/server';
import { PasswordUtils, ValidationUtils, JWTUtils } from '@/lib/auth';
import { ServerCookieUtils } from '@/lib/auth-server';
import { supabase } from '@/lib/supabase';

interface RegisterRequest {
  username: string;
  email: string;
  password: string;
}

interface RegisterResponse {
  success: boolean;
  message: string;
  user?: {
    user_id: string;
    username: string;
    email: string;
    role: string;
  };
}

export async function POST(request: NextRequest): Promise<NextResponse<RegisterResponse>> {
  try {
    // Parse request body
    const body: RegisterRequest = await request.json();
    const { username, email, password } = body;

    // Input validation
    if (!username || !email || !password) {
      return NextResponse.json(
        { success: false, message: 'Username, email, and password are required' },
        { status: 400 }
      );
    }

    // Sanitize inputs
    const sanitizedUsername = ValidationUtils.sanitizeInput(username);
    const sanitizedEmail = ValidationUtils.sanitizeInput(email);

    // Validate email format
    if (!ValidationUtils.isValidEmail(sanitizedEmail)) {
      return NextResponse.json(
        { success: false, message: 'Invalid email format' },
        { status: 400 }
      );
    }

    // Validate username format
    if (!ValidationUtils.isValidUsername(sanitizedUsername)) {
      return NextResponse.json(
        { 
          success: false, 
          message: 'Username must be 3-50 characters long and contain only letters, numbers, and underscores' 
        },
        { status: 400 }
      );
    }

    // Validate password strength
    const passwordValidation = ValidationUtils.validatePassword(password);
    if (!passwordValidation.isValid) {
      return NextResponse.json(
        { 
          success: false, 
          message: `Password validation failed: ${passwordValidation.errors.join(', ')}` 
        },
        { status: 400 }
      );
    }

    // Check if user already exists
    const { data: existingUsers, error: checkError } = await supabase
      .from('haq_users_db.users')
      .select('user_id')
      .or(`email.eq.${sanitizedEmail},username.eq.${sanitizedUsername}`);

    if (checkError) {
      console.error('Database error checking existing user:', checkError);
      return NextResponse.json(
        { success: false, message: 'Internal server error' },
        { status: 500 }
      );
    }

    if (existingUsers && existingUsers.length > 0) {
      return NextResponse.json(
        { success: false, message: 'User with this email or username already exists' },
        { status: 409 }
      );
    }

    // Hash password
    const passwordHash = await PasswordUtils.hashPassword(password);

    // Create user in database
    const { data: newUser, error: insertError } = await supabase
      .from('haq_users_db.users')
      .insert({
        username: sanitizedUsername,
        email: sanitizedEmail,
        password_hash: passwordHash,
        role: 'user'
      })
      .select('user_id, username, email, role, created_at')
      .single();

    if (insertError) {
      console.error('Database error creating user:', insertError);
      
      // Handle unique constraint violations
      if (insertError.code === '23505') {
        return NextResponse.json(
          { success: false, message: 'User with this email or username already exists' },
          { status: 409 }
        );
      }
      
      return NextResponse.json(
        { success: false, message: 'Failed to create user account' },
        { status: 500 }
      );
    }

    if (!newUser) {
      return NextResponse.json(
        { success: false, message: 'Failed to create user account' },
        { status: 500 }
      );
    }

    // Generate JWT token
    const token = JWTUtils.generateToken({
      user_id: newUser.user_id,
      role: newUser.role as 'user' | 'admin'
    });

    // Set authentication cookie
    await ServerCookieUtils.setAuthCookie(token);

    // Return success response (exclude sensitive data)
    return NextResponse.json(
      {
        success: true,
        message: 'User account created successfully',
        user: {
          user_id: newUser.user_id,
          username: newUser.username,
          email: newUser.email,
          role: newUser.role
        }
      },
      { status: 201 }
    );

  } catch (error) {
    console.error('Registration error:', error);
    
    // Handle JSON parsing errors
    if (error instanceof SyntaxError) {
      return NextResponse.json(
        { success: false, message: 'Invalid JSON in request body' },
        { status: 400 }
      );
    }

    // Handle JWT generation errors
    if (error instanceof Error && error.message.includes('JWT')) {
      return NextResponse.json(
        { success: false, message: 'Authentication setup failed' },
        { status: 500 }
      );
    }

    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Handle unsupported methods
export async function GET() {
  return NextResponse.json(
    { success: false, message: 'Method not allowed' },
    { status: 405 }
  );
}

export async function PUT() {
  return NextResponse.json(
    { success: false, message: 'Method not allowed' },
    { status: 405 }
  );
}

export async function DELETE() {
  return NextResponse.json(
    { success: false, message: 'Method not allowed' },
    { status: 405 }
  );
}

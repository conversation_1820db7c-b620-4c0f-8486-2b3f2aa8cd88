import React from 'react';
import { Link } from 'react-router-dom';
import { Star, MapPin, Users, AlertTriangle, CheckCircle } from 'lucide-react';

interface Company {
  id: number;
  name: string;
  logo: string;
  haqScore: number;
  totalReviews: number;
  industry: string;
  location: string;
  redFlags: string[];
  greenFlags: string[];
}

interface CompanyCardProps {
  company: Company;
  delay?: number;
}

export const CompanyCard: React.FC<CompanyCardProps> = ({ company, delay = 0 }) => {
  const getScoreColor = (score: number) => {
    if (score >= 4.0) return 'text-green-400 bg-green-400/10';
    if (score >= 3.0) return 'text-accent-secondary bg-accent-secondary/10';
    return 'text-red-400 bg-red-400/10';
  };

  return (
    <div
      className="bg-surface-primary rounded-medium border border-border-primary hover:border-accent-primary transition-all duration-200 animate-slide-up group hover:shadow-lg hover:shadow-accent-primary/10"
      style={{ animationDelay: `${delay}ms` }}
    >
      <Link to={`/company/${company.id}`} className="block p-6">
        <div className="flex items-start space-x-4">
          <img
            src={company.logo}
            alt={`${company.name} logo`}
            className="w-12 h-12 rounded-lg object-cover border border-border-primary"
          />
          <div className="flex-1 min-w-0">
            <h3 className="text-lg font-semibold text-text-primary truncate group-hover:text-accent-primary transition-colors">
              {company.name}
            </h3>
            <div className="flex items-center space-x-2 mt-1">
              <span className="text-sm text-text-secondary">{company.industry}</span>
              <span className="text-text-secondary">•</span>
              <div className="flex items-center space-x-1 text-sm text-text-secondary">
                <MapPin className="w-3 h-3" />
                <span>{company.location}</span>
              </div>
            </div>
          </div>
          <div className="flex flex-col items-end">
            <div className={`flex items-center space-x-1 px-3 py-1 rounded-pill text-sm font-medium ${getScoreColor(company.haqScore)}`}>
              <Star className="w-4 h-4" />
              <span>{company.haqScore}</span>
            </div>
            <div className="flex items-center space-x-1 text-xs text-text-secondary mt-1">
              <Users className="w-3 h-3" />
              <span>{company.totalReviews} reviews</span>
            </div>
          </div>
        </div>

        {/* Flags */}
        <div className="mt-4 space-y-2">
          {company.redFlags.length > 0 && (
            <div className="flex items-start space-x-2">
              <AlertTriangle className="w-4 h-4 text-red-400 mt-0.5 flex-shrink-0" />
              <div className="flex flex-wrap gap-1">
                {company.redFlags.slice(0, 2).map((flag, index) => (
                  <span
                    key={index}
                    className="inline-block px-2 py-1 bg-red-400/10 text-red-400 text-xs rounded-pill border border-red-400/20"
                  >
                    {flag}
                  </span>
                ))}
                {company.redFlags.length > 2 && (
                  <span className="inline-block px-2 py-1 bg-red-400/10 text-red-400 text-xs rounded-pill border border-red-400/20">
                    +{company.redFlags.length - 2} more
                  </span>
                )}
              </div>
            </div>
          )}

          {company.greenFlags.length > 0 && (
            <div className="flex items-start space-x-2">
              <CheckCircle className="w-4 h-4 text-green-400 mt-0.5 flex-shrink-0" />
              <div className="flex flex-wrap gap-1">
                {company.greenFlags.slice(0, 2).map((flag, index) => (
                  <span
                    key={index}
                    className="inline-block px-2 py-1 bg-green-400/10 text-green-400 text-xs rounded-pill border border-green-400/20"
                  >
                    {flag}
                  </span>
                ))}
                {company.greenFlags.length > 2 && (
                  <span className="inline-block px-2 py-1 bg-green-400/10 text-green-400 text-xs rounded-pill border border-green-400/20">
                    +{company.greenFlags.length - 2} more
                  </span>
                )}
              </div>
            </div>
          )}
        </div>
      </Link>
    </div>
  );
};
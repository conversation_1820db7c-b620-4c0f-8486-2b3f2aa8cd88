import { NextRequest, NextResponse } from 'next/server';
import { withAdminAuth } from '@/lib/admin-middleware';
import { supabase } from '@/lib/supabase';
import { z } from 'zod';

// Validation schema for company creation
const createCompanySchema = z.object({
  name: z.string().min(1, 'Company name is required').max(255, 'Company name too long'),
  industry: z.string().optional(),
  hq_location: z.string().optional(),
  description: z.string().optional(),
  website_url: z.string().url('Invalid website URL').optional().or(z.literal('')),
  logo_url: z.string().url('Invalid logo URL').optional().or(z.literal('')),
  employee_count_range: z.string().optional(),
  founded_year: z.number().int().min(1800).max(new Date().getFullYear()).optional(),
});

/**
 * GET /api/admin/companies
 * Retrieve all companies for admin dashboard
 * Requires admin authentication
 */
async function getCompanies(request: NextRequest, context: any, user: any): Promise<NextResponse> {
  try {
    // Get query parameters for pagination and filtering
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const search = searchParams.get('search') || '';
    const industry = searchParams.get('industry') || '';

    // Calculate offset for pagination
    const offset = (page - 1) * limit;

    // Build query
    let query = supabase
      .from('companies')
      .select(`
        company_id,
        name,
        slug,
        industry,
        location,
        description,
        website_url,
        logo_url,
        employee_count_range,
        founded_year,
        haq_score,
        total_reviews,
        is_verified,
        created_at,
        updated_at
      `)
      .order('created_at', { ascending: false });

    // Apply search filter
    if (search) {
      query = query.ilike('name', `%${search}%`);
    }

    // Apply industry filter
    if (industry) {
      query = query.eq('industry', industry);
    }

    // Apply pagination
    query = query.range(offset, offset + limit - 1);

    const { data: companies, error, count } = await query;

    if (error) {
      console.error('Error fetching companies:', error);
      return NextResponse.json(
        { success: false, message: 'Failed to fetch companies' },
        { status: 500 }
      );
    }

    // Get total count for pagination
    const { count: totalCount } = await supabase
      .from('companies')
      .select('*', { count: 'exact', head: true });

    return NextResponse.json({
      success: true,
      data: {
        companies: companies || [],
        pagination: {
          page,
          limit,
          total: totalCount || 0,
          totalPages: Math.ceil((totalCount || 0) / limit)
        }
      }
    });

  } catch (error) {
    console.error('Get companies error:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/admin/companies
 * Create a new company profile
 * Requires admin authentication
 */
async function createCompany(request: NextRequest, context: any, user: any): Promise<NextResponse> {
  try {
    const body = await request.json();

    // Validate request body
    const validationResult = createCompanySchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        { 
          success: false, 
          message: 'Validation failed',
          errors: validationResult.error.errors
        },
        { status: 400 }
      );
    }

    const companyData = validationResult.data;

    // Generate slug from company name
    const slug = companyData.name
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/^-+|-+$/g, '');

    // Check if company name or slug already exists
    const { data: existingCompany } = await supabase
      .from('companies')
      .select('company_id, name, slug')
      .or(`name.eq.${companyData.name},slug.eq.${slug}`)
      .limit(1);

    if (existingCompany && existingCompany.length > 0) {
      return NextResponse.json(
        { 
          success: false, 
          message: 'Company with this name already exists' 
        },
        { status: 409 }
      );
    }

    // Insert new company
    const { data: newCompany, error } = await supabase
      .from('companies')
      .insert({
        name: companyData.name,
        slug,
        industry: companyData.industry || null,
        location: companyData.hq_location || null,
        description: companyData.description || null,
        website_url: companyData.website_url || null,
        logo_url: companyData.logo_url || null,
        employee_count_range: companyData.employee_count_range || null,
        founded_year: companyData.founded_year || null,
        haq_score: 0.00,
        total_reviews: 0,
        is_verified: false
      })
      .select()
      .single();

    if (error) {
      console.error('Error creating company:', error);
      return NextResponse.json(
        { success: false, message: 'Failed to create company' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Company created successfully',
      data: newCompany
    }, { status: 201 });

  } catch (error) {
    console.error('Create company error:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Export wrapped handlers with admin authentication
export const GET = withAdminAuth(getCompanies);
export const POST = withAdminAuth(createCompany);

// Handle unsupported methods
export async function PUT() {
  return NextResponse.json(
    { success: false, message: 'Method not allowed' },
    { status: 405 }
  );
}

export async function DELETE() {
  return NextResponse.json(
    { success: false, message: 'Method not allowed' },
    { status: 405 }
  );
}

export async function PATCH() {
  return NextResponse.json(
    { success: false, message: 'Method not allowed' },
    { status: 405 }
  );
}

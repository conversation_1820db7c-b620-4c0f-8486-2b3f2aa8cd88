import React from 'react';
import Link from 'next/link';
import { Shield, Mail, Phone, MapPin } from 'lucide-react';

export const Footer: React.FC = () => {
  return (
    <footer className="bg-background-secondary border-t border-border-primary">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Brand */}
          <div className="col-span-1 md:col-span-2">
            <div className="flex items-center space-x-2 mb-4">
              <div className="w-8 h-8 bg-gradient-to-br from-accent-primary to-accent-secondary rounded-lg flex items-center justify-center">
                <Shield className="w-5 h-5 text-text-on-accent" />
              </div>
              <div className="flex flex-col">
                <span className="text-xl font-bold text-accent-primary">Haq</span>
                <span className="text-xs text-text-secondary -mt-1">حق</span>
              </div>
            </div>
            <p className="text-text-secondary mb-4 max-w-md">
              Empowering Pakistani employees through transparency, anonymous reviews, and workplace accountability. 
              Your voice matters in building a fair workplace culture.
            </p>
            <div className="flex space-x-4">
              <a href="#" className="text-text-secondary hover:text-accent-primary transition-colors duration-200">
                <Mail className="w-5 h-5" />
              </a>
              <a href="#" className="text-text-secondary hover:text-accent-primary transition-colors duration-200">
                <Phone className="w-5 h-5" />
              </a>
              <a href="#" className="text-text-secondary hover:text-accent-primary transition-colors duration-200">
                <MapPin className="w-5 h-5" />
              </a>
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="text-text-primary font-semibold mb-4 uppercase tracking-wide">Quick Links</h3>
            <ul className="space-y-2">
              <li>
                <Link href="/companies" className="text-text-secondary hover:text-accent-primary transition-colors duration-200">
                  Browse Companies
                </Link>
              </li>
              <li>
                <Link href="/review/submit" className="text-text-secondary hover:text-accent-primary transition-colors duration-200">
                  Write Review
                </Link>
              </li>
              <li>
                <Link href="/salaries" className="text-text-secondary hover:text-accent-primary transition-colors duration-200">
                  Salary Insights
                </Link>
              </li>
              <li>
                <Link href="/community" className="text-text-secondary hover:text-accent-primary transition-colors duration-200">
                  Community
                </Link>
              </li>
            </ul>
          </div>

          {/* Legal */}
          <div>
            <h3 className="text-text-primary font-semibold mb-4 uppercase tracking-wide">Legal</h3>
            <ul className="space-y-2">
              <li>
                <Link href="/privacy" className="text-text-secondary hover:text-accent-primary transition-colors duration-200">
                  Privacy Policy
                </Link>
              </li>
              <li>
                <Link href="/terms" className="text-text-secondary hover:text-accent-primary transition-colors duration-200">
                  Terms of Service
                </Link>
              </li>
              <li>
                <Link href="/guidelines" className="text-text-secondary hover:text-accent-primary transition-colors duration-200">
                  Community Guidelines
                </Link>
              </li>
              <li>
                <Link href="/about" className="text-text-secondary hover:text-accent-primary transition-colors duration-200">
                  About Us
                </Link>
              </li>
            </ul>
          </div>
        </div>

        <div className="border-t border-border-primary mt-8 pt-8 flex flex-col md:flex-row justify-between items-center">
          <p className="text-text-secondary text-sm">
            © 2025 Haq Platform. All rights reserved. Built for Pakistani employees.
          </p>
          <p className="text-text-secondary text-sm mt-2 md:mt-0">
            Made with ❤️ for workplace transparency
          </p>
        </div>
      </div>
    </footer>
  );
};

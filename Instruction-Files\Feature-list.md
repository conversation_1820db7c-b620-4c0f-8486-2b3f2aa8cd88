Core Philosophy: Clarity, Trust, and Actionable Insight
Every feature is designed with these three principles in mind. The layout must be intuitive, the trust mechanisms must be transparent, and the information provided must help users make better career decisions.

User Roles Defined
Anonymous User (The Job Seeker): The default visitor. Their access is read-only. They are the primary consumer of information. They have no backend.
Registered Reviewer (The "Haq" Contributor): An anonymous user who has created an account. They can contribute, manage their contributions, and engage more deeply with the community. They have a personal, private dashboard.
Company Representative (The Employer): A verified individual officially representing a company. They have a professional backend dashboard to manage their company's presence.
Platform Administrator (The "Haq" Team): The internal team that manages the entire platform. They have a comprehensive super-admin backend.
Part 1: The Public-Facing Platform (For Anonymous Users & Job Seekers)
This is the core website that any visitor can access. The layout will be clean, mobile-first, and available in both English and Urdu.

1.1. Homepage
High-Level Goal: To immediately engage the user and guide them to relevant information.
Layout & Features:
Hero Section: A prominent search bar with the text "Search for a company..." and a tagline like "Your right to a transparent workplace." (آپ کا شفاف کام کی جگہ کا حق).
"How it Works" Section: A simple, 3-step graphic explaining: 1. Search for a Company. 2. Read Anonymous Reviews. 3. Share Your Own Experience Safely.
Trending Companies: A dynamic list of companies that have received the most reviews or views recently.
Top-Rated Workplaces: A curated list of companies with the highest "Haq Score."
"Red Flag" Ticker: A live, anonymized ticker showing recent issue tags being reported (e.g., "A 'Provident Fund Issue' was just reported for a company in Lahore"). This adds dynamism and urgency.
Call-to-Action (CTA) Banners: Visually distinct banners prompting users to "Leave an Anonymous Review."
Footer: Links to About Us, FAQ, Privacy Policy, Community Guidelines, and Contact Us.
1.2. Company Profile Page
High-Level Goal: To provide a comprehensive, 360-degree view of a company's work environment.
Layout & Features:
Header: Company Name, Logo, Industry, City/Headquarters, and a link to their official website. A prominent "Claim this Profile" button for un-claimed pages.
Main Summary Module (Top-Fold):
The "Haq Score": A large, color-coded score (e.g., out of 5.0).
Ratings at a Glance: Star ratings for sub-categories: Salary & Benefits, Work-Life Balance, Management, Culture, Career Growth.
Recommendation Meter: "% of employees would recommend this company to a friend."
Red Flags / Green Flags: A visually scannable summary using the tags from reviews (e.g., 🚩 Red Flags: Unpaid Salaries (12 reports), Toxic Culture (9 reports) | ✅ Green Flags: Good Work-Life Balance (15 reports), Paid Time Off (12 reports)).
Tabs Section:
Reviews (Default Tab):
Filter & Sort Controls: Sort by "Most Recent," "Highest/Lowest Rating," "Most Helpful." Filter by "Job Title Category," "Employment Status (Current/Former)," and importantly, by Verification Status ("All," "Verified Current," "Verified Past").
Review Card: Each review will be a self-contained card showing:
Overall Star Rating.
Generalized Job Title and Location.
Date of Review.
Verification Badge: A prominent Gold ("Verified Current Employee") or Silver ("Verified Past Employee") badge if applicable. Unverified reviews have no badge.
Pros, Cons, and Advice to Management sections.
"Was this review helpful?" (Yes/No) counter.
An option for users to "Flag" the review for moderation.
Company Response: If a company has responded, it will be shown in an indented, clearly marked "Official Company Response" block directly below the relevant review.
Salaries Tab:
Aggregated salary data for that company. Users can filter by job title and years of experience.
Displays average base salary, reported bonuses, and a checklist of benefits.
CTA to anonymously add their own salary data.
About Tab: Displays company-provided information (mission, vision, benefits) only if the profile is claimed.
1.3. Anonymous Review Submission Flow
High-Level Goal: To make contributing easy, secure, and to gather structured, high-quality data while rigorously protecting anonymity.
Layout & Features (Multi-step Form):
Step 1: Company Identification: Search and select the company.
Step 2: Your Role: Job Title (selection from a predefined list of categories), City, Employment Status (Current/Former), Employment duration.
Step 3: The Review:
Overall Rating (1-5 stars, mandatory).
Star ratings for sub-categories.
Pros & Cons text boxes (minimum character count to encourage detail).
Advice to Management text box.
Step 4: The Issues (Crucial for Data):
"Did you experience any of the following issues?" A checklist of predefined tags: "Unpaid/Delayed Salaries," "Wrongful Termination," "Provident Fund Not Provided," "Toxic Management," "Workplace Harassment," "Lack of Growth," etc. This is vital for the "Red Flags" feature.
Step 5: Salary & Benefits (Optional but encouraged): Anonymous form to add salary and check off benefits received.
Step 6: Anonymity Check & Submission:
AI PII Scrubber: The system automatically scans all text fields and highlights potential PII (names, specific projects) with a warning: "Please remove personal details to protect your identity."
Final Checklist: Before the final "Submit" button, a user must check boxes confirming:
"I have read and agree to the Community Guidelines."
"I confirm this review is truthful and based on my own experience."
"I understand my review will be posted anonymously."
Post-Submission: A "Thank You" page that encourages the user to create a free anonymous account to track their review's status.

Part 1.4: The "Haq" Community Forum
This feature will be a top-level section of the platform, accessible directly from the main navigation bar as "COMMUNITY".

High-Level Goal: To foster a safe, anonymous space for peer-to-peer support, discussion of workplace rights, career advice, and systemic issues within the Pakistani job market. It builds a defensive moat for the platform by creating a loyal, engaged user base.
1.4.1. Forum Homepage / All Discussions View
Layout & Features: As depicted in the image, this page will have a two-column layout.
Left Sidebar:
Categories: A filterable list to navigate discussions.
All (Default View)
Career Advice
Interview Tips
Labor Rights (Crucial for the Haq mission)
Legal Questions
Job Search Strategies
Work Culture
Salary Discussion
Mental Health at Work
Community Guidelines: A static, always-visible box outlining the core rules:
Maintain 100% anonymity. No personal details.
Be respectful and helpful.
Share constructive advice. No personal attacks.
Report inappropriate content.
Do not share company-specific secrets or PII.
Main Content Area:
Controls:
A prominent "Start New Discussion" button.
A "Sort By" dropdown menu with options: "Most Recent," "Trending" (high engagement in a recent period), "Most Popular" (all-time upvotes/comments), "Unanswered."
Discussion Post List: Each item in the list is a "Post Card" containing:
Engagement Tag: A "Hot" tag for trending posts.
Category Tag: A color-coded tag indicating the post's category (e.g., "Labor Rights").
Post Title: The main, clickable headline of the discussion.
Post Snippet: The first 1-2 lines of the post body to provide context.
Author & Stats (Anonymized):
Author: Anonymized username (e.g., Anonymous_User_001).
Upvotes: A simple count (e.g., ▲ 67).
Comments: A count of replies (e.g., 💬 31).
Views: A total view count (e.g., 👁️ 342).
Timestamp: Relative time (e.g., "523d ago," "3h ago").
1.4.2. Individual Discussion Thread Page
High-Level Goal: To allow focused discussion on a single topic.
Layout & Features:
Original Post (OP): The full content of the post is displayed clearly at the top.
Includes the Title, full Body, anonymous author, and engagement stats.
Action Buttons for OP: Upvote/Downvote, Share (copy a direct link), and Flag for Moderation.
Comment Section:
A text box for registered users to "Write a comment...".
Comments are displayed in a threaded format to allow for direct replies.
Each comment shows the anonymous username of the commenter, the text, and a timestamp.
Actions for each comment: Upvote/Downvote, Reply, and Flag for Moderation.
1.4.3. "Start New Discussion" Flow
High-Level Goal: A simple, secure form for users to create new discussion topics.
Layout & Features (Modal or separate page):
Title Field: A clear input for the discussion title (mandatory).
Body Field: A simple rich-text editor allowing basic formatting (bold, italics, bullet points, blockquotes).
Category Dropdown: User must select one category from the list (mandatory for organization).
Anonymity Check & Submission:
The same AI-powered PII scrubber from the review form will scan the Title and Body in real-time.
User must tick a box confirming they have read the Community Guidelines before the "Post" button is enabled.

Part 2: User-Specific Dashboards & Backends
2.1. Registered Reviewer ("Haq" Contributor) Dashboard
Access: Logged in via an anonymous username and password.
High-Level Goal: To empower contributors to manage their content and feel valued, without compromising their identity.
Layout & Features:
My Contributions: A list of all reviews and salary data they've submitted.
Status: Shows if a review is "Pending Approval," "Live," or "Rejected" (with a brief, generic reason from moderators).
Actions: Ability to permanently delete any of their own contributions at any time. No edit function to maintain integrity after posting.
Verification Center:
A clear explanation of the different verification tiers.
An interface to securely submit documents for "Verified Past Employee" status (with clear instructions on redaction and our data deletion policy).
An interface for the "Verified Current Employee" one-time email process.
Current verification status is clearly displayed.
Followed Companies: A feed showing new reviews for companies they choose to follow.
Account Settings: Change username, password, or permanently delete the entire account.
2.2. Company Representative (Employer) Dashboard
Access: A separate login portal (employers.haq.pk). Access is granted by Platform Admins after a verification process (e.g., using an official email and phone call).
High-Level Goal: To provide companies with tools for reputation management, constructive engagement, and analytics.
Layout & Features (Professional, data-centric dashboard):
Analytics Overview (Main Page):
Trend graph of their "Haq Score" over the last 3/6/12 months.
Key metrics: Profile views, number of reviews, overall ratings.
Sentiment Analysis: A simple breakdown of positive vs. negative keyword trends from their reviews.
Reviews Feed:
A real-time feed of new reviews about their company.
A text box and "Submit Response" button below each review. All responses are sent to moderation before going live to ensure they are not abusive.
Profile Management:
Ability to edit the "About" section of their public profile, update their logo, and list official benefits.
Premium Features (Monetization):
Enhanced Analytics: Deeper insights, competitor benchmarking (comparing their score to industry averages).
Job Postings: Ability to post jobs that appear on their profile and on a future "Jobs" section of the site, potentially highlighted as coming from a company that engages with feedback.
2.3. Platform Administrator (Admin) Backend
Access: Highly secure, internal-only login with two-factor authentication.
High-Level Goal: To give the Haq team complete oversight and the tools to maintain the platform's integrity.
Layout & Features (Complex, multi-functional interface):
Master Dashboard: At-a-glance view of site-wide activity: new users, pending reviews, flagged content, etc.
Review Moderation Queue: The most critical tool.
A feed of all submitted and flagged reviews.
Displays the review content and the reason it's in the queue (e.g., "New Submission," "User Flagged," "AI Flagged: Potential PII").
One-click actions: "Approve," "Reject," "Request Edit" (sends an automated, anonymous notification to the user).
User Management: Ability to view anonymous user profiles (without seeing PII), see their contribution history, and ban users who repeatedly violate guidelines.
Company Management:
Verification queue for new company representatives.
Ability to manage all company profiles.
Analytics Engine: Deep, site-wide analytics on user behavior, popular companies, and review trends.
CMS (Content Management System): An easy interface for the Haq team to edit static pages like the FAQ, About Us, and Community Guidelines without needing a developer.

2.1. Registered Reviewer ("Haq" Contributor) Dashboard
New Section: My Community Activity
My Discussions: A list of all discussion threads the user has started, with the ability to see stats and permanently delete their own threads.
My Comments: A list of all comments they have made across the forum, with the ability to permanently delete their own comments.
Notifications: A new notification bell icon in the dashboard header. It will alert users when:
Someone replies to their discussion thread.
Someone replies to their comment.
Their post receives a certain number of upvotes (e.g., 25).
2.3. Platform Administrator (Admin) Backend
New Section: Forum Moderation
Moderation Queue: A dashboard showing all user-flagged posts and comments.
Displays the flagged content, the reason for the flag, and who flagged it (anonymously).
Admin Actions:
Keep Content: Dismiss the flag.
Remove Content: Delete a post or comment that violates guidelines. A reason can be selected from a dropdown, which can optionally trigger an anonymous notification to the violator.
Lock Thread: Prevent new comments on a heated or resolved discussion.
Pin Thread: Pin an important announcement or thread to the top of its category.
User Management: Admins can view a user's forum activity to identify patterns of abuse and issue warnings or ban accounts from the community section.
Category Management: Admins can add, edit, or remove forum categories as the community evolves.
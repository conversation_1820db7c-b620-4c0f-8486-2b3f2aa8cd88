'use client'

import { SWRConfig } from 'swr'
import { createClientComponentClient } from '@/lib/supabase'

// Default fetcher function for SWR
const fetcher = async (url: string) => {
  const supabase = createClientComponentClient()
  
  // Handle different API endpoints
  if (url.startsWith('/api/companies')) {
    // For now, return mock data since we need to set up proper API routes
    // The direct Supabase schema queries don't work with PostgREST syntax
    return [
      {
        id: 1,
        company_id: 1,
        name: "TechCorp Solutions",
        slug: "techcorp-solutions",
        logo_url: "/placeholder-company.svg",
        description: "Leading technology solutions provider",
        location: "Karachi, Pakistan",
        industry: "Technology",
        employee_count: "500-1000",
        haq_score: 85,
        total_reviews: 24,
        redFlags: ["High turnover rate", "Unpaid overtime"],
        greenFlags: ["Good benefits", "Career growth opportunities"]
      },
      {
        id: 2,
        company_id: 2,
        name: "InnovatePak",
        slug: "innovatepak",
        logo_url: "/placeholder-company.svg",
        description: "Innovation-driven software company",
        location: "Lahore, Pakistan",
        industry: "Software",
        employee_count: "100-500",
        haq_score: 78,
        total_reviews: 18,
        redFlags: ["Limited remote work"],
        greenFlags: ["Modern office", "Learning opportunities", "Flexible hours"]
      },
      {
        id: 3,
        company_id: 3,
        name: "DataFlow Systems",
        slug: "dataflow-systems",
        logo_url: "/placeholder-company.svg",
        description: "Data analytics and business intelligence",
        location: "Islamabad, Pakistan",
        industry: "Analytics",
        employee_count: "50-100",
        haq_score: 72,
        total_reviews: 12,
        redFlags: ["Micromanagement"],
        greenFlags: ["Good work-life balance", "Competitive salary"]
      }
    ]
  }
  
  if (url.startsWith('/api/company/')) {
    const slug = url.split('/').pop()
    // Return mock data for individual company
    const companies = [
      {
        id: 1,
        company_id: 1,
        name: "TechCorp Solutions",
        slug: "techcorp-solutions",
        logo_url: "/placeholder-company.svg",
        description: "Leading technology solutions provider",
        location: "Karachi, Pakistan",
        industry: "Technology",
        employee_count: "500-1000",
        haq_score: 85,
        total_reviews: 24,
        redFlags: ["High turnover rate", "Unpaid overtime"],
        greenFlags: ["Good benefits", "Career growth opportunities"]
      }
    ]

    return companies.find(company => company.slug === slug) || companies[0]
  }
  
  // Default fetch for other endpoints
  const response = await fetch(url)
  if (!response.ok) {
    throw new Error('Failed to fetch')
  }
  return response.json()
}

// SWR configuration
const swrConfig = {
  fetcher,
  revalidateOnFocus: false,
  revalidateOnReconnect: true,
  refreshInterval: 0, // Disable automatic refresh
  errorRetryCount: 3,
  errorRetryInterval: 5000,
  onError: (error: Error) => {
    console.error('SWR Error:', error)
  },
  onSuccess: (data: any, key: string) => {
    // Optional: Log successful data fetches in development
    if (process.env.NODE_ENV === 'development') {
      console.log('SWR Success:', key, data)
    }
  }
}

interface SWRProviderProps {
  children: React.ReactNode
}

export function SWRProvider({ children }: SWRProviderProps) {
  return (
    <SWRConfig value={swrConfig}>
      {children}
    </SWRConfig>
  )
}

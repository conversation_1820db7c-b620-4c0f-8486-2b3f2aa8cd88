(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[120],{283:(e,s,t)=>{"use strict";t.d(s,{As:()=>o,AuthProvider:()=>c});var a=t(5155),r=t(2115);let l={user:null,isLoading:!0,isAuthenticated:!1,error:null};function n(e,s){switch(s.type){case"AUTH_START":return{...e,isLoading:!0,error:null};case"AUTH_SUCCESS":return{...e,user:s.payload,isAuthenticated:!0,isLoading:!1,error:null};case"AUTH_FAILURE":return{...e,user:null,isAuthenticated:!1,isLoading:!1,error:s.payload};case"AUTH_LOGOUT":return{...e,user:null,isAuthenticated:!1,isLoading:!1,error:null};case"CLEAR_ERROR":return{...e,error:null};case"SET_LOADING":return{...e,isLoading:s.payload};default:return e}}let i=(0,r.createContext)(null);function c(e){let{children:s}=e,[t,c]=(0,r.useReducer)(n,l);(0,r.useEffect)(()=>{o()},[]);let o=(0,r.useCallback)(async()=>{try{c({type:"SET_LOADING",payload:!0});let e=await fetch("/api/auth/me",{method:"GET",credentials:"include"});if(e.ok){let s=await e.json();s.success&&s.user?(console.log("✅ Auth check: User authenticated",s.user.username),c({type:"AUTH_SUCCESS",payload:s.user})):(console.log("❌ Auth check: No user found"),c({type:"AUTH_LOGOUT"}))}else console.log("❌ Auth check: Response not ok",e.status),c({type:"AUTH_LOGOUT"})}catch(e){console.error("Session check failed:",e),c({type:"AUTH_LOGOUT"})}finally{c({type:"SET_LOADING",payload:!1})}},[]),d=(0,r.useCallback)(async(e,s)=>{try{c({type:"AUTH_START"});let t=await fetch("/api/auth/login",{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({email:e,password:s})}),a=await t.json();if(a.success&&a.user)return c({type:"AUTH_SUCCESS",payload:a.user}),{success:!0,message:a.message};return c({type:"AUTH_FAILURE",payload:a.message}),{success:!1,message:a.message}}catch(s){let e="Network error. Please try again.";return c({type:"AUTH_FAILURE",payload:e}),{success:!1,message:e}}},[]),m=(0,r.useCallback)(async(e,s,t)=>{try{c({type:"AUTH_START"});let a=await fetch("/api/auth/register",{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({username:e,email:s,password:t})}),r=await a.json();if(r.success&&r.user)return c({type:"AUTH_SUCCESS",payload:r.user}),{success:!0,message:r.message};return c({type:"AUTH_FAILURE",payload:r.message}),{success:!1,message:r.message}}catch(s){let e="Network error. Please try again.";return c({type:"AUTH_FAILURE",payload:e}),{success:!1,message:e}}},[]),x=(0,r.useCallback)(async()=>{try{c({type:"SET_LOADING",payload:!0}),await fetch("/api/auth/logout",{method:"POST",credentials:"include"}),c({type:"AUTH_LOGOUT"})}catch(e){console.error("Logout error:",e),c({type:"AUTH_LOGOUT"})}finally{c({type:"SET_LOADING",payload:!1})}},[]),u=(0,r.useCallback)(()=>{c({type:"CLEAR_ERROR"})},[]),h=(0,r.useCallback)(async()=>{await o()},[o]),g=(0,r.useMemo)(()=>({...t,login:d,register:m,logout:x,clearError:u,refreshUser:h}),[t,d,m,x,u,h]);return(0,a.jsx)(i.Provider,{value:g,children:s})}function o(){let e=(0,r.useContext)(i);if(!e)throw Error("useAuth must be used within an AuthProvider");return e}},1876:(e,s,t)=>{"use strict";t.d(s,{default:()=>N});var a=t(5155),r=t(2115),l=t(2177),n=t(1153),i=t(8778),c=t(283),o=t(1497),d=t(3227),m=t(7924),x=t(8564),u=t(7550),h=t(2138),g=t(1243),p=t(333),y=t(5580),b=t(463),j=t(646);let f=n.z.object({company_id:n.z.string().uuid("Please select a company"),overall_rating:n.z.number().min(1,"Please provide a rating").max(5,"Rating must be between 1 and 5"),pros:n.z.string().optional(),cons:n.z.string().optional(),advice_management:n.z.string().optional()}),N=()=>{let{user:e,isAuthenticated:s,isLoading:t}=(0,c.As)(),[n,N]=(0,r.useState)(1),[v,w]=(0,r.useState)([]),[A,_]=(0,r.useState)(""),[S,T]=(0,r.useState)(!1),[C,k]=(0,r.useState)(null),[U,R]=(0,r.useState)(!1),[E,L]=(0,r.useState)(!1),[O,P]=(0,r.useState)(""),[H,I]=(0,r.useState)([]),{control:G,handleSubmit:z,watch:F,setValue:Y,formState:{errors:D,isValid:W},reset:B}=(0,l.mN)({resolver:(0,i.u)(f),mode:"onChange",defaultValues:{company_id:"",overall_rating:0,pros:"",cons:"",advice_management:""}}),q=F(),J=async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";T(!0);try{let s=new URLSearchParams;e.trim()&&s.append("q",e.trim()),s.append("limit","20");let t=await fetch("/api/companies?".concat(s)),a=await t.json();a.success?w(a.data.companies):(console.error("Failed to fetch companies:",a.message),w([]))}catch(e){console.error("Error fetching companies:",e),w([])}finally{T(!1)}};(0,r.useEffect)(()=>{J()},[]),(0,r.useEffect)(()=>{let e=setTimeout(()=>{J(A)},300);return()=>clearTimeout(e)},[A]);let M=e=>{let s=e.toLowerCase();return["manager","ceo","director","supervisor","boss","lead","john","jane","smith","johnson","williams","brown","jones","email","phone","address","linkedin","facebook","twitter","my name","i am","called me","told me personally"].filter(e=>s.includes(e))};(0,r.useEffect)(()=>{let e=[];if(q.pros){let s=M(q.pros);s.length>0&&e.push({field:"pros",keywords:s})}if(q.cons){let s=M(q.cons);s.length>0&&e.push({field:"cons",keywords:s})}if(q.advice_management){let s=M(q.advice_management);s.length>0&&e.push({field:"advice_management",keywords:s})}I(e)},[q.pros,q.cons,q.advice_management]);let V=e=>{k(e),Y("company_id",e.company_id),N(2)},Q=e=>{Y("overall_rating",e)},K=async e=>{if(!s)return void P("You must be logged in to submit a review");R(!0),P("");try{let s=await fetch("/api/reviews",{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify(e)}),t=await s.json();t.success?(L(!0),N(5),B(),k(null),I([])):P(t.message||"Failed to submit review")}catch(e){console.error("Review submission error:",e),P("An unexpected error occurred. Please try again.")}finally{R(!1)}},X=()=>{n<4&&N(n+1)},Z=()=>{n>1&&N(n-1)};return t?(0,a.jsx)("div",{className:"max-w-2xl mx-auto p-6 bg-white rounded-lg shadow-lg",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"}),(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:"Loading..."}),(0,a.jsx)("p",{className:"text-gray-600",children:"Checking your authentication status..."})]})}):s?(0,a.jsxs)("div",{className:"max-w-4xl mx-auto p-6",children:[(0,a.jsxs)("div",{className:"mb-8",children:[(0,a.jsx)("div",{className:"flex items-center justify-between",children:[1,2,3,4].map(e=>(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ".concat(e<=n?"bg-blue-600 text-white":"bg-gray-200 text-gray-600"),children:e}),e<4&&(0,a.jsx)("div",{className:"w-16 h-1 mx-2 ".concat(e<n?"bg-blue-600":"bg-gray-200")})]},e))}),(0,a.jsxs)("div",{className:"flex justify-between mt-2 text-sm text-gray-600",children:[(0,a.jsx)("span",{children:"Select Company"}),(0,a.jsx)("span",{children:"Rate Experience"}),(0,a.jsx)("span",{children:"Write Review"}),(0,a.jsx)("span",{children:"Submit"})]})]}),(0,a.jsxs)("form",{onSubmit:z(K),className:"space-y-6",children:[1===n&&(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-lg p-6",children:[(0,a.jsxs)("div",{className:"flex items-center mb-6",children:[(0,a.jsx)(d.A,{className:"h-6 w-6 text-blue-600 mr-2"}),(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:"Select Company"})]}),(0,a.jsx)("div",{className:"mb-4",children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(m.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400"}),(0,a.jsx)("input",{type:"text",placeholder:"Search for a company...",value:A,onChange:e=>_(e.target.value),className:"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]})}),S?(0,a.jsxs)("div",{className:"text-center py-8",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"}),(0,a.jsx)("p",{className:"mt-2 text-gray-600",children:"Loading companies..."})]}):(0,a.jsx)("div",{className:"space-y-2 max-h-96 overflow-y-auto",children:0===v.length?(0,a.jsx)("p",{className:"text-center text-gray-500 py-8",children:A?"No companies found matching your search.":"No companies available."}):v.map(e=>(0,a.jsx)("div",{onClick:()=>V(e),className:"p-4 border border-gray-200 rounded-lg hover:border-blue-500 hover:bg-blue-50 cursor-pointer transition-colors",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[e.logo_url?(0,a.jsx)("img",{src:e.logo_url,alt:e.name,className:"w-12 h-12 rounded-lg object-cover mr-4"}):(0,a.jsx)("div",{className:"w-12 h-12 rounded-lg bg-gray-200 flex items-center justify-center mr-4",children:(0,a.jsx)(d.A,{className:"h-6 w-6 text-gray-400"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900",children:e.name}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:e.industry&&e.location?"".concat(e.industry," • ").concat(e.location):e.industry||e.location||"Company"})]})]}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsxs)("div",{className:"text-sm font-medium text-gray-900",children:["HAQ Score: ",e.haq_score]}),(0,a.jsxs)("div",{className:"text-sm text-gray-600",children:[e.total_reviews," reviews"]})]})]})},e.company_id))}),D.company_id&&(0,a.jsx)("p",{className:"mt-2 text-sm text-red-600",children:D.company_id.message})]}),2===n&&C&&(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-lg p-6",children:[(0,a.jsxs)("div",{className:"flex items-center mb-6",children:[(0,a.jsx)(x.A,{className:"h-6 w-6 text-blue-600 mr-2"}),(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:"Rate Your Experience"})]}),(0,a.jsx)("div",{className:"mb-6 p-4 bg-gray-50 rounded-lg",children:(0,a.jsxs)("div",{className:"flex items-center",children:[C.logo_url?(0,a.jsx)("img",{src:C.logo_url,alt:C.name,className:"w-16 h-16 rounded-lg object-cover mr-4"}):(0,a.jsx)("div",{className:"w-16 h-16 rounded-lg bg-gray-200 flex items-center justify-center mr-4",children:(0,a.jsx)(d.A,{className:"h-8 w-8 text-gray-400"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-xl font-semibold text-gray-900",children:C.name}),(0,a.jsx)("p",{className:"text-gray-600",children:C.industry&&C.location?"".concat(C.industry," • ").concat(C.location):C.industry||C.location||"Company"})]})]})}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsxs)("p",{className:"text-lg text-gray-700 mb-6",children:["How would you rate your overall experience working at ",C.name,"?"]}),(0,a.jsx)(l.xI,{name:"overall_rating",control:G,render:e=>{let{field:s}=e;return(0,a.jsx)("div",{className:"flex justify-center space-x-2 mb-6",children:[1,2,3,4,5].map(e=>(0,a.jsx)("button",{type:"button",onClick:()=>{s.onChange(e),Q(e)},className:"p-2 rounded-lg transition-colors ".concat(s.value>=e?"text-yellow-500 hover:text-yellow-600":"text-gray-300 hover:text-yellow-400"),children:(0,a.jsx)(x.A,{className:"h-12 w-12 fill-current"})},e))})}}),q.overall_rating>0&&(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsxs)("p",{className:"text-lg font-medium text-gray-900",children:[1===q.overall_rating&&"Very Poor",2===q.overall_rating&&"Poor",3===q.overall_rating&&"Average",4===q.overall_rating&&"Good",5===q.overall_rating&&"Excellent"]}),(0,a.jsxs)("p",{className:"text-gray-600",children:[q.overall_rating," out of 5 stars"]})]})]}),D.overall_rating&&(0,a.jsx)("p",{className:"mt-4 text-sm text-red-600 text-center",children:D.overall_rating.message}),(0,a.jsxs)("div",{className:"flex justify-between mt-8",children:[(0,a.jsxs)("button",{type:"button",onClick:Z,className:"flex items-center px-4 py-2 text-gray-600 hover:text-gray-800",children:[(0,a.jsx)(u.A,{className:"h-4 w-4 mr-2"}),"Back"]}),(0,a.jsxs)("button",{type:"button",onClick:X,disabled:!(e=>{switch(e){case 2:return!!q.company_id;case 3:case 4:return!!q.company_id&&q.overall_rating>0;default:return!0}})(3),className:"flex items-center px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed",children:["Continue",(0,a.jsx)(h.A,{className:"h-4 w-4 ml-2"})]})]})]}),3===n&&C&&(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-lg p-6",children:[(0,a.jsxs)("div",{className:"flex items-center mb-6",children:[(0,a.jsx)(o.A,{className:"h-6 w-6 text-blue-600 mr-2"}),(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:"Write Your Review"})]}),(0,a.jsx)("div",{className:"mb-6 p-4 bg-gray-50 rounded-lg",children:(0,a.jsx)("div",{className:"flex items-center justify-between",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:C.name}),(0,a.jsxs)("div",{className:"ml-4 flex items-center",children:[[1,2,3,4,5].map(e=>(0,a.jsx)(x.A,{className:"h-5 w-5 ".concat(e<=q.overall_rating?"text-yellow-500 fill-current":"text-gray-300")},e)),(0,a.jsxs)("span",{className:"ml-2 text-gray-600",children:["(",q.overall_rating,"/5)"]})]})]})})}),H.length>0&&(0,a.jsx)("div",{className:"mb-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg",children:(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)(g.A,{className:"h-5 w-5 text-yellow-600 mt-0.5 mr-3 flex-shrink-0"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-yellow-800",children:"Potential Personal Information Detected"}),(0,a.jsx)("p",{className:"text-sm text-yellow-700 mt-1",children:"We detected words that might identify specific people or contain personal information. Please review your text to ensure anonymity."}),(0,a.jsx)("div",{className:"mt-2",children:H.map((e,s)=>(0,a.jsxs)("div",{className:"text-sm text-yellow-700",children:[(0,a.jsxs)("strong",{children:[e.field,":"]})," ",e.keywords.join(", ")]},s))})]})]})}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("label",{className:"flex items-center text-sm font-medium text-gray-700 mb-2",children:[(0,a.jsx)(p.A,{className:"h-4 w-4 text-green-600 mr-2"}),"What did you like about working here? (Optional)"]}),(0,a.jsx)(l.xI,{name:"pros",control:G,render:e=>{let{field:s}=e;return(0,a.jsx)("textarea",{...s,rows:4,placeholder:"Share the positive aspects of your experience...",className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"})}})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("label",{className:"flex items-center text-sm font-medium text-gray-700 mb-2",children:[(0,a.jsx)(y.A,{className:"h-4 w-4 text-red-600 mr-2"}),"What could be improved? (Optional)"]}),(0,a.jsx)(l.xI,{name:"cons",control:G,render:e=>{let{field:s}=e;return(0,a.jsx)("textarea",{...s,rows:4,placeholder:"Share areas where the company could improve...",className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"})}})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("label",{className:"flex items-center text-sm font-medium text-gray-700 mb-2",children:[(0,a.jsx)(b.A,{className:"h-4 w-4 text-yellow-600 mr-2"}),"Advice to Management (Optional)"]}),(0,a.jsx)(l.xI,{name:"advice_management",control:G,render:e=>{let{field:s}=e;return(0,a.jsx)("textarea",{...s,rows:4,placeholder:"What advice would you give to management?",className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"})}})]})]}),(0,a.jsxs)("div",{className:"flex justify-between mt-8",children:[(0,a.jsxs)("button",{type:"button",onClick:Z,className:"flex items-center px-4 py-2 text-gray-600 hover:text-gray-800",children:[(0,a.jsx)(u.A,{className:"h-4 w-4 mr-2"}),"Back"]}),(0,a.jsxs)("button",{type:"button",onClick:X,className:"flex items-center px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700",children:["Review & Submit",(0,a.jsx)(h.A,{className:"h-4 w-4 ml-2"})]})]})]}),4===n&&C&&(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-lg p-6",children:[(0,a.jsxs)("div",{className:"flex items-center mb-6",children:[(0,a.jsx)(j.A,{className:"h-6 w-6 text-blue-600 mr-2"}),(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:"Review & Submit"})]}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"p-4 bg-gray-50 rounded-lg",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:"Company & Rating"}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-gray-700",children:C.name}),(0,a.jsxs)("div",{className:"flex items-center",children:[[1,2,3,4,5].map(e=>(0,a.jsx)(x.A,{className:"h-4 w-4 ".concat(e<=q.overall_rating?"text-yellow-500 fill-current":"text-gray-300")},e)),(0,a.jsxs)("span",{className:"ml-2 text-gray-600",children:["(",q.overall_rating,"/5)"]})]})]})]}),q.pros&&(0,a.jsxs)("div",{className:"p-4 bg-green-50 rounded-lg",children:[(0,a.jsxs)("h4",{className:"flex items-center font-medium text-green-800 mb-2",children:[(0,a.jsx)(p.A,{className:"h-4 w-4 mr-2"}),"Pros"]}),(0,a.jsx)("p",{className:"text-green-700",children:q.pros})]}),q.cons&&(0,a.jsxs)("div",{className:"p-4 bg-red-50 rounded-lg",children:[(0,a.jsxs)("h4",{className:"flex items-center font-medium text-red-800 mb-2",children:[(0,a.jsx)(y.A,{className:"h-4 w-4 mr-2"}),"Cons"]}),(0,a.jsx)("p",{className:"text-red-700",children:q.cons})]}),q.advice_management&&(0,a.jsxs)("div",{className:"p-4 bg-yellow-50 rounded-lg",children:[(0,a.jsxs)("h4",{className:"flex items-center font-medium text-yellow-800 mb-2",children:[(0,a.jsx)(b.A,{className:"h-4 w-4 mr-2"}),"Advice to Management"]}),(0,a.jsx)("p",{className:"text-yellow-700",children:q.advice_management})]}),H.length>0&&(0,a.jsx)("div",{className:"p-4 bg-yellow-50 border border-yellow-200 rounded-lg",children:(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)(g.A,{className:"h-5 w-5 text-yellow-600 mt-0.5 mr-3 flex-shrink-0"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-yellow-800",children:"Privacy Notice"}),(0,a.jsx)("p",{className:"text-sm text-yellow-700 mt-1",children:"Your review contains words that might identify specific people. Please ensure your review maintains anonymity before submitting."})]})]})}),(0,a.jsx)("div",{className:"p-4 bg-blue-50 rounded-lg",children:(0,a.jsx)("p",{className:"text-sm text-blue-700",children:"By submitting this review, you confirm that it's based on your genuine experience and doesn't contain false information. Your review will be moderated before publication."})})]}),O&&(0,a.jsx)("div",{className:"mt-4 p-4 bg-red-50 border border-red-200 rounded-lg",children:(0,a.jsx)("p",{className:"text-red-700",children:O})}),(0,a.jsxs)("div",{className:"flex justify-between mt-8",children:[(0,a.jsxs)("button",{type:"button",onClick:Z,disabled:U,className:"flex items-center px-4 py-2 text-gray-600 hover:text-gray-800 disabled:opacity-50",children:[(0,a.jsx)(u.A,{className:"h-4 w-4 mr-2"}),"Back"]}),(0,a.jsx)("button",{type:"submit",disabled:U,className:"flex items-center px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed",children:U?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Submitting..."]}):(0,a.jsxs)(a.Fragment,{children:["Submit Review",(0,a.jsx)(j.A,{className:"h-4 w-4 ml-2"})]})})]})]}),5===n&&E&&(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-lg p-6 text-center",children:[(0,a.jsx)(j.A,{className:"mx-auto h-16 w-16 text-green-500 mb-4"}),(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:"Review Submitted Successfully!"}),(0,a.jsx)("p",{className:"text-gray-600 mb-6",children:"Thank you for sharing your experience. Your review is now pending moderation and will be published once approved."}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)("button",{type:"button",onClick:()=>{N(1),L(!1),k(null),B()},className:"w-full px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700",children:"Submit Another Review"}),(0,a.jsx)("a",{href:"/",className:"block w-full px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50",children:"Return to Home"})]})]})]})]}):(0,a.jsx)("div",{className:"max-w-2xl mx-auto p-6 bg-white rounded-lg shadow-lg",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(o.A,{className:"mx-auto h-12 w-12 text-gray-400 mb-4"}),(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:"Login Required"}),(0,a.jsx)("p",{className:"text-gray-600 mb-6",children:"You must be logged in to submit a company review."}),(0,a.jsx)("a",{href:"/auth/login",className:"inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700",children:"Login to Continue"})]})})}},9162:(e,s,t)=>{Promise.resolve().then(t.bind(t,1876))}},e=>{var s=s=>e(e.s=s);e.O(0,[558,870,441,684,358],()=>s(9162)),_N_E=e.O()}]);
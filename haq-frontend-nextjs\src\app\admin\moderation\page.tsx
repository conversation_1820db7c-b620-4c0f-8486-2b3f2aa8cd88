'use client'

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import AdminLayout from '@/components/admin/AdminLayout';
import {
  CheckCircle,
  XCircle,
  Clock,
  Building2,
  User,
  Calendar,
  Star,
  MessageSquare,
  AlertTriangle,
  RefreshCw
} from 'lucide-react';

// Types
interface Company {
  company_id: string;
  name: string;
  industry: string;
  location: string;
}

interface Review {
  review_id: string;
  company: Company;
  author_id: string;
  overall_rating: number;
  pros: string;
  cons: string;
  advice_management: string;
  status: string;
  created_at: string;
  updated_at: string;
}

interface ModerationResponse {
  success: boolean;
  data: {
    reviews: Review[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
      hasNext: boolean;
      hasPrev: boolean;
    };
    sort: string;
    filters: {
      status: string;
    };
  };
}

// Confirmation Dialog Component
interface ConfirmDialogProps {
  isOpen: boolean;
  title: string;
  content: string;
  confirmText: string;
  cancelText: string;
  onConfirm: () => void;
  onCancel: () => void;
  loading?: boolean;
  type?: 'approve' | 'reject';
}

const ConfirmDialog: React.FC<ConfirmDialogProps> = ({
  isOpen,
  title,
  content,
  confirmText,
  cancelText,
  onConfirm,
  onCancel,
  loading = false,
  type = 'approve'
}) => {
  if (!isOpen) return null;

  const confirmButtonClass = type === 'approve' 
    ? 'bg-green-600 hover:bg-green-700 text-white'
    : 'bg-red-600 hover:bg-red-700 text-white';

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-surface-primary border border-border-primary rounded-lg p-6 max-w-md w-full mx-4">
        <div className="flex items-center space-x-3 mb-4">
          {type === 'approve' ? (
            <CheckCircle className="w-6 h-6 text-green-600" />
          ) : (
            <XCircle className="w-6 h-6 text-red-600" />
          )}
          <h3 className="text-lg font-semibold text-text-primary">{title}</h3>
        </div>
        
        <p className="text-text-secondary mb-6">{content}</p>
        
        <div className="flex space-x-3 justify-end">
          <button
            onClick={onCancel}
            disabled={loading}
            className="px-4 py-2 border border-border-primary text-text-secondary hover:text-text-primary hover:border-border-secondary rounded-lg transition-colors duration-200 disabled:opacity-50"
          >
            {cancelText}
          </button>
          <button
            onClick={onConfirm}
            disabled={loading}
            className={`px-4 py-2 rounded-lg transition-colors duration-200 disabled:opacity-50 flex items-center space-x-2 ${confirmButtonClass}`}
          >
            {loading && <RefreshCw className="w-4 h-4 animate-spin" />}
            <span>{confirmText}</span>
          </button>
        </div>
      </div>
    </div>
  );
};

// Review Card Component
interface ReviewCardProps {
  review: Review;
  onApprove: (reviewId: string) => void;
  onReject: (reviewId: string) => void;
  loading: boolean;
}

const ReviewCard: React.FC<ReviewCardProps> = ({ review, onApprove, onReject, loading }) => {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`w-4 h-4 ${i < rating ? 'text-yellow-400 fill-current' : 'text-gray-300'}`}
      />
    ));
  };

  return (
    <div className="bg-surface-primary border border-border-primary rounded-lg p-6 hover:border-accent-primary transition-colors duration-200">
      {/* Header */}
      <div className="flex items-start justify-between mb-4">
        <div className="flex items-center space-x-3">
          <Building2 className="w-5 h-5 text-accent-primary" />
          <div>
            <h3 className="font-semibold text-text-primary">{review.company.name}</h3>
            <p className="text-sm text-text-secondary">
              {review.company.industry} • {review.company.location}
            </p>
          </div>
        </div>
        
        <div className="flex items-center space-x-2">
          <Clock className="w-4 h-4 text-text-secondary" />
          <span className="text-sm text-text-secondary">
            {formatDate(review.created_at)}
          </span>
        </div>
      </div>

      {/* Rating */}
      <div className="flex items-center space-x-2 mb-4">
        <span className="text-sm font-medium text-text-primary">Overall Rating:</span>
        <div className="flex items-center space-x-1">
          {renderStars(review.overall_rating)}
          <span className="text-sm text-text-secondary ml-1">
            ({review.overall_rating}/5)
          </span>
        </div>
      </div>

      {/* Review Content */}
      <div className="space-y-4 mb-6">
        {review.pros && (
          <div>
            <h4 className="font-medium text-text-primary mb-2 flex items-center space-x-2">
              <CheckCircle className="w-4 h-4 text-green-600" />
              <span>Pros</span>
            </h4>
            <p className="text-text-secondary text-sm bg-surface-secondary p-3 rounded-lg">
              {review.pros}
            </p>
          </div>
        )}

        {review.cons && (
          <div>
            <h4 className="font-medium text-text-primary mb-2 flex items-center space-x-2">
              <XCircle className="w-4 h-4 text-red-600" />
              <span>Cons</span>
            </h4>
            <p className="text-text-secondary text-sm bg-surface-secondary p-3 rounded-lg">
              {review.cons}
            </p>
          </div>
        )}

        {review.advice_management && (
          <div>
            <h4 className="font-medium text-text-primary mb-2 flex items-center space-x-2">
              <MessageSquare className="w-4 h-4 text-blue-600" />
              <span>Advice to Management</span>
            </h4>
            <p className="text-text-secondary text-sm bg-surface-secondary p-3 rounded-lg">
              {review.advice_management}
            </p>
          </div>
        )}
      </div>

      {/* Author Info (Admin Only) */}
      <div className="flex items-center space-x-2 mb-4 p-3 bg-surface-secondary rounded-lg">
        <User className="w-4 h-4 text-text-secondary" />
        <span className="text-sm text-text-secondary">
          Author ID: <code className="bg-background-secondary px-1 rounded text-xs">{review.author_id}</code>
        </span>
      </div>

      {/* Action Buttons */}
      <div className="flex space-x-3">
        <button
          onClick={() => onApprove(review.review_id)}
          disabled={loading}
          className="flex-1 bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-colors duration-200 disabled:opacity-50 flex items-center justify-center space-x-2"
        >
          <CheckCircle className="w-4 h-4" />
          <span>Approve</span>
        </button>
        
        <button
          onClick={() => onReject(review.review_id)}
          disabled={loading}
          className="flex-1 bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg transition-colors duration-200 disabled:opacity-50 flex items-center justify-center space-x-2"
        >
          <XCircle className="w-4 h-4" />
          <span>Reject</span>
        </button>
      </div>
    </div>
  );
};

// Main Component
export default function AdminModerationPage() {
  const router = useRouter();
  const [reviews, setReviews] = useState<Review[]>([]);
  const [loading, setLoading] = useState(true);
  const [actionLoading, setActionLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0,
    totalPages: 0,
    hasNext: false,
    hasPrev: false
  });
  
  // Confirmation dialog state
  const [confirmDialog, setConfirmDialog] = useState<{
    isOpen: boolean;
    type: 'approve' | 'reject';
    reviewId: string;
    companyName: string;
  }>({
    isOpen: false,
    type: 'approve',
    reviewId: '',
    companyName: ''
  });

  // Fetch pending reviews
  const fetchPendingReviews = async (page = 1) => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await fetch(`/api/admin/reviews/pending?page=${page}&limit=10&sort=newest`);
      const data: ModerationResponse = await response.json();
      
      if (!response.ok) {
        throw new Error(data.success ? 'Unknown error' : 'Failed to fetch pending reviews');
      }
      
      if (data.success) {
        setReviews(data.data.reviews);
        setPagination(data.data.pagination);
      } else {
        throw new Error('Failed to fetch pending reviews');
      }
    } catch (error) {
      console.error('Error fetching pending reviews:', error);
      setError(error instanceof Error ? error.message : 'Failed to fetch pending reviews');
    } finally {
      setLoading(false);
    }
  };

  // Handle review approval/rejection
  const handleReviewAction = async (reviewId: string, status: 'approved' | 'rejected') => {
    try {
      setActionLoading(true);
      
      const response = await fetch(`/api/admin/reviews/${reviewId}/status`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status }),
      });
      
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.message || `Failed to ${status} review`);
      }
      
      if (data.success) {
        // Remove the review from the list
        setReviews(prev => prev.filter(review => review.review_id !== reviewId));
        
        // Show success message (you could add a toast notification here)
        console.log(`Review ${status} successfully`);
        
        // Refresh the list if it becomes empty
        if (reviews.length === 1) {
          fetchPendingReviews(pagination.page);
        }
      } else {
        throw new Error(data.message || `Failed to ${status} review`);
      }
    } catch (error) {
      console.error(`Error ${status}ing review:`, error);
      setError(error instanceof Error ? error.message : `Failed to ${status} review`);
    } finally {
      setActionLoading(false);
      setConfirmDialog({ isOpen: false, type: 'approve', reviewId: '', companyName: '' });
    }
  };

  // Handle approve button click
  const handleApprove = (reviewId: string) => {
    const review = reviews.find(r => r.review_id === reviewId);
    if (review) {
      setConfirmDialog({
        isOpen: true,
        type: 'approve',
        reviewId,
        companyName: review.company.name
      });
    }
  };

  // Handle reject button click
  const handleReject = (reviewId: string) => {
    const review = reviews.find(r => r.review_id === reviewId);
    if (review) {
      setConfirmDialog({
        isOpen: true,
        type: 'reject',
        reviewId,
        companyName: review.company.name
      });
    }
  };

  // Handle confirmation
  const handleConfirm = () => {
    const status = confirmDialog.type === 'approve' ? 'approved' : 'rejected';
    handleReviewAction(confirmDialog.reviewId, status);
  };

  // Handle page change
  const handlePageChange = (newPage: number) => {
    fetchPendingReviews(newPage);
  };

  // Initial load
  useEffect(() => {
    fetchPendingReviews();
  }, []);

  return (
    <AdminLayout title="Review Moderation Queue">
      <div className="space-y-6">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-text-primary">Review Moderation Queue</h1>
              <p className="text-text-secondary mt-2">
                Approve or reject pending reviews to make them public
              </p>
            </div>
            
            <button
              onClick={() => fetchPendingReviews(pagination.page)}
              disabled={loading}
              className="bg-accent-primary hover:bg-accent-secondary text-text-on-accent px-4 py-2 rounded-lg transition-colors duration-200 disabled:opacity-50 flex items-center space-x-2"
            >
              <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
              <span>Refresh</span>
            </button>
          </div>
          
          {/* Stats */}
          <div className="mt-6 grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="bg-surface-primary border border-border-primary rounded-lg p-4">
              <div className="flex items-center space-x-3">
                <Clock className="w-5 h-5 text-yellow-600" />
                <div>
                  <p className="text-sm text-text-secondary">Pending Reviews</p>
                  <p className="text-xl font-semibold text-text-primary">{pagination.total}</p>
                </div>
              </div>
            </div>
            
            <div className="bg-surface-primary border border-border-primary rounded-lg p-4">
              <div className="flex items-center space-x-3">
                <CheckCircle className="w-5 h-5 text-green-600" />
                <div>
                  <p className="text-sm text-text-secondary">Current Page</p>
                  <p className="text-xl font-semibold text-text-primary">
                    {pagination.page} of {pagination.totalPages}
                  </p>
                </div>
              </div>
            </div>
            
            <div className="bg-surface-primary border border-border-primary rounded-lg p-4">
              <div className="flex items-center space-x-3">
                <MessageSquare className="w-5 h-5 text-blue-600" />
                <div>
                  <p className="text-sm text-text-secondary">Showing</p>
                  <p className="text-xl font-semibold text-text-primary">
                    {reviews.length} reviews
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Error State */}
        {error && (
          <div className="mb-6 bg-red-50 border border-red-200 rounded-lg p-4 flex items-center space-x-3">
            <AlertTriangle className="w-5 h-5 text-red-600" />
            <div>
              <p className="text-red-800 font-medium">Error</p>
              <p className="text-red-600 text-sm">{error}</p>
            </div>
          </div>
        )}

        {/* Loading State */}
        {loading && (
          <div className="text-center py-12">
            <RefreshCw className="w-8 h-8 animate-spin text-accent-primary mx-auto mb-4" />
            <p className="text-text-secondary">Loading pending reviews...</p>
          </div>
        )}

        {/* Empty State */}
        {!loading && reviews.length === 0 && !error && (
          <div className="text-center py-12">
            <CheckCircle className="w-16 h-16 text-green-600 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-text-primary mb-2">No Pending Reviews</h3>
            <p className="text-text-secondary">
              All reviews have been moderated. Great job!
            </p>
          </div>
        )}

        {/* Reviews List */}
        {!loading && reviews.length > 0 && (
          <div className="space-y-6">
            {reviews.map((review) => (
              <ReviewCard
                key={review.review_id}
                review={review}
                onApprove={handleApprove}
                onReject={handleReject}
                loading={actionLoading}
              />
            ))}
          </div>
        )}

        {/* Pagination */}
        {!loading && pagination.totalPages > 1 && (
          <div className="mt-8 flex items-center justify-between">
            <button
              onClick={() => handlePageChange(pagination.page - 1)}
              disabled={!pagination.hasPrev || loading}
              className="bg-surface-primary border border-border-primary text-text-primary px-4 py-2 rounded-lg hover:border-accent-primary transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Previous
            </button>
            
            <span className="text-text-secondary">
              Page {pagination.page} of {pagination.totalPages}
            </span>
            
            <button
              onClick={() => handlePageChange(pagination.page + 1)}
              disabled={!pagination.hasNext || loading}
              className="bg-surface-primary border border-border-primary text-text-primary px-4 py-2 rounded-lg hover:border-accent-primary transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Next
            </button>
          </div>
        )}

        {/* Confirmation Dialog */}
      <ConfirmDialog
        isOpen={confirmDialog.isOpen}
        title={confirmDialog.type === 'approve' ? 'Approve Review' : 'Reject Review'}
        content={`Are you sure you want to ${confirmDialog.type} this review for ${confirmDialog.companyName}? This action cannot be undone.`}
        confirmText={confirmDialog.type === 'approve' ? 'Approve' : 'Reject'}
        cancelText="Cancel"
        onConfirm={handleConfirm}
        onCancel={() => setConfirmDialog({ isOpen: false, type: 'approve', reviewId: '', companyName: '' })}
        loading={actionLoading}
        type={confirmDialog.type}
      />
    </AdminLayout>
  );
}

(()=>{var e={};e.id=673,e.ids=[673],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},12909:(e,t,r)=>{"use strict";r.d(t,{DU:()=>c,Qi:()=>l,h8:()=>u,ne:()=>d});var s=r(85663),a=r(43205),i=r.n(a);let n=()=>{let e=process.env.JWT_SECRET;if(!e)throw Error("JWT_SECRET environment variable is required");return e},o=process.env.JWT_EXPIRES_IN||"7d";class u{static{this.SALT_ROUNDS=12}static async hashPassword(e){try{let t=await s.Ay.genSalt(this.SALT_ROUNDS);return await s.Ay.hash(e,t)}catch(e){throw Error("Failed to hash password")}}static async verifyPassword(e,t){try{return await s.Ay.compare(e,t)}catch(e){throw Error("Failed to verify password")}}}class c{static generateToken(e){try{return i().sign(e,n(),{expiresIn:o,algorithm:"HS256"})}catch(e){throw Error("Failed to generate JWT token")}}static verifyToken(e){try{return i().verify(e,n(),{algorithms:["HS256"]})}catch(e){if(e instanceof i().TokenExpiredError)throw Error("Token has expired");if(e instanceof i().JsonWebTokenError)throw Error("Invalid token");throw Error("Token verification failed")}}static extractTokenFromHeader(e){return e&&e.startsWith("Bearer ")?e.substring(7):null}}let l={NAME:"haq_auth_token",OPTIONS:{httpOnly:!0,secure:!0,sameSite:"strict",maxAge:604800,path:"/"}};class d{static isValidEmail(e){return/^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/.test(e)&&e.length<=255}static isValidUsername(e){return/^[a-zA-Z0-9_]{3,50}$/.test(e)}static validatePassword(e){let t=[];return e.length<8&&t.push("Password must be at least 8 characters long"),e.length>128&&t.push("Password must be less than 128 characters"),/[a-z]/.test(e)||t.push("Password must contain at least one lowercase letter"),/[A-Z]/.test(e)||t.push("Password must contain at least one uppercase letter"),/[0-9]/.test(e)||t.push("Password must contain at least one number"),/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(e)||t.push("Password must contain at least one special character"),{isValid:0===t.length,errors:t}}static sanitizeInput(e){return e.trim().replace(/[<>]/g,"").substring(0,1e3)}}},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},39727:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},51641:(e,t,r)=>{"use strict";r.d(t,{L:()=>n,b:()=>o});var s=r(44999),a=r(12909),i=r(56621);class n{static async setAuthCookie(e){(await (0,s.UL)()).set(a.Qi.NAME,e,a.Qi.OPTIONS)}static async getAuthToken(){try{let e=(await (0,s.UL)()).get(a.Qi.NAME);return e?.value||null}catch(e){return null}}static async removeAuthCookie(){(await (0,s.UL)()).delete(a.Qi.NAME)}}class o{static async getCurrentUser(){try{let e=await n.getAuthToken();if(!e)return null;let t=a.DU.verifyToken(e),{data:r,error:s}=await i.ND.from("users").select("user_id, username, email, role, created_at, updated_at").eq("user_id",t.user_id).limit(1);if(s||!r||0===r.length)return null;return r[0]}catch(e){return null}}static async isAuthenticated(){return null!==await this.getCurrentUser()}static async isAdmin(){try{let e=await n.getAuthToken();if(!e)return!1;let t=a.DU.verifyToken(e);return"admin"===t.role}catch(e){return!1}}static async verifyTokenAndGetUser(e){try{let t=a.DU.verifyToken(e),{data:r,error:s}=await i.ND.from("users").select("user_id, username, email, role, created_at, updated_at").eq("user_id",t.user_id).limit(1);if(s||!r||0===r.length)return null;return r[0]}catch(e){return null}}}},51906:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=51906,e.exports=t},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56621:(e,t,r)=>{"use strict";r.d(t,{ND:()=>i});var s=r(39398);r(98766);let a=null,i=a=(0,s.createClient)("https://wqbuilazpyxpwyuwuqpi.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6IndxYnVpbGF6cHl4cHd5dXd1cXBpIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTA1NTYyNDMsImV4cCI6MjA2NjEzMjI0M30.GeRI54Rskwdbfm9_lRxy1-7YQ8vA74JWblNF2GRpzrI")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{},97371:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>g,routeModule:()=>y,serverHooks:()=>f,workAsyncStorage:()=>w,workUnitAsyncStorage:()=>x});var s={};r.r(s),r.d(s,{DELETE:()=>m,GET:()=>d,POST:()=>p,PUT:()=>h});var a=r(96559),i=r(48088),n=r(37719),o=r(32190),u=r(12909),c=r(51641),l=r(56621);async function d(e){try{let e,t=await c.L.getAuthToken();if(!t)return o.NextResponse.json({success:!1,message:"Authentication required"},{status:401});try{e=u.DU.verifyToken(t)}catch(e){return await c.L.removeAuthCookie(),o.NextResponse.json({success:!1,message:"Invalid or expired token"},{status:401})}let{data:r,error:s}=await l.ND.from("users").select("user_id, username, email, role, created_at").eq("user_id",e.user_id).limit(1);if(s)return console.error("Database error fetching user profile:",s),o.NextResponse.json({success:!1,message:"Internal server error"},{status:500});if(!r||0===r.length)return await c.L.removeAuthCookie(),o.NextResponse.json({success:!1,message:"User not found"},{status:404});let a=r[0];return o.NextResponse.json({success:!0,message:"User profile retrieved successfully",user:{user_id:a.user_id,username:a.username,email:a.email,role:a.role,created_at:a.created_at}},{status:200})}catch(e){return console.error("Get user profile error:",e),o.NextResponse.json({success:!1,message:"Internal server error"},{status:500})}}async function p(){return o.NextResponse.json({success:!1,message:"Method not allowed"},{status:405})}async function h(){return o.NextResponse.json({success:!1,message:"Method not allowed"},{status:405})}async function m(){return o.NextResponse.json({success:!1,message:"Method not allowed"},{status:405})}let y=new a.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/auth/me/route",pathname:"/api/auth/me",filename:"route",bundlePath:"app/api/auth/me/route"},resolvedPagePath:"D:\\Haq website v1\\haq-frontend-nextjs\\src\\app\\api\\auth\\me\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:w,workUnitAsyncStorage:x,serverHooks:f}=y;function g(){return(0,n.patchFetch)({workAsyncStorage:w,workUnitAsyncStorage:x})}}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,580,398,766,358],()=>r(97371));module.exports=s})();
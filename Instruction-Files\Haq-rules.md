[START] Haq MVP Development Directives v1.0
Objective: Construct the Minimum Viable Product (MVP) for the "Haq" web application, prioritizing security, anonymity, and performance from genesis. These directives are to be followed without exception.

Current Timestamp: 2025-06-22T06:48:52+05:00

RULESET-100: CORE ARCHITECTURE
RULE-101: SEPARATION_OF_CONCERNS

The project MUST be composed of three (3) distinct, independently deployable applications:
haq-frontend: The public-facing website. Target: anonymous users. Optimization: extreme performance and speed.
haq-backend: The centralized API server. Target: provides data to all applications. Optimization: security and efficient data processing.
haq-admin-panel: The internal moderation tool. Target: admin users. Optimization: functionality over raw speed.
RULE-102: DATABASE_ISOLATION

Two (2) logically and physically separate database schemas MUST be created:
haq_users_db: Stores all Personally Identifiable Information (PII), specifically the users table. Access to this schema must be maximally restricted.
haq_content_db: Stores all non-PII, public-facing content (companies, reviews).
Constraint: No FOREIGN KEY relationship shall exist between haq_users_db and haq_content_db.
RULE-103: STATELESS_BACKEND

The haq-backend MUST be stateless. All session state must be managed via client-side JWTs.
RULESET-200: API SPECIFICATION (v1)
Base Path: /api/v1

Group: PUBLIC (Authorization: NONE)
Directive: These endpoints MUST be highly optimized for speed and heavily cached. They MUST NOT return any sensitive data.
endpoint_id	method	path	description	response_contract_exclusion	caching_policy
PUB-01	GET	/companies	Get paginated list of all companies.	N/A	CDN_SHORT
PUB-02	GET	/companies/search?q={query}	Search for companies by name.	N/A	API_MEDIUM
PUB-03	GET	/companies/{companyId}	Get details for a single company.	N/A	CDN_SHORT
PUB-04	GET	/companies/{companyId}/reviews	Get approved reviews for a company.	author_id	CDN_SHORT

Export to Sheets
Group: AUTHENTICATED (Authorization: JWT_USER)
Directive: These endpoints require a valid user JWT.
endpoint_id	method	path	description
AUTH-01	POST	/auth/register	Register a new user.
AUTH-02	POST	/auth/login	Login and receive a JWT.
AUTH-03	POST	/reviews	Submit a new review. status defaults to pending.

Export to Sheets
Group: ADMIN (Authorization: JWT_ADMIN)
Directive: These endpoints require a valid JWT where payload role is admin. Access MUST be strictly denied otherwise.
endpoint_id	method	path	description
ADMIN-01	POST	/admin/companies	Create a new company profile.
ADMIN-02	GET	/admin/reviews/pending	Get all reviews with status = pending.
ADMIN-03	PATCH	/admin/reviews/{reviewId}/status	Update a review's status (approved/rejected).

Export to Sheets
RULESET-300: FRONTEND PERFORMANCE DIRECTIVES (haq-frontend)
RULE-301: SERVER_SIDE_RENDERING (SSR)

The haq-frontend application MUST be built using a framework that supports SSR. Recommended: Next.js.
All primary public pages (Homepage, Company Page, Search Page) MUST be server-side rendered to ensure minimal Time-to-First-Byte (TTFB) and fast First Contentful Paint (FCP).
RULE-302: EFFICIENT_DATA_FETCHING

Client-side "network waterfalls" are forbidden for initial page loads.
Use the framework's SSR data-fetching method (e.g., getServerSideProps in Next.js) to fetch all required data for a page on the server before rendering. Consolidate data from multiple API calls into a single props object.
RULE-303: CODE_SPLITTING

Code splitting MUST be implemented at the route level. The framework (Next.js) handles this automatically.
Any component exceeding 25kB in bundle size or not visible "above the fold" MUST be lazy-loaded using a dynamic import. Directive: Use next/dynamic.
RULE-304: ASSET_OPTIMIZATION

All images MUST be served via a framework-native optimization component (e.g., next/image).
This component MUST be configured to serve modern formats like WebP and lazy-load off-screen images by default.
RULESET-400: CACHING HIERARCHY
RULE-401: CDN_CACHE (LAYER 1)

Implementation: Vercel Edge Network or Cloudflare.
Target: Static assets (.js, .css, images, fonts) and rendered HTML of public, anonymous pages.
Policy:
Static assets: Cache-Control: public, max-age=31536000, immutable.
Public SSR Pages (PUB-03, PUB-04 destinations): Cache-Control: s-maxage=300, stale-while-revalidate=600. (5-minute cache, 10-minute revalidation window).
RULE-402: API_CACHE (LAYER 2)

Implementation: In-memory cache (e.g., Redis) at the API Gateway level.
Target: PUBLIC API endpoints that are frequently accessed but not user-specific.
Policy:
PUB-02 (/companies/search): TTL = 60 seconds.
PUB-01 (/companies): TTL = 120 seconds.
RULE-403: CLIENT_SIDE_CACHE (LAYER 3)

Implementation: A state-management library with automatic caching. Recommended: SWR.
Target: All data fetched on the client side.
Policy: Use the default "stale-while-revalidate" strategy to provide an instant UI response from cache while fetching fresh data in the background.
RULESET-500: TECH STACK & LIBRARY MANIFEST
Frontend Framework: Next.js (Reason: SSR, code splitting, image optimization).
Client-Side Data Fetching: SWR (Reason: Perfect Next.js integration, performance-first caching strategy).
Styling: Tailwind CSS (Reason: Utility-first, minimal CSS bundle size, no runtime overhead).
UI Components: A headless library such as Headless UI or Radix UI. (Reason: Maximum performance and accessibility with zero pre-shipped styles or JS overhead).
Forms: React Hook Form (Reason: Minimizes re-renders for optimal performance in complex forms).
Backend Framework: Node.js with Express.js or Fastify.
Database: PostgreSQL via Supabase (Reason: Managed PostgreSQL with built-in auth, real-time subscriptions, and API generation).
Database Management: Supabase (Reason: Provides managed PostgreSQL, authentication, real-time features, and direct SQL access via Management API).
Password Hashing: Argon2 (preferred) or bcrypt.
RULESET-600: SECURITY MANDATE
RULE-601: ANONYMITY_BY_DESIGN

No server or application logs shall capture user IP addresses.
The author_id field from the reviews table MUST NEVER be included in any public-facing API response. This must be enforced at the backend API serialization layer.
RULE-602: AUTHENTICATION & AUTHORIZATION

Authentication MUST be performed using JWTs stored in HttpOnly, Secure, SameSite=Strict cookies.
Authorization for ADMIN routes MUST check for role: 'admin' in the validated JWT payload on the backend for every request.
RULE-603: INPUT_SANITIZATION

All user-generated content (reviews, search queries, etc.) MUST be sanitized on the backend before being stored in the database to prevent Stored XSS attacks. Use a library like DOMPurify.
All database queries MUST use parameterized statements to prevent SQL injection.
Conclusion: Adherence to these directives will result in the successful, secure, and performant creation of the Haq MVP, establishing a solid foundation for all future development phases.

[END] Haq MVP Development Directives v1.0
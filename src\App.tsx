import React from 'react';
import { <PERSON>rowserRouter as Router, Routes, Route } from 'react-router-dom';
import { Header } from './components/layout/Header';
import { Footer } from './components/layout/Footer';
import { HomePage } from './pages/HomePage';
import { CompanyProfilePage } from './pages/CompanyProfilePage';
import { ReviewSubmissionPage } from './pages/ReviewSubmissionPage';
import { SalaryComparePage } from './pages/SalaryComparePage';
import { CommunityPage } from './pages/CommunityPage';
import { CompanySearchPage } from './pages/CompanySearchPage';

function App() {
  return (
    <Router>
      <div className="min-h-screen bg-gray-50 flex flex-col">
        <Header />
        <main className="flex-1">
          <Routes>
            <Route path="/" element={<HomePage />} />
            <Route path="/companies" element={<CompanySearchPage />} />
            <Route path="/company/:id" element={<CompanyProfilePage />} />
            <Route path="/review/submit" element={<ReviewSubmissionPage />} />
            <Route path="/salaries" element={<SalaryComparePage />} />
            <Route path="/community" element={<CommunityPage />} />
          </Routes>
        </main>
        <Footer />
      </div>
    </Router>
  );
}

export default App;
[{"D:\\Haq website v1\\haq-frontend-nextjs\\src\\app\\admin\\companies\\page.tsx": "1", "D:\\Haq website v1\\haq-frontend-nextjs\\src\\app\\admin\\login\\page.tsx": "2", "D:\\Haq website v1\\haq-frontend-nextjs\\src\\app\\admin\\page.tsx": "3", "D:\\Haq website v1\\haq-frontend-nextjs\\src\\app\\api\\admin\\companies\\route.ts": "4", "D:\\Haq website v1\\haq-frontend-nextjs\\src\\app\\api\\auth\\login\\route.ts": "5", "D:\\Haq website v1\\haq-frontend-nextjs\\src\\app\\api\\auth\\logout\\route.ts": "6", "D:\\Haq website v1\\haq-frontend-nextjs\\src\\app\\api\\auth\\me\\route.ts": "7", "D:\\Haq website v1\\haq-frontend-nextjs\\src\\app\\api\\auth\\register\\route.ts": "8", "D:\\Haq website v1\\haq-frontend-nextjs\\src\\app\\api\\companies\\route.ts": "9", "D:\\Haq website v1\\haq-frontend-nextjs\\src\\app\\api\\companies\\[id]\\reviews\\route.ts": "10", "D:\\Haq website v1\\haq-frontend-nextjs\\src\\app\\api\\companies\\[id]\\route.ts": "11", "D:\\Haq website v1\\haq-frontend-nextjs\\src\\app\\api\\reviews\\route.ts": "12", "D:\\Haq website v1\\haq-frontend-nextjs\\src\\app\\api\\search\\companies\\route.ts": "13", "D:\\Haq website v1\\haq-frontend-nextjs\\src\\app\\auth\\login\\page.tsx": "14", "D:\\Haq website v1\\haq-frontend-nextjs\\src\\app\\auth\\signup\\page.tsx": "15", "D:\\Haq website v1\\haq-frontend-nextjs\\src\\app\\companies\\page.tsx": "16", "D:\\Haq website v1\\haq-frontend-nextjs\\src\\app\\companies\\[companyId]\\page.tsx": "17", "D:\\Haq website v1\\haq-frontend-nextjs\\src\\app\\layout.tsx": "18", "D:\\Haq website v1\\haq-frontend-nextjs\\src\\app\\page.tsx": "19", "D:\\Haq website v1\\haq-frontend-nextjs\\src\\app\\review\\submit\\page.tsx": "20", "D:\\Haq website v1\\haq-frontend-nextjs\\src\\app\\search\\page.tsx": "21", "D:\\Haq website v1\\haq-frontend-nextjs\\src\\components\\admin\\AdminLayout.tsx": "22", "D:\\Haq website v1\\haq-frontend-nextjs\\src\\components\\common\\ClientOnly.tsx": "23", "D:\\Haq website v1\\haq-frontend-nextjs\\src\\components\\common\\CompanyCard.tsx": "24", "D:\\Haq website v1\\haq-frontend-nextjs\\src\\components\\common\\ErrorBoundary.tsx": "25", "D:\\Haq website v1\\haq-frontend-nextjs\\src\\components\\common\\SearchBar.tsx": "26", "D:\\Haq website v1\\haq-frontend-nextjs\\src\\components\\companies\\CompaniesListClient.tsx": "27", "D:\\Haq website v1\\haq-frontend-nextjs\\src\\components\\companies\\CompanyDetails.tsx": "28", "D:\\Haq website v1\\haq-frontend-nextjs\\src\\components\\companies\\CompanyReviews.tsx": "29", "D:\\Haq website v1\\haq-frontend-nextjs\\src\\components\\layout\\Footer.tsx": "30", "D:\\Haq website v1\\haq-frontend-nextjs\\src\\components\\layout\\Header.tsx": "31", "D:\\Haq website v1\\haq-frontend-nextjs\\src\\components\\reviews\\ReviewForm.tsx": "32", "D:\\Haq website v1\\haq-frontend-nextjs\\src\\contexts\\AuthContext.tsx": "33", "D:\\Haq website v1\\haq-frontend-nextjs\\src\\lib\\admin-middleware.ts": "34", "D:\\Haq website v1\\haq-frontend-nextjs\\src\\lib\\auth-client.ts": "35", "D:\\Haq website v1\\haq-frontend-nextjs\\src\\lib\\auth-server.ts": "36", "D:\\Haq website v1\\haq-frontend-nextjs\\src\\lib\\auth.ts": "37", "D:\\Haq website v1\\haq-frontend-nextjs\\src\\lib\\sanitization.ts": "38", "D:\\Haq website v1\\haq-frontend-nextjs\\src\\lib\\supabase.ts": "39", "D:\\Haq website v1\\haq-frontend-nextjs\\src\\providers\\SWRProvider.tsx": "40"}, {"size": 15958, "mtime": 1750565922723, "results": "41", "hashOfConfig": "42"}, {"size": 7814, "mtime": 1750565827334, "results": "43", "hashOfConfig": "42"}, {"size": 8291, "mtime": 1750565862484, "results": "44", "hashOfConfig": "42"}, {"size": 6159, "mtime": 1750706771857, "results": "45", "hashOfConfig": "42"}, {"size": 5227, "mtime": 1750706705101, "results": "46", "hashOfConfig": "42"}, {"size": 1856, "mtime": 1750564745660, "results": "47", "hashOfConfig": "42"}, {"size": 3023, "mtime": 1750708067374, "results": "48", "hashOfConfig": "42"}, {"size": 5522, "mtime": 1750564667246, "results": "49", "hashOfConfig": "42"}, {"size": 3969, "mtime": 1750707290694, "results": "50", "hashOfConfig": "42"}, {"size": 6820, "mtime": 1750708886110, "results": "51", "hashOfConfig": "42"}, {"size": 4514, "mtime": 1750708844973, "results": "52", "hashOfConfig": "42"}, {"size": 6832, "mtime": 1750707335215, "results": "53", "hashOfConfig": "42"}, {"size": 4599, "mtime": 1750711575294, "results": "54", "hashOfConfig": "42"}, {"size": 11562, "mtime": 1750565118129, "results": "55", "hashOfConfig": "42"}, {"size": 15057, "mtime": 1750565099631, "results": "56", "hashOfConfig": "42"}, {"size": 1091, "mtime": 1750709259476, "results": "57", "hashOfConfig": "42"}, {"size": 4135, "mtime": 1750709346999, "results": "58", "hashOfConfig": "42"}, {"size": 1679, "mtime": 1750710564466, "results": "59", "hashOfConfig": "42"}, {"size": 12007, "mtime": 1750711486562, "results": "60", "hashOfConfig": "42"}, {"size": 755, "mtime": 1750708519360, "results": "61", "hashOfConfig": "42"}, {"size": 11665, "mtime": 1750711544310, "results": "62", "hashOfConfig": "42"}, {"size": 7024, "mtime": 1750565789376, "results": "63", "hashOfConfig": "42"}, {"size": 643, "mtime": 1750709844599, "results": "64", "hashOfConfig": "42"}, {"size": 7780, "mtime": 1750711033976, "results": "65", "hashOfConfig": "42"}, {"size": 1553, "mtime": 1750711011990, "results": "66", "hashOfConfig": "42"}, {"size": 1981, "mtime": 1750711473593, "results": "67", "hashOfConfig": "42"}, {"size": 10866, "mtime": 1750709303596, "results": "68", "hashOfConfig": "42"}, {"size": 8733, "mtime": 1750708957012, "results": "69", "hashOfConfig": "42"}, {"size": 11210, "mtime": 1750709004132, "results": "70", "hashOfConfig": "42"}, {"size": 4615, "mtime": 1750559093271, "results": "71", "hashOfConfig": "42"}, {"size": 12061, "mtime": 1750564544965, "results": "72", "hashOfConfig": "42"}, {"size": 29802, "mtime": 1750708152017, "results": "73", "hashOfConfig": "42"}, {"size": 8434, "mtime": 1750708180960, "results": "74", "hashOfConfig": "42"}, {"size": 4331, "mtime": 1750565705541, "results": "75", "hashOfConfig": "42"}, {"size": 3950, "mtime": 1750565091242, "results": "76", "hashOfConfig": "42"}, {"size": 3346, "mtime": 1750706648796, "results": "77", "hashOfConfig": "42"}, {"size": 6410, "mtime": 1750565064189, "results": "78", "hashOfConfig": "42"}, {"size": 7351, "mtime": 1750707548536, "results": "79", "hashOfConfig": "42"}, {"size": 5613, "mtime": 1750710248393, "results": "80", "hashOfConfig": "42"}, {"size": 3640, "mtime": 1750563028273, "results": "81", "hashOfConfig": "42"}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "d5o054", {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 9, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 1, "fixableWarningCount": 0, "source": null}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 6, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "142", "messages": "143", "suppressedMessages": "144", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "145", "messages": "146", "suppressedMessages": "147", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "148", "messages": "149", "suppressedMessages": "150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "151", "messages": "152", "suppressedMessages": "153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "154", "messages": "155", "suppressedMessages": "156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "157", "messages": "158", "suppressedMessages": "159", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "160", "messages": "161", "suppressedMessages": "162", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 1, "fixableWarningCount": 0, "source": null}, {"filePath": "163", "messages": "164", "suppressedMessages": "165", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "166", "messages": "167", "suppressedMessages": "168", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 1, "fixableWarningCount": 0, "source": null}, {"filePath": "169", "messages": "170", "suppressedMessages": "171", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "172", "messages": "173", "suppressedMessages": "174", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "175", "messages": "176", "suppressedMessages": "177", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "178", "messages": "179", "suppressedMessages": "180", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "181", "messages": "182", "suppressedMessages": "183", "errorCount": 6, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "184", "messages": "185", "suppressedMessages": "186", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "187", "messages": "188", "suppressedMessages": "189", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "190", "messages": "191", "suppressedMessages": "192", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "193", "messages": "194", "suppressedMessages": "195", "errorCount": 6, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "196", "messages": "197", "suppressedMessages": "198", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "199", "messages": "200", "suppressedMessages": "201", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "D:\\Haq website v1\\haq-frontend-nextjs\\src\\app\\admin\\companies\\page.tsx", ["202"], [], "D:\\Haq website v1\\haq-frontend-nextjs\\src\\app\\admin\\login\\page.tsx", ["203"], [], "D:\\Haq website v1\\haq-frontend-nextjs\\src\\app\\admin\\page.tsx", [], [], "D:\\Haq website v1\\haq-frontend-nextjs\\src\\app\\api\\admin\\companies\\route.ts", ["204", "205", "206", "207", "208", "209", "210", "211", "212"], [], "D:\\Haq website v1\\haq-frontend-nextjs\\src\\app\\api\\auth\\login\\route.ts", ["213"], [], "D:\\Haq website v1\\haq-frontend-nextjs\\src\\app\\api\\auth\\logout\\route.ts", ["214", "215"], [], "D:\\Haq website v1\\haq-frontend-nextjs\\src\\app\\api\\auth\\me\\route.ts", ["216", "217"], [], "D:\\Haq website v1\\haq-frontend-nextjs\\src\\app\\api\\auth\\register\\route.ts", [], [], "D:\\Haq website v1\\haq-frontend-nextjs\\src\\app\\api\\companies\\route.ts", ["218"], [], "D:\\Haq website v1\\haq-frontend-nextjs\\src\\app\\api\\companies\\[id]\\reviews\\route.ts", [], [], "D:\\Haq website v1\\haq-frontend-nextjs\\src\\app\\api\\companies\\[id]\\route.ts", ["219", "220"], [], "D:\\Haq website v1\\haq-frontend-nextjs\\src\\app\\api\\reviews\\route.ts", ["221", "222"], [], "D:\\Haq website v1\\haq-frontend-nextjs\\src\\app\\api\\search\\companies\\route.ts", [], [], "D:\\Haq website v1\\haq-frontend-nextjs\\src\\app\\auth\\login\\page.tsx", ["223"], [], "D:\\Haq website v1\\haq-frontend-nextjs\\src\\app\\auth\\signup\\page.tsx", [], [], "D:\\Haq website v1\\haq-frontend-nextjs\\src\\app\\companies\\page.tsx", [], [], "D:\\Haq website v1\\haq-frontend-nextjs\\src\\app\\companies\\[companyId]\\page.tsx", [], [], "D:\\Haq website v1\\haq-frontend-nextjs\\src\\app\\layout.tsx", [], [], "D:\\Haq website v1\\haq-frontend-nextjs\\src\\app\\page.tsx", ["224", "225", "226", "227", "228", "229"], [], "D:\\Haq website v1\\haq-frontend-nextjs\\src\\app\\review\\submit\\page.tsx", [], [], "D:\\Haq website v1\\haq-frontend-nextjs\\src\\app\\search\\page.tsx", ["230", "231", "232", "233", "234"], [], "D:\\Haq website v1\\haq-frontend-nextjs\\src\\components\\admin\\AdminLayout.tsx", ["235"], [], "D:\\Haq website v1\\haq-frontend-nextjs\\src\\components\\common\\ClientOnly.tsx", [], [], "D:\\Haq website v1\\haq-frontend-nextjs\\src\\components\\common\\CompanyCard.tsx", [], [], "D:\\Haq website v1\\haq-frontend-nextjs\\src\\components\\common\\ErrorBoundary.tsx", [], [], "D:\\Haq website v1\\haq-frontend-nextjs\\src\\components\\common\\SearchBar.tsx", [], [], "D:\\Haq website v1\\haq-frontend-nextjs\\src\\components\\companies\\CompaniesListClient.tsx", ["236", "237", "238"], [], "D:\\Haq website v1\\haq-frontend-nextjs\\src\\components\\companies\\CompanyDetails.tsx", ["239"], [], "D:\\Haq website v1\\haq-frontend-nextjs\\src\\components\\companies\\CompanyReviews.tsx", ["240", "241", "242", "243", "244"], [], "D:\\Haq website v1\\haq-frontend-nextjs\\src\\components\\layout\\Footer.tsx", [], [], "D:\\Haq website v1\\haq-frontend-nextjs\\src\\components\\layout\\Header.tsx", ["245"], [], "D:\\Haq website v1\\haq-frontend-nextjs\\src\\components\\reviews\\ReviewForm.tsx", ["246", "247", "248", "249", "250", "251", "252"], [], "D:\\Haq website v1\\haq-frontend-nextjs\\src\\contexts\\AuthContext.tsx", ["253", "254", "255"], [], "D:\\Haq website v1\\haq-frontend-nextjs\\src\\lib\\admin-middleware.ts", ["256", "257", "258", "259", "260", "261"], [], "D:\\Haq website v1\\haq-frontend-nextjs\\src\\lib\\auth-client.ts", ["262", "263"], [], "D:\\Haq website v1\\haq-frontend-nextjs\\src\\lib\\auth-server.ts", ["264", "265", "266", "267", "268"], [], "D:\\Haq website v1\\haq-frontend-nextjs\\src\\lib\\auth.ts", ["269", "270", "271", "272", "273"], [], "D:\\Haq website v1\\haq-frontend-nextjs\\src\\lib\\sanitization.ts", ["274", "275", "276", "277", "278", "279", "280"], [], "D:\\Haq website v1\\haq-frontend-nextjs\\src\\lib\\supabase.ts", [], [], "D:\\Haq website v1\\haq-frontend-nextjs\\src\\providers\\SWRProvider.tsx", ["281", "282"], [], {"ruleId": "283", "severity": 1, "message": "284", "line": 192, "column": 29, "nodeType": "285", "endLine": 196, "endColumn": 31}, {"ruleId": "286", "severity": 2, "message": "287", "line": 52, "column": 14, "nodeType": null, "messageId": "288", "endLine": 52, "endColumn": 19}, {"ruleId": "286", "severity": 2, "message": "289", "line": 23, "column": 51, "nodeType": null, "messageId": "288", "endLine": 23, "endColumn": 58}, {"ruleId": "290", "severity": 2, "message": "291", "line": 23, "column": 60, "nodeType": "292", "messageId": "293", "endLine": 23, "endColumn": 63, "suggestions": "294"}, {"ruleId": "286", "severity": 2, "message": "295", "line": 23, "column": 65, "nodeType": null, "messageId": "288", "endLine": 23, "endColumn": 69}, {"ruleId": "290", "severity": 2, "message": "291", "line": 23, "column": 71, "nodeType": "292", "messageId": "293", "endLine": 23, "endColumn": 74, "suggestions": "296"}, {"ruleId": "286", "severity": 2, "message": "297", "line": 70, "column": 37, "nodeType": null, "messageId": "288", "endLine": 70, "endColumn": 42}, {"ruleId": "286", "severity": 2, "message": "289", "line": 112, "column": 52, "nodeType": null, "messageId": "288", "endLine": 112, "endColumn": 59}, {"ruleId": "290", "severity": 2, "message": "291", "line": 112, "column": 61, "nodeType": "292", "messageId": "293", "endLine": 112, "endColumn": 64, "suggestions": "298"}, {"ruleId": "286", "severity": 2, "message": "295", "line": 112, "column": 66, "nodeType": null, "messageId": "288", "endLine": 112, "endColumn": 70}, {"ruleId": "290", "severity": 2, "message": "291", "line": 112, "column": 72, "nodeType": "292", "messageId": "293", "endLine": 112, "endColumn": 75, "suggestions": "299"}, {"ruleId": "286", "severity": 2, "message": "300", "line": 49, "column": 11, "nodeType": null, "messageId": "288", "endLine": 49, "endColumn": 20}, {"ruleId": "286", "severity": 2, "message": "301", "line": 10, "column": 28, "nodeType": null, "messageId": "288", "endLine": 10, "endColumn": 35}, {"ruleId": "286", "severity": 2, "message": "287", "line": 19, "column": 16, "nodeType": null, "messageId": "288", "endLine": 19, "endColumn": 21}, {"ruleId": "286", "severity": 2, "message": "301", "line": 18, "column": 27, "nodeType": null, "messageId": "288", "endLine": 18, "endColumn": 34}, {"ruleId": "286", "severity": 2, "message": "287", "line": 34, "column": 14, "nodeType": null, "messageId": "288", "endLine": 34, "endColumn": 19}, {"ruleId": "286", "severity": 2, "message": "297", "line": 52, "column": 37, "nodeType": null, "messageId": "288", "endLine": 52, "endColumn": 42}, {"ruleId": "286", "severity": 2, "message": "302", "line": 72, "column": 39, "nodeType": null, "messageId": "288", "endLine": 72, "endColumn": 49}, {"ruleId": "303", "severity": 2, "message": "304", "line": 79, "column": 9, "nodeType": "305", "messageId": "306", "endLine": 79, "endColumn": 25, "fix": "307"}, {"ruleId": "290", "severity": 2, "message": "291", "line": 10, "column": 36, "nodeType": "292", "messageId": "293", "endLine": 10, "endColumn": 39, "suggestions": "308"}, {"ruleId": "290", "severity": 2, "message": "291", "line": 177, "column": 21, "nodeType": "292", "messageId": "293", "endLine": 177, "endColumn": 24, "suggestions": "309"}, {"ruleId": "310", "severity": 2, "message": "311", "line": 282, "column": 18, "nodeType": "312", "messageId": "313", "suggestions": "314"}, {"ruleId": "286", "severity": 2, "message": "315", "line": 6, "column": 33, "nodeType": null, "messageId": "288", "endLine": 6, "endColumn": 37}, {"ruleId": "286", "severity": 2, "message": "316", "line": 6, "column": 66, "nodeType": null, "messageId": "288", "endLine": 6, "endColumn": 77}, {"ruleId": "286", "severity": 2, "message": "317", "line": 6, "column": 91, "nodeType": null, "messageId": "288", "endLine": 6, "endColumn": 104}, {"ruleId": "286", "severity": 2, "message": "318", "line": 6, "column": 106, "nodeType": null, "messageId": "288", "endLine": 6, "endColumn": 116}, {"ruleId": "286", "severity": 2, "message": "319", "line": 11, "column": 7, "nodeType": null, "messageId": "288", "endLine": 11, "endColumn": 24}, {"ruleId": "290", "severity": 2, "message": "291", "line": 236, "column": 51, "nodeType": "292", "messageId": "293", "endLine": 236, "endColumn": 54, "suggestions": "320"}, {"ruleId": "283", "severity": 1, "message": "284", "line": 49, "column": 13, "nodeType": "285", "endLine": 53, "endColumn": 15}, {"ruleId": "310", "severity": 2, "message": "321", "line": 202, "column": 38, "nodeType": "312", "messageId": "313", "suggestions": "322"}, {"ruleId": "310", "severity": 2, "message": "321", "line": 202, "column": 49, "nodeType": "312", "messageId": "313", "suggestions": "323"}, {"ruleId": "310", "severity": 2, "message": "321", "line": 237, "column": 52, "nodeType": "312", "messageId": "313", "suggestions": "324"}, {"ruleId": "310", "severity": 2, "message": "321", "line": 237, "column": 63, "nodeType": "312", "messageId": "313", "suggestions": "325"}, {"ruleId": "310", "severity": 2, "message": "311", "line": 83, "column": 52, "nodeType": "312", "messageId": "313", "suggestions": "326"}, {"ruleId": "303", "severity": 2, "message": "327", "line": 115, "column": 9, "nodeType": "305", "messageId": "306", "endLine": 115, "endColumn": 16, "fix": "328"}, {"ruleId": "310", "severity": 2, "message": "321", "line": 228, "column": 28, "nodeType": "312", "messageId": "313", "suggestions": "329"}, {"ruleId": "310", "severity": 2, "message": "321", "line": 228, "column": 51, "nodeType": "312", "messageId": "313", "suggestions": "330"}, {"ruleId": "283", "severity": 1, "message": "284", "line": 122, "column": 15, "nodeType": "285", "endLine": 126, "endColumn": 17}, {"ruleId": "286", "severity": 2, "message": "331", "line": 3, "column": 27, "nodeType": null, "messageId": "288", "endLine": 3, "endColumn": 36}, {"ruleId": "286", "severity": 2, "message": "332", "line": 62, "column": 23, "nodeType": null, "messageId": "288", "endLine": 62, "endColumn": 37}, {"ruleId": "286", "severity": 2, "message": "333", "line": 63, "column": 10, "nodeType": null, "messageId": "288", "endLine": 63, "endColumn": 19}, {"ruleId": "286", "severity": 2, "message": "334", "line": 63, "column": 21, "nodeType": null, "messageId": "288", "endLine": 63, "endColumn": 33}, {"ruleId": "303", "severity": 2, "message": "327", "line": 123, "column": 9, "nodeType": "305", "messageId": "306", "endLine": 123, "endColumn": 16, "fix": "335"}, {"ruleId": "286", "severity": 2, "message": "336", "line": 6, "column": 10, "nodeType": null, "messageId": "288", "endLine": 6, "endColumn": 16}, {"ruleId": "286", "severity": 2, "message": "337", "line": 49, "column": 11, "nodeType": null, "messageId": "288", "endLine": 49, "endColumn": 15}, {"ruleId": "286", "severity": 2, "message": "338", "line": 65, "column": 26, "nodeType": null, "messageId": "288", "endLine": 65, "endColumn": 33}, {"ruleId": "283", "severity": 1, "message": "284", "line": 349, "column": 29, "nodeType": "285", "endLine": 353, "endColumn": 31}, {"ruleId": "283", "severity": 1, "message": "284", "line": 400, "column": 19, "nodeType": "285", "endLine": 404, "endColumn": 21}, {"ruleId": "310", "severity": 2, "message": "311", "line": 713, "column": 65, "nodeType": "312", "messageId": "313", "suggestions": "339"}, {"ruleId": "310", "severity": 2, "message": "311", "line": 714, "column": 28, "nodeType": "312", "messageId": "313", "suggestions": "340"}, {"ruleId": "341", "severity": 2, "message": "342", "line": 778, "column": 15, "nodeType": "285", "endLine": 781, "endColumn": 16}, {"ruleId": "343", "severity": 1, "message": "344", "line": 97, "column": 6, "nodeType": "345", "endLine": 97, "endColumn": 8, "suggestions": "346"}, {"ruleId": "286", "severity": 2, "message": "287", "line": 153, "column": 14, "nodeType": null, "messageId": "288", "endLine": 153, "endColumn": 19}, {"ruleId": "286", "severity": 2, "message": "287", "line": 183, "column": 14, "nodeType": null, "messageId": "288", "endLine": 183, "endColumn": 19}, {"ruleId": "290", "severity": 2, "message": "291", "line": 12, "column": 10, "nodeType": "292", "messageId": "293", "endLine": 12, "endColumn": 13, "suggestions": "347"}, {"ruleId": "286", "severity": 2, "message": "301", "line": 22, "column": 39, "nodeType": null, "messageId": "288", "endLine": 22, "endColumn": 46}, {"ruleId": "290", "severity": 2, "message": "291", "line": 74, "column": 44, "nodeType": "292", "messageId": "293", "endLine": 74, "endColumn": 47, "suggestions": "348"}, {"ruleId": "290", "severity": 2, "message": "291", "line": 74, "column": 55, "nodeType": "292", "messageId": "293", "endLine": 74, "endColumn": 58, "suggestions": "349"}, {"ruleId": "290", "severity": 2, "message": "291", "line": 76, "column": 48, "nodeType": "292", "messageId": "293", "endLine": 76, "endColumn": 51, "suggestions": "350"}, {"ruleId": "290", "severity": 2, "message": "291", "line": 148, "column": 54, "nodeType": "292", "messageId": "293", "endLine": 148, "endColumn": 57, "suggestions": "351"}, {"ruleId": "286", "severity": 2, "message": "287", "line": 119, "column": 14, "nodeType": null, "messageId": "288", "endLine": 119, "endColumn": 19}, {"ruleId": "286", "severity": 2, "message": "287", "line": 139, "column": 14, "nodeType": null, "messageId": "288", "endLine": 139, "endColumn": 19}, {"ruleId": "286", "severity": 2, "message": "352", "line": 2, "column": 51, "nodeType": null, "messageId": "288", "endLine": 2, "endColumn": 61}, {"ruleId": "286", "severity": 2, "message": "287", "line": 25, "column": 14, "nodeType": null, "messageId": "288", "endLine": 25, "endColumn": 19}, {"ruleId": "286", "severity": 2, "message": "287", "line": 66, "column": 14, "nodeType": null, "messageId": "288", "endLine": 66, "endColumn": 19}, {"ruleId": "286", "severity": 2, "message": "287", "line": 93, "column": 14, "nodeType": null, "messageId": "288", "endLine": 93, "endColumn": 19}, {"ruleId": "286", "severity": 2, "message": "287", "line": 119, "column": 14, "nodeType": null, "messageId": "288", "endLine": 119, "endColumn": 19}, {"ruleId": "286", "severity": 2, "message": "287", "line": 53, "column": 14, "nodeType": null, "messageId": "288", "endLine": 53, "endColumn": 19}, {"ruleId": "286", "severity": 2, "message": "287", "line": 67, "column": 14, "nodeType": null, "messageId": "288", "endLine": 67, "endColumn": 19}, {"ruleId": "286", "severity": 2, "message": "287", "line": 86, "column": 14, "nodeType": null, "messageId": "288", "endLine": 86, "endColumn": 19}, {"ruleId": "286", "severity": 2, "message": "287", "line": 222, "column": 14, "nodeType": null, "messageId": "288", "endLine": 222, "endColumn": 19}, {"ruleId": "286", "severity": 2, "message": "287", "line": 236, "column": 14, "nodeType": null, "messageId": "288", "endLine": 236, "endColumn": 19}, {"ruleId": "353", "severity": 2, "message": "354", "line": 16, "column": 21, "nodeType": "355", "messageId": "356", "endLine": 16, "endColumn": 37}, {"ruleId": "290", "severity": 2, "message": "291", "line": 18, "column": 32, "nodeType": "292", "messageId": "293", "endLine": 18, "endColumn": 35, "suggestions": "357"}, {"ruleId": "286", "severity": 2, "message": "287", "line": 132, "column": 12, "nodeType": null, "messageId": "288", "endLine": 132, "endColumn": 17}, {"ruleId": "290", "severity": 2, "message": "291", "line": 141, "column": 57, "nodeType": "292", "messageId": "293", "endLine": 141, "endColumn": 60, "suggestions": "358"}, {"ruleId": "290", "severity": 2, "message": "291", "line": 151, "column": 23, "nodeType": "292", "messageId": "293", "endLine": 151, "endColumn": 26, "suggestions": "359"}, {"ruleId": "290", "severity": 2, "message": "291", "line": 155, "column": 21, "nodeType": "292", "messageId": "293", "endLine": 155, "endColumn": 24, "suggestions": "360"}, {"ruleId": "361", "severity": 1, "message": "362", "line": 277, "column": 1, "nodeType": "363", "endLine": 287, "endColumn": 3}, {"ruleId": "286", "severity": 2, "message": "364", "line": 8, "column": 9, "nodeType": null, "messageId": "288", "endLine": 8, "endColumn": 17}, {"ruleId": "290", "severity": 2, "message": "291", "line": 106, "column": 21, "nodeType": "292", "messageId": "293", "endLine": 106, "endColumn": 24, "suggestions": "365"}, "@next/next/no-img-element", "Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element", "JSXOpeningElement", "@typescript-eslint/no-unused-vars", "'error' is defined but never used.", "unusedVar", "'context' is defined but never used.", "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["366", "367"], "'user' is defined but never used.", ["368", "369"], "'count' is assigned a value but never used.", ["370", "371"], ["372", "373"], "'userAgent' is assigned a value but never used.", "'request' is defined but never used.", "'statsError' is assigned a value but never used.", "prefer-const", "'reviewStatistics' is never reassigned. Use 'const' instead.", "Identifier", "useConst", {"range": "374", "text": "375"}, ["376", "377"], ["378", "379"], "react/no-unescaped-entities", "`'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.", "JSXText", "unescapedEntityAlts", ["380", "381", "382", "383"], "'Star' is defined but never used.", "'CheckCircle' is defined but never used.", "'MessageCircle' is defined but never used.", "'DollarSign' is defined but never used.", "'featuredCompanies' is assigned a value but never used.", ["384", "385"], "`\"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`.", ["386", "387", "388", "389"], ["390", "391", "392", "393"], ["394", "395", "396", "397"], ["398", "399", "400", "401"], ["402", "403", "404", "405"], "'endPage' is never reassigned. Use 'const' instead.", {"range": "406", "text": "407"}, ["408", "409", "410", "411"], ["412", "413", "414", "415"], "'useEffect' is defined but never used.", "'setReviewsData' is assigned a value but never used.", "'isLoading' is assigned a value but never used.", "'setIsLoading' is assigned a value but never used.", {"range": "416", "text": "407"}, "'Search' is defined but never used.", "'user' is assigned a value but never used.", "'isValid' is assigned a value but never used.", ["417", "418", "419", "420"], ["421", "422", "423", "424"], "@next/next/no-html-link-for-pages", "Do not use an `<a>` element to navigate to `/`. Use `<Link />` from `next/link` instead. See: https://nextjs.org/docs/messages/no-html-link-for-pages", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'checkExistingSession'. Either include it or remove the dependency array.", "ArrayExpression", ["425"], ["426", "427"], ["428", "429"], ["430", "431"], ["432", "433"], ["434", "435"], "'JWTPayload' is defined but never used.", "@typescript-eslint/no-require-imports", "A `require()` style import is forbidden.", "CallExpression", "noRequireImports", ["436", "437"], ["438", "439"], ["440", "441"], ["442", "443"], "import/no-anonymous-default-export", "Assign object to a variable before exporting as module default", "ExportDefaultDeclaration", "'supabase' is assigned a value but never used.", ["444", "445"], {"messageId": "446", "fix": "447", "desc": "448"}, {"messageId": "449", "fix": "450", "desc": "451"}, {"messageId": "446", "fix": "452", "desc": "448"}, {"messageId": "449", "fix": "453", "desc": "451"}, {"messageId": "446", "fix": "454", "desc": "448"}, {"messageId": "449", "fix": "455", "desc": "451"}, {"messageId": "446", "fix": "456", "desc": "448"}, {"messageId": "449", "fix": "457", "desc": "451"}, [2062, 2248], "const reviewStatistics = {\n      total_reviews: 0,\n      average_rating: 0,\n      rating_distribution: {\n        1: 0,\n        2: 0,\n        3: 0,\n        4: 0,\n        5: 0\n      }\n    };", {"messageId": "446", "fix": "458", "desc": "448"}, {"messageId": "449", "fix": "459", "desc": "451"}, {"messageId": "446", "fix": "460", "desc": "448"}, {"messageId": "449", "fix": "461", "desc": "451"}, {"messageId": "462", "data": "463", "fix": "464", "desc": "465"}, {"messageId": "462", "data": "466", "fix": "467", "desc": "468"}, {"messageId": "462", "data": "469", "fix": "470", "desc": "471"}, {"messageId": "462", "data": "472", "fix": "473", "desc": "474"}, {"messageId": "446", "fix": "475", "desc": "448"}, {"messageId": "449", "fix": "476", "desc": "451"}, {"messageId": "462", "data": "477", "fix": "478", "desc": "479"}, {"messageId": "462", "data": "480", "fix": "481", "desc": "482"}, {"messageId": "462", "data": "483", "fix": "484", "desc": "485"}, {"messageId": "462", "data": "486", "fix": "487", "desc": "488"}, {"messageId": "462", "data": "489", "fix": "490", "desc": "479"}, {"messageId": "462", "data": "491", "fix": "492", "desc": "482"}, {"messageId": "462", "data": "493", "fix": "494", "desc": "485"}, {"messageId": "462", "data": "495", "fix": "496", "desc": "488"}, {"messageId": "462", "data": "497", "fix": "498", "desc": "479"}, {"messageId": "462", "data": "499", "fix": "500", "desc": "482"}, {"messageId": "462", "data": "501", "fix": "502", "desc": "485"}, {"messageId": "462", "data": "503", "fix": "504", "desc": "488"}, {"messageId": "462", "data": "505", "fix": "506", "desc": "479"}, {"messageId": "462", "data": "507", "fix": "508", "desc": "482"}, {"messageId": "462", "data": "509", "fix": "510", "desc": "485"}, {"messageId": "462", "data": "511", "fix": "512", "desc": "488"}, {"messageId": "462", "data": "513", "fix": "514", "desc": "465"}, {"messageId": "462", "data": "515", "fix": "516", "desc": "468"}, {"messageId": "462", "data": "517", "fix": "518", "desc": "471"}, {"messageId": "462", "data": "519", "fix": "520", "desc": "474"}, [3318, 3397], "const endPage = Math.min(pagination.totalPages, startPage + maxVisiblePages - 1);", {"messageId": "462", "data": "521", "fix": "522", "desc": "479"}, {"messageId": "462", "data": "523", "fix": "524", "desc": "482"}, {"messageId": "462", "data": "525", "fix": "526", "desc": "485"}, {"messageId": "462", "data": "527", "fix": "528", "desc": "488"}, {"messageId": "462", "data": "529", "fix": "530", "desc": "479"}, {"messageId": "462", "data": "531", "fix": "532", "desc": "482"}, {"messageId": "462", "data": "533", "fix": "534", "desc": "485"}, {"messageId": "462", "data": "535", "fix": "536", "desc": "488"}, [2909, 2988], {"messageId": "462", "data": "537", "fix": "538", "desc": "465"}, {"messageId": "462", "data": "539", "fix": "540", "desc": "468"}, {"messageId": "462", "data": "541", "fix": "542", "desc": "471"}, {"messageId": "462", "data": "543", "fix": "544", "desc": "474"}, {"messageId": "462", "data": "545", "fix": "546", "desc": "465"}, {"messageId": "462", "data": "547", "fix": "548", "desc": "468"}, {"messageId": "462", "data": "549", "fix": "550", "desc": "471"}, {"messageId": "462", "data": "551", "fix": "552", "desc": "474"}, {"desc": "553", "fix": "554"}, {"messageId": "446", "fix": "555", "desc": "448"}, {"messageId": "449", "fix": "556", "desc": "451"}, {"messageId": "446", "fix": "557", "desc": "448"}, {"messageId": "449", "fix": "558", "desc": "451"}, {"messageId": "446", "fix": "559", "desc": "448"}, {"messageId": "449", "fix": "560", "desc": "451"}, {"messageId": "446", "fix": "561", "desc": "448"}, {"messageId": "449", "fix": "562", "desc": "451"}, {"messageId": "446", "fix": "563", "desc": "448"}, {"messageId": "449", "fix": "564", "desc": "451"}, {"messageId": "446", "fix": "565", "desc": "448"}, {"messageId": "449", "fix": "566", "desc": "451"}, {"messageId": "446", "fix": "567", "desc": "448"}, {"messageId": "449", "fix": "568", "desc": "451"}, {"messageId": "446", "fix": "569", "desc": "448"}, {"messageId": "449", "fix": "570", "desc": "451"}, {"messageId": "446", "fix": "571", "desc": "448"}, {"messageId": "449", "fix": "572", "desc": "451"}, {"messageId": "446", "fix": "573", "desc": "448"}, {"messageId": "449", "fix": "574", "desc": "451"}, "suggestUnknown", {"range": "575", "text": "576"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "577", "text": "578"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "579", "text": "576"}, {"range": "580", "text": "578"}, {"range": "581", "text": "576"}, {"range": "582", "text": "578"}, {"range": "583", "text": "576"}, {"range": "584", "text": "578"}, {"range": "585", "text": "576"}, {"range": "586", "text": "578"}, {"range": "587", "text": "576"}, {"range": "588", "text": "578"}, "replaceWithAlt", {"alt": "589"}, {"range": "590", "text": "591"}, "Replace with `&apos;`.", {"alt": "592"}, {"range": "593", "text": "594"}, "Replace with `&lsquo;`.", {"alt": "595"}, {"range": "596", "text": "597"}, "Replace with `&#39;`.", {"alt": "598"}, {"range": "599", "text": "600"}, "Replace with `&rsquo;`.", {"range": "601", "text": "576"}, {"range": "602", "text": "578"}, {"alt": "603"}, {"range": "604", "text": "605"}, "Replace with `&quot;`.", {"alt": "606"}, {"range": "607", "text": "608"}, "Replace with `&ldquo;`.", {"alt": "609"}, {"range": "610", "text": "611"}, "Replace with `&#34;`.", {"alt": "612"}, {"range": "613", "text": "614"}, "Replace with `&rdquo;`.", {"alt": "603"}, {"range": "615", "text": "616"}, {"alt": "606"}, {"range": "617", "text": "618"}, {"alt": "609"}, {"range": "619", "text": "620"}, {"alt": "612"}, {"range": "621", "text": "622"}, {"alt": "603"}, {"range": "623", "text": "624"}, {"alt": "606"}, {"range": "625", "text": "626"}, {"alt": "609"}, {"range": "627", "text": "628"}, {"alt": "612"}, {"range": "629", "text": "630"}, {"alt": "603"}, {"range": "631", "text": "632"}, {"alt": "606"}, {"range": "633", "text": "634"}, {"alt": "609"}, {"range": "635", "text": "636"}, {"alt": "612"}, {"range": "637", "text": "638"}, {"alt": "589"}, {"range": "639", "text": "640"}, {"alt": "592"}, {"range": "641", "text": "642"}, {"alt": "595"}, {"range": "643", "text": "644"}, {"alt": "598"}, {"range": "645", "text": "646"}, {"alt": "603"}, {"range": "647", "text": "648"}, {"alt": "606"}, {"range": "649", "text": "650"}, {"alt": "609"}, {"range": "651", "text": "652"}, {"alt": "612"}, {"range": "653", "text": "654"}, {"alt": "603"}, {"range": "655", "text": "603"}, {"alt": "606"}, {"range": "656", "text": "606"}, {"alt": "609"}, {"range": "657", "text": "609"}, {"alt": "612"}, {"range": "658", "text": "612"}, {"alt": "589"}, {"range": "659", "text": "660"}, {"alt": "592"}, {"range": "661", "text": "662"}, {"alt": "595"}, {"range": "663", "text": "664"}, {"alt": "598"}, {"range": "665", "text": "666"}, {"alt": "589"}, {"range": "667", "text": "668"}, {"alt": "592"}, {"range": "669", "text": "670"}, {"alt": "595"}, {"range": "671", "text": "672"}, {"alt": "598"}, {"range": "673", "text": "674"}, "Update the dependencies array to be: [checkExistingSession]", {"range": "675", "text": "676"}, {"range": "677", "text": "576"}, {"range": "678", "text": "578"}, {"range": "679", "text": "576"}, {"range": "680", "text": "578"}, {"range": "681", "text": "576"}, {"range": "682", "text": "578"}, {"range": "683", "text": "576"}, {"range": "684", "text": "578"}, {"range": "685", "text": "576"}, {"range": "686", "text": "578"}, {"range": "687", "text": "576"}, {"range": "688", "text": "578"}, {"range": "689", "text": "576"}, {"range": "690", "text": "578"}, {"range": "691", "text": "576"}, {"range": "692", "text": "578"}, {"range": "693", "text": "576"}, {"range": "694", "text": "578"}, {"range": "695", "text": "576"}, {"range": "696", "text": "578"}, [934, 937], "unknown", [934, 937], "never", [945, 948], [945, 948], [3201, 3204], [3201, 3204], [3212, 3215], [3212, 3215], [377, 380], [377, 380], [5280, 5283], [5280, 5283], "&apos;", [10671, 10708], "\n              Don&apos;t have an account?", "&lsquo;", [10671, 10708], "\n              Don&lsquo;t have an account?", "&#39;", [10671, 10708], "\n              Don&#39;t have an account?", "&rsquo;", [10671, 10708], "\n              Don&rsquo;t have an account?", [11081, 11084], [11081, 11084], "&quot;", [6817, 6856], "\n                  Search Results for &quot;", "&ldquo;", [6817, 6856], "\n                  Search Results for &ldquo;", "&#34;", [6817, 6856], "\n                  Search Results for &#34;", "&rdquo;", [6817, 6856], "\n                  Search Results for &rdquo;", [6866, 6884], "&quot;\n                ", [6866, 6884], "&ldquo;\n                ", [6866, 6884], "&#34;\n                ", [6866, 6884], "&rdquo;\n                ", [8099, 8152], "\n                No companies match your search for &quot;", [8099, 8152], "\n                No companies match your search for &ldquo;", [8099, 8152], "\n                No companies match your search for &#34;", [8099, 8152], "\n                No companies match your search for &rdquo;", [8162, 8227], "&quot;. Try different keywords or browse all companies.\n              ", [8162, 8227], "&ldquo;. Try different keywords or browse all companies.\n              ", [8162, 8227], "&#34;. Try different keywords or browse all companies.\n              ", [8162, 8227], "&rdquo;. Try different keywords or browse all companies.\n              ", [1838, 1884], "You don&apos;t have permission to access this area.", [1838, 1884], "You don&lsquo;t have permission to access this area.", [1838, 1884], "You don&#39;t have permission to access this area.", [1838, 1884], "You don&rsquo;t have permission to access this area.", [8400, 8406], " for &quot;", [8400, 8406], " for &ldquo;", [8400, 8406], " for &#34;", [8400, 8406], " for &rdquo;", [8428, 8429], [8428, 8429], [8428, 8429], [8428, 8429], [26830, 27054], "\n                  By submitting this review, you confirm that it&apos;s based on your genuine experience\n                  and doesn't contain false information. Your review will be moderated before publication.\n                ", [26830, 27054], "\n                  By submitting this review, you confirm that it&lsquo;s based on your genuine experience\n                  and doesn't contain false information. Your review will be moderated before publication.\n                ", [26830, 27054], "\n                  By submitting this review, you confirm that it&#39;s based on your genuine experience\n                  and doesn't contain false information. Your review will be moderated before publication.\n                ", [26830, 27054], "\n                  By submitting this review, you confirm that it&rsquo;s based on your genuine experience\n                  and doesn't contain false information. Your review will be moderated before publication.\n                ", [26830, 27054], "\n                  By submitting this review, you confirm that it's based on your genuine experience\n                  and doesn&apos;t contain false information. Your review will be moderated before publication.\n                ", [26830, 27054], "\n                  By submitting this review, you confirm that it's based on your genuine experience\n                  and doesn&lsquo;t contain false information. Your review will be moderated before publication.\n                ", [26830, 27054], "\n                  By submitting this review, you confirm that it's based on your genuine experience\n                  and doesn&#39;t contain false information. Your review will be moderated before publication.\n                ", [26830, 27054], "\n                  By submitting this review, you confirm that it's based on your genuine experience\n                  and doesn&rsquo;t contain false information. Your review will be moderated before publication.\n                ", [2386, 2388], "[checkExistingSession]", [322, 325], [322, 325], [1881, 1884], [1881, 1884], [1892, 1895], [1892, 1895], [1973, 1976], [1973, 1976], [4053, 4056], [4053, 4056], [528, 531], [528, 531], [3600, 3603], [3600, 3603], [3933, 3936], [3933, 3936], [4083, 4086], [4083, 4086], [3242, 3245], [3242, 3245]]
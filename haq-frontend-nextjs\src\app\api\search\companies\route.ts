import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';

// Input validation and sanitization
function validateSearchQuery(query: string): string {
  if (!query || typeof query !== 'string') {
    throw new Error('Search query is required and must be a string');
  }
  
  // Trim whitespace and limit length
  const trimmed = query.trim();
  if (trimmed.length === 0) {
    throw new Error('Search query cannot be empty');
  }
  
  if (trimmed.length > 100) {
    throw new Error('Search query is too long (max 100 characters)');
  }
  
  return trimmed;
}

function validatePagination(page: string | null, limit: string | null) {
  const pageNum = page ? parseInt(page, 10) : 1;
  const limitNum = limit ? parseInt(limit, 10) : 10;
  
  if (isNaN(pageNum) || pageNum < 1) {
    throw new Error('Page must be a positive integer');
  }
  
  if (isNaN(limitNum) || limitNum < 1 || limitNum > 50) {
    throw new Error('Limit must be between 1 and 50');
  }
  
  return { page: pageNum, limit: limitNum };
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const query = searchParams.get('q');
    const page = searchParams.get('page');
    const limit = searchParams.get('limit');
    
    // Validate inputs
    if (!query) {
      return NextResponse.json(
        { error: 'Search query parameter "q" is required' },
        { status: 400 }
      );
    }
    
    const searchQuery = validateSearchQuery(query);
    const { page: pageNum, limit: limitNum } = validatePagination(page, limit);
    
    // Calculate offset for pagination
    const offset = (pageNum - 1) * limitNum;
    
    // Perform case-insensitive search using ILIKE (PostgreSQL)
    // Using parameterized queries to prevent SQL injection
    const { data: companies, error: searchError, count } = await supabase
      .from('companies')
      .select(`
        company_id,
        name,
        industry,
        location,
        website_url,
        logo_url,
        employee_count_range,
        haq_score,
        total_reviews,
        created_at
      `, { count: 'exact' })
      .ilike('name', `%${searchQuery}%`) // Case-insensitive search
      .range(offset, offset + limitNum - 1)
      .order('name', { ascending: true });
    
    if (searchError) {
      console.error('Database search error:', searchError);
      return NextResponse.json(
        { error: 'Failed to search companies' },
        { status: 500 }
      );
    }
    
    // Calculate pagination metadata
    const total = count || 0;
    const totalPages = Math.ceil(total / limitNum);
    const hasNext = pageNum < totalPages;
    const hasPrev = pageNum > 1;
    
    // Prepare response with pagination
    const response = {
      companies: companies || [],
      pagination: {
        page: pageNum,
        limit: limitNum,
        total,
        totalPages,
        hasNext,
        hasPrev
      }
    };
    
    // Set caching headers according to HAQ rules (API_MEDIUM policy)
    const headers = new Headers({
      'Content-Type': 'application/json',
      'Cache-Control': 'public, max-age=60, s-maxage=60', // 1-minute cache as per RULE-402
      'Vary': 'Accept-Encoding',
      'X-Content-Type-Options': 'nosniff',
      'X-Frame-Options': 'DENY'
    });
    
    return new NextResponse(JSON.stringify(response), {
      status: 200,
      headers
    });
    
  } catch (error) {
    console.error('Search API error:', error);
    
    // Handle validation errors
    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }
    
    // Handle unexpected errors
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Only allow GET requests
export async function POST() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405, headers: { 'Allow': 'GET' } }
  );
}

export async function PUT() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405, headers: { 'Allow': 'GET' } }
  );
}

export async function DELETE() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405, headers: { 'Allow': 'GET' } }
  );
}

export async function PATCH() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405, headers: { 'Allow': 'GET' } }
  );
}

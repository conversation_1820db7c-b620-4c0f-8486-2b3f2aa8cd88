{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 148, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Haq%20website%20v1/haq-frontend-nextjs/src/lib/supabase.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js'\nimport { createBrowserClient } from '@supabase/ssr'\n\n// Supabase configuration\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!\n\n// Singleton pattern to prevent multiple client instances (Context7 best practice)\nlet browserClientInstance: ReturnType<typeof createBrowserClient> | null = null\nlet serverClientInstance: ReturnType<typeof createClient> | null = null\n\n// Client-side Supabase client (for browser components)\nexport const createClientComponentClient = () => {\n  if (typeof window === 'undefined') {\n    // Server-side: create a new instance each time\n    return createBrowserClient(supabaseUrl, supabaseAnonKey)\n  }\n\n  // Client-side: use singleton\n  if (!browserClientInstance) {\n    browserClientInstance = createBrowserClient(supabaseUrl, supabase<PERSON>non<PERSON>ey)\n  }\n  return browserClientInstance\n}\n\n// Server-side Supabase client (for API routes and server components)\nexport const createServerClient = () => {\n  if (!serverClientInstance) {\n    serverClientInstance = createClient(supabaseUrl, supabaseAnonKey)\n  }\n  return serverClientInstance\n}\n\n// Main client for general use (uses public schema by default)\nexport const supabase = createServerClient()\n\n// Database types (will be generated later)\nexport type Database = {\n  haq_users_db: {\n    Tables: {\n      users: {\n        Row: {\n          user_id: string\n          username: string\n          email: string\n          password_hash: string\n          role: 'user' | 'admin'\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          user_id?: string\n          username: string\n          email: string\n          password_hash: string\n          role?: 'user' | 'admin'\n          created_at?: string\n          updated_at?: string\n        }\n        Update: {\n          user_id?: string\n          username?: string\n          email?: string\n          password_hash?: string\n          role?: 'user' | 'admin'\n          created_at?: string\n          updated_at?: string\n        }\n      }\n    }\n  }\n  haq_content_db: {\n    Tables: {\n      companies: {\n        Row: {\n          company_id: string\n          name: string\n          slug: string\n          industry: string | null\n          location: string | null\n          description: string | null\n          website_url: string | null\n          logo_url: string | null\n          employee_count_range: string | null\n          founded_year: number | null\n          haq_score: number\n          total_reviews: number\n          is_verified: boolean\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          company_id?: string\n          name: string\n          slug: string\n          industry?: string | null\n          location?: string | null\n          description?: string | null\n          website_url?: string | null\n          logo_url?: string | null\n          employee_count_range?: string | null\n          founded_year?: number | null\n          haq_score?: number\n          total_reviews?: number\n          is_verified?: boolean\n          created_at?: string\n          updated_at?: string\n        }\n        Update: {\n          company_id?: string\n          name?: string\n          slug?: string\n          industry?: string | null\n          location?: string | null\n          description?: string | null\n          website_url?: string | null\n          logo_url?: string | null\n          employee_count_range?: string | null\n          founded_year?: number | null\n          haq_score?: number\n          total_reviews?: number\n          is_verified?: boolean\n          created_at?: string\n          updated_at?: string\n        }\n      }\n      reviews: {\n        Row: {\n          review_id: string\n          company_id: string\n          anonymous_user_hash: string\n          overall_rating: number | null\n          work_life_balance_rating: number | null\n          compensation_rating: number | null\n          management_rating: number | null\n          culture_rating: number | null\n          title: string\n          pros: string | null\n          cons: string | null\n          advice_to_management: string | null\n          job_title: string | null\n          employment_status: 'current' | 'former' | null\n          employment_duration: string | null\n          department: string | null\n          location: string | null\n          is_approved: boolean\n          is_featured: boolean\n          helpful_count: number\n          created_at: string\n          updated_at: string\n        }\n      }\n      salary_reports: {\n        Row: {\n          salary_id: string\n          company_id: string\n          anonymous_user_hash: string\n          job_title: string\n          department: string | null\n          location: string | null\n          experience_level: 'entry' | 'mid' | 'senior' | 'lead' | 'executive' | null\n          base_salary: number | null\n          bonus: number\n          stock_options: number\n          total_compensation: number | null\n          currency: string\n          employment_type: 'full-time' | 'part-time' | 'contract' | 'internship' | null\n          years_of_experience: number | null\n          years_at_company: number | null\n          is_approved: boolean\n          created_at: string\n          updated_at: string\n        }\n      }\n      company_flags: {\n        Row: {\n          flag_id: string\n          company_id: string\n          flag_type: 'red' | 'green' | null\n          flag_text: string\n          flag_count: number\n          created_at: string\n        }\n      }\n    }\n  }\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AAAA;;;AAEA,yBAAyB;AACzB,MAAM;AACN,MAAM;AAEN,kFAAkF;AAClF,IAAI,wBAAuE;AAC3E,IAAI,uBAA+D;AAG5D,MAAM,8BAA8B;IACzC,wCAAmC;QACjC,+CAA+C;QAC/C,OAAO,CAAA,GAAA,4KAAA,CAAA,sBAAmB,AAAD,EAAE,aAAa;IAC1C;;AAOF;AAGO,MAAM,qBAAqB;IAChC,IAAI,CAAC,sBAAsB;QACzB,uBAAuB,CAAA,GAAA,yLAAA,CAAA,eAAY,AAAD,EAAE,aAAa;IACnD;IACA,OAAO;AACT;AAGO,MAAM,WAAW", "debugId": null}}, {"offset": {"line": 192, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Haq%20website%20v1/haq-frontend-nextjs/src/lib/sanitization.ts"], "sourcesContent": ["/**\n * Input Sanitization Utilities\n * Following HAQ-rules.md RULE-603: INPUT_SANITIZATION\n * \n * This module provides consistent sanitization across the application\n * to prevent XSS attacks and ensure data integrity.\n */\n\nimport DOMPurify from 'dompurify';\n\n// Server-side sanitization (Node.js environment)\nlet purify: typeof DOMPurify;\n\nif (typeof window === 'undefined') {\n  // Server-side: Use jsdom for DOMPurify\n  const { JSDOM } = require('jsdom');\n  const window = new JSDOM('').window;\n  purify = DOMPurify(window as any);\n} else {\n  // Client-side: Use browser's window\n  purify = DOMPurify;\n}\n\n/**\n * Sanitization configuration for different content types\n */\nexport const SanitizationConfig = {\n  // For review text content - no HTML allowed\n  REVIEW_TEXT: {\n    ALLOWED_TAGS: [], // No HTML tags allowed\n    ALLOWED_ATTR: [], // No attributes allowed\n    KEEP_CONTENT: true, // Keep text content\n    RETURN_DOM: false, // Return string\n  },\n  \n  // For search queries - very strict\n  SEARCH_QUERY: {\n    ALLOWED_TAGS: [],\n    ALLOWED_ATTR: [],\n    KEEP_CONTENT: true,\n    RETURN_DOM: false,\n  },\n  \n  // For company names and basic text\n  BASIC_TEXT: {\n    ALLOWED_TAGS: [],\n    ALLOWED_ATTR: [],\n    KEEP_CONTENT: true,\n    RETURN_DOM: false,\n  }\n} as const;\n\n/**\n * Sanitize review text content\n * Removes all HTML tags and attributes while preserving text content\n */\nexport function sanitizeReviewText(text: string | undefined | null): string | null {\n  if (!text || typeof text !== 'string') {\n    return null;\n  }\n  \n  const sanitized = purify.sanitize(text.trim(), SanitizationConfig.REVIEW_TEXT);\n  return sanitized.trim() || null;\n}\n\n/**\n * Sanitize search query input\n * Very strict sanitization for search terms\n */\nexport function sanitizeSearchQuery(query: string | undefined | null): string {\n  if (!query || typeof query !== 'string') {\n    return '';\n  }\n  \n  const sanitized = purify.sanitize(query.trim(), SanitizationConfig.SEARCH_QUERY);\n  return sanitized.trim();\n}\n\n/**\n * Sanitize basic text input (company names, usernames, etc.)\n */\nexport function sanitizeBasicText(text: string | undefined | null): string | null {\n  if (!text || typeof text !== 'string') {\n    return null;\n  }\n  \n  const sanitized = purify.sanitize(text.trim(), SanitizationConfig.BASIC_TEXT);\n  return sanitized.trim() || null;\n}\n\n/**\n * Validate and sanitize email addresses\n */\nexport function sanitizeEmail(email: string | undefined | null): string | null {\n  if (!email || typeof email !== 'string') {\n    return null;\n  }\n  \n  // Basic email validation regex\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n  const trimmedEmail = email.trim().toLowerCase();\n  \n  if (!emailRegex.test(trimmedEmail)) {\n    return null;\n  }\n  \n  // Sanitize to remove any potential XSS\n  const sanitized = purify.sanitize(trimmedEmail, SanitizationConfig.BASIC_TEXT);\n  return sanitized;\n}\n\n/**\n * Sanitize URL inputs\n */\nexport function sanitizeUrl(url: string | undefined | null): string | null {\n  if (!url || typeof url !== 'string') {\n    return null;\n  }\n  \n  try {\n    // Validate URL format\n    const urlObj = new URL(url.trim());\n    \n    // Only allow http and https protocols\n    if (!['http:', 'https:'].includes(urlObj.protocol)) {\n      return null;\n    }\n    \n    // Sanitize the URL string\n    const sanitized = purify.sanitize(urlObj.toString(), SanitizationConfig.BASIC_TEXT);\n    return sanitized;\n  } catch (error) {\n    // Invalid URL format\n    return null;\n  }\n}\n\n/**\n * Batch sanitize an object's string properties\n */\nexport function sanitizeObject<T extends Record<string, any>>(\n  obj: T,\n  sanitizer: (value: string) => string | null = sanitizeBasicText\n): Partial<T> {\n  const sanitized: Partial<T> = {};\n  \n  for (const [key, value] of Object.entries(obj)) {\n    if (typeof value === 'string') {\n      const sanitizedValue = sanitizer(value);\n      if (sanitizedValue !== null) {\n        (sanitized as any)[key] = sanitizedValue;\n      }\n    } else if (value !== null && value !== undefined) {\n      // Keep non-string values as-is\n      (sanitized as any)[key] = value;\n    }\n  }\n  \n  return sanitized;\n}\n\n/**\n * PII Detection Utilities\n */\nexport const PIIDetection = {\n  // Common PII keywords to detect\n  KEYWORDS: [\n    // Names and titles\n    'manager', 'ceo', 'director', 'supervisor', 'boss', 'lead', 'president',\n    'vice president', 'vp', 'senior', 'junior', 'head of', 'chief',\n    \n    // Common names (basic set)\n    'john', 'jane', 'smith', 'johnson', 'williams', 'brown', 'jones',\n    'garcia', 'miller', 'davis', 'rodriguez', 'martinez', 'hernandez',\n    \n    // Contact information\n    'email', 'phone', 'address', 'linkedin', 'facebook', 'twitter',\n    'instagram', 'whatsapp', 'telegram', 'skype',\n    \n    // Personal identifiers\n    'my name', 'i am', 'called me', 'told me personally', 'my manager',\n    'my boss', 'spoke to me', 'said to me', 'personally told',\n    \n    // Location specifics that might identify\n    'my office', 'my desk', 'my team', 'my department', 'my floor'\n  ],\n  \n  /**\n   * Detect potential PII in text content\n   */\n  detectPII(text: string): string[] {\n    if (!text || typeof text !== 'string') {\n      return [];\n    }\n    \n    const lowerText = text.toLowerCase();\n    const detectedKeywords: string[] = [];\n    \n    for (const keyword of this.KEYWORDS) {\n      if (lowerText.includes(keyword)) {\n        detectedKeywords.push(keyword);\n      }\n    }\n    \n    return detectedKeywords;\n  },\n  \n  /**\n   * Check if text contains potential PII\n   */\n  hasPII(text: string): boolean {\n    return this.detectPII(text).length > 0;\n  },\n  \n  /**\n   * Get PII warnings for review form fields\n   */\n  getReviewWarnings(reviewData: {\n    pros?: string;\n    cons?: string;\n    advice_management?: string;\n  }): Array<{ field: string; keywords: string[] }> {\n    const warnings: Array<{ field: string; keywords: string[] }> = [];\n    \n    if (reviewData.pros) {\n      const keywords = this.detectPII(reviewData.pros);\n      if (keywords.length > 0) {\n        warnings.push({ field: 'pros', keywords });\n      }\n    }\n    \n    if (reviewData.cons) {\n      const keywords = this.detectPII(reviewData.cons);\n      if (keywords.length > 0) {\n        warnings.push({ field: 'cons', keywords });\n      }\n    }\n    \n    if (reviewData.advice_management) {\n      const keywords = this.detectPII(reviewData.advice_management);\n      if (keywords.length > 0) {\n        warnings.push({ field: 'advice_management', keywords });\n      }\n    }\n    \n    return warnings;\n  }\n};\n\n/**\n * Validation utilities\n */\nexport const ValidationUtils = {\n  /**\n   * Validate UUID format\n   */\n  isValidUUID(uuid: string): boolean {\n    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;\n    return uuidRegex.test(uuid);\n  },\n  \n  /**\n   * Validate rating value\n   */\n  isValidRating(rating: number): boolean {\n    return Number.isInteger(rating) && rating >= 1 && rating <= 5;\n  },\n  \n  /**\n   * Validate text length\n   */\n  isValidTextLength(text: string, maxLength: number = 5000): boolean {\n    return typeof text === 'string' && text.length <= maxLength;\n  }\n};\n\nexport default {\n  sanitizeReviewText,\n  sanitizeSearchQuery,\n  sanitizeBasicText,\n  sanitizeEmail,\n  sanitizeUrl,\n  sanitizeObject,\n  PIIDetection,\n  ValidationUtils,\n  SanitizationConfig\n};\n"], "names": [], "mappings": "AAAA;;;;;;CAMC;;;;;;;;;;;;AAED;;AAEA,iDAAiD;AACjD,IAAI;AAEJ,wCAAmC;IACjC,uCAAuC;IACvC,MAAM,EAAE,KAAK,EAAE;IACf,MAAM,SAAS,IAAI,MAAM,IAAI,MAAM;IACnC,SAAS,CAAA,GAAA,oJAAA,CAAA,UAAS,AAAD,EAAE;AACrB,OAAO;;AAGP;AAKO,MAAM,qBAAqB;IAChC,4CAA4C;IAC5C,aAAa;QACX,cAAc,EAAE;QAChB,cAAc,EAAE;QAChB,cAAc;QACd,YAAY;IACd;IAEA,mCAAmC;IACnC,cAAc;QACZ,cAAc,EAAE;QAChB,cAAc,EAAE;QAChB,cAAc;QACd,YAAY;IACd;IAEA,mCAAmC;IACnC,YAAY;QACV,cAAc,EAAE;QAChB,cAAc,EAAE;QAChB,cAAc;QACd,YAAY;IACd;AACF;AAMO,SAAS,mBAAmB,IAA+B;IAChE,IAAI,CAAC,QAAQ,OAAO,SAAS,UAAU;QACrC,OAAO;IACT;IAEA,MAAM,YAAY,OAAO,QAAQ,CAAC,KAAK,IAAI,IAAI,mBAAmB,WAAW;IAC7E,OAAO,UAAU,IAAI,MAAM;AAC7B;AAMO,SAAS,oBAAoB,KAAgC;IAClE,IAAI,CAAC,SAAS,OAAO,UAAU,UAAU;QACvC,OAAO;IACT;IAEA,MAAM,YAAY,OAAO,QAAQ,CAAC,MAAM,IAAI,IAAI,mBAAmB,YAAY;IAC/E,OAAO,UAAU,IAAI;AACvB;AAKO,SAAS,kBAAkB,IAA+B;IAC/D,IAAI,CAAC,QAAQ,OAAO,SAAS,UAAU;QACrC,OAAO;IACT;IAEA,MAAM,YAAY,OAAO,QAAQ,CAAC,KAAK,IAAI,IAAI,mBAAmB,UAAU;IAC5E,OAAO,UAAU,IAAI,MAAM;AAC7B;AAKO,SAAS,cAAc,KAAgC;IAC5D,IAAI,CAAC,SAAS,OAAO,UAAU,UAAU;QACvC,OAAO;IACT;IAEA,+BAA+B;IAC/B,MAAM,aAAa;IACnB,MAAM,eAAe,MAAM,IAAI,GAAG,WAAW;IAE7C,IAAI,CAAC,WAAW,IAAI,CAAC,eAAe;QAClC,OAAO;IACT;IAEA,uCAAuC;IACvC,MAAM,YAAY,OAAO,QAAQ,CAAC,cAAc,mBAAmB,UAAU;IAC7E,OAAO;AACT;AAKO,SAAS,YAAY,GAA8B;IACxD,IAAI,CAAC,OAAO,OAAO,QAAQ,UAAU;QACnC,OAAO;IACT;IAEA,IAAI;QACF,sBAAsB;QACtB,MAAM,SAAS,IAAI,IAAI,IAAI,IAAI;QAE/B,sCAAsC;QACtC,IAAI,CAAC;YAAC;YAAS;SAAS,CAAC,QAAQ,CAAC,OAAO,QAAQ,GAAG;YAClD,OAAO;QACT;QAEA,0BAA0B;QAC1B,MAAM,YAAY,OAAO,QAAQ,CAAC,OAAO,QAAQ,IAAI,mBAAmB,UAAU;QAClF,OAAO;IACT,EAAE,OAAO,OAAO;QACd,qBAAqB;QACrB,OAAO;IACT;AACF;AAKO,SAAS,eACd,GAAM,EACN,YAA8C,iBAAiB;IAE/D,MAAM,YAAwB,CAAC;IAE/B,KAAK,MAAM,CAAC,KAAK,MAAM,IAAI,OAAO,OAAO,CAAC,KAAM;QAC9C,IAAI,OAAO,UAAU,UAAU;YAC7B,MAAM,iBAAiB,UAAU;YACjC,IAAI,mBAAmB,MAAM;gBAC1B,SAAiB,CAAC,IAAI,GAAG;YAC5B;QACF,OAAO,IAAI,UAAU,QAAQ,UAAU,WAAW;YAChD,+BAA+B;YAC9B,SAAiB,CAAC,IAAI,GAAG;QAC5B;IACF;IAEA,OAAO;AACT;AAKO,MAAM,eAAe;IAC1B,gCAAgC;IAChC,UAAU;QACR,mBAAmB;QACnB;QAAW;QAAO;QAAY;QAAc;QAAQ;QAAQ;QAC5D;QAAkB;QAAM;QAAU;QAAU;QAAW;QAEvD,2BAA2B;QAC3B;QAAQ;QAAQ;QAAS;QAAW;QAAY;QAAS;QACzD;QAAU;QAAU;QAAS;QAAa;QAAY;QAEtD,sBAAsB;QACtB;QAAS;QAAS;QAAW;QAAY;QAAY;QACrD;QAAa;QAAY;QAAY;QAErC,uBAAuB;QACvB;QAAW;QAAQ;QAAa;QAAsB;QACtD;QAAW;QAAe;QAAc;QAExC,yCAAyC;QACzC;QAAa;QAAW;QAAW;QAAiB;KACrD;IAED;;GAEC,GACD,WAAU,IAAY;QACpB,IAAI,CAAC,QAAQ,OAAO,SAAS,UAAU;YACrC,OAAO,EAAE;QACX;QAEA,MAAM,YAAY,KAAK,WAAW;QAClC,MAAM,mBAA6B,EAAE;QAErC,KAAK,MAAM,WAAW,IAAI,CAAC,QAAQ,CAAE;YACnC,IAAI,UAAU,QAAQ,CAAC,UAAU;gBAC/B,iBAAiB,IAAI,CAAC;YACxB;QACF;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,QAAO,IAAY;QACjB,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,MAAM,GAAG;IACvC;IAEA;;GAEC,GACD,mBAAkB,UAIjB;QACC,MAAM,WAAyD,EAAE;QAEjE,IAAI,WAAW,IAAI,EAAE;YACnB,MAAM,WAAW,IAAI,CAAC,SAAS,CAAC,WAAW,IAAI;YAC/C,IAAI,SAAS,MAAM,GAAG,GAAG;gBACvB,SAAS,IAAI,CAAC;oBAAE,OAAO;oBAAQ;gBAAS;YAC1C;QACF;QAEA,IAAI,WAAW,IAAI,EAAE;YACnB,MAAM,WAAW,IAAI,CAAC,SAAS,CAAC,WAAW,IAAI;YAC/C,IAAI,SAAS,MAAM,GAAG,GAAG;gBACvB,SAAS,IAAI,CAAC;oBAAE,OAAO;oBAAQ;gBAAS;YAC1C;QACF;QAEA,IAAI,WAAW,iBAAiB,EAAE;YAChC,MAAM,WAAW,IAAI,CAAC,SAAS,CAAC,WAAW,iBAAiB;YAC5D,IAAI,SAAS,MAAM,GAAG,GAAG;gBACvB,SAAS,IAAI,CAAC;oBAAE,OAAO;oBAAqB;gBAAS;YACvD;QACF;QAEA,OAAO;IACT;AACF;AAKO,MAAM,kBAAkB;IAC7B;;GAEC,GACD,aAAY,IAAY;QACtB,MAAM,YAAY;QAClB,OAAO,UAAU,IAAI,CAAC;IACxB;IAEA;;GAEC,GACD,eAAc,MAAc;QAC1B,OAAO,OAAO,SAAS,CAAC,WAAW,UAAU,KAAK,UAAU;IAC9D;IAEA;;GAEC,GACD,mBAAkB,IAAY,EAAE,YAAoB,IAAI;QACtD,OAAO,OAAO,SAAS,YAAY,KAAK,MAAM,IAAI;IACpD;AACF;uCAEe;IACb;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;AACF", "debugId": null}}, {"offset": {"line": 465, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Haq%20website%20v1/haq-frontend-nextjs/src/app/api/companies/%5Bid%5D/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { supabase } from '@/lib/supabase';\nimport { ValidationUtils } from '@/lib/sanitization';\n\n/**\n * GET /api/companies/[id]\n * Public endpoint to get single company details\n * Following HAQ-rules.md PUB-03 specification\n * \n * Path Parameters:\n * - id: Company UUID\n * \n * Response includes:\n * - Company details (name, industry, location, etc.)\n * - Aggregated review statistics\n * - NO individual review data (use /api/companies/[id]/reviews for that)\n */\nexport async function GET(\n  request: NextRequest,\n  { params }: { params: { id: string } }\n) {\n  try {\n    const companyId = params.id;\n\n    // Validate UUID format\n    if (!ValidationUtils.isValidUUID(companyId)) {\n      return NextResponse.json(\n        { \n          success: false, \n          message: 'Invalid company ID format',\n          error: 'Company ID must be a valid UUID'\n        },\n        { status: 400 }\n      );\n    }\n\n    // Fetch company details with aggregated review statistics\n    const { data: company, error: companyError } = await supabase\n      .from('companies')\n      .select(`\n        company_id,\n        name,\n        slug,\n        industry,\n        location,\n        website_url,\n        logo_url,\n        employee_count_range,\n        founded_year,\n        description,\n        haq_score,\n        total_reviews,\n        is_verified,\n        created_at,\n        updated_at\n      `)\n      .eq('company_id', companyId)\n      .single();\n\n    if (companyError || !company) {\n      return NextResponse.json(\n        { \n          success: false, \n          message: 'Company not found',\n          error: 'The requested company does not exist'\n        },\n        { status: 404 }\n      );\n    }\n\n    // Get aggregated review statistics for approved reviews only\n    const { data: reviewStats, error: statsError } = await supabase\n      .from('reviews')\n      .select('overall_rating')\n      .eq('company_id', companyId)\n      .eq('status', 'approved'); // Only approved reviews\n\n    // Calculate review statistics\n    let reviewStatistics = {\n      total_reviews: 0,\n      average_rating: 0,\n      rating_distribution: {\n        1: 0,\n        2: 0,\n        3: 0,\n        4: 0,\n        5: 0\n      }\n    };\n\n    if (reviewStats && reviewStats.length > 0) {\n      reviewStatistics.total_reviews = reviewStats.length;\n      \n      // Calculate average rating\n      const totalRating = reviewStats.reduce((sum, review) => sum + review.overall_rating, 0);\n      reviewStatistics.average_rating = Math.round((totalRating / reviewStats.length) * 10) / 10;\n      \n      // Calculate rating distribution\n      reviewStats.forEach(review => {\n        reviewStatistics.rating_distribution[review.overall_rating as keyof typeof reviewStatistics.rating_distribution]++;\n      });\n    }\n\n    // Prepare response data\n    const responseData = {\n      company: {\n        ...company,\n        // Override with calculated statistics\n        total_reviews: reviewStatistics.total_reviews,\n        average_rating: reviewStatistics.average_rating,\n        rating_distribution: reviewStatistics.rating_distribution\n      }\n    };\n\n    return NextResponse.json({\n      success: true,\n      data: responseData\n    }, {\n      headers: {\n        // CDN_SHORT caching policy as per HAQ-rules.md\n        'Cache-Control': 'public, max-age=300, s-maxage=300', // 5 minutes\n        'Vary': 'Accept-Encoding',\n        'X-Content-Type-Options': 'nosniff',\n        'X-Frame-Options': 'DENY'\n      }\n    });\n\n  } catch (error) {\n    console.error('Company details API error:', error);\n    return NextResponse.json(\n      { \n        success: false, \n        message: 'Internal server error',\n        error: 'Unexpected error occurred'\n      },\n      { status: 500 }\n    );\n  }\n}\n\n/**\n * Handle unsupported HTTP methods\n */\nexport async function POST() {\n  return NextResponse.json(\n    { success: false, message: 'Method not allowed' },\n    { status: 405, headers: { 'Allow': 'GET' } }\n  );\n}\n\nexport async function PUT() {\n  return NextResponse.json(\n    { success: false, message: 'Method not allowed' },\n    { status: 405, headers: { 'Allow': 'GET' } }\n  );\n}\n\nexport async function DELETE() {\n  return NextResponse.json(\n    { success: false, message: 'Method not allowed' },\n    { status: 405, headers: { 'Allow': 'GET' } }\n  );\n}\n\nexport async function PATCH() {\n  return NextResponse.json(\n    { success: false, message: 'Method not allowed' },\n    { status: 405, headers: { 'Allow': 'GET' } }\n  );\n}\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;;;;AAeO,eAAe,IACpB,OAAoB,EACpB,EAAE,MAAM,EAA8B;IAEtC,IAAI;QACF,MAAM,YAAY,OAAO,EAAE;QAE3B,uBAAuB;QACvB,IAAI,CAAC,4HAAA,CAAA,kBAAe,CAAC,WAAW,CAAC,YAAY;YAC3C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBACE,SAAS;gBACT,SAAS;gBACT,OAAO;YACT,GACA;gBAAE,QAAQ;YAAI;QAElB;QAEA,0DAA0D;QAC1D,MAAM,EAAE,MAAM,OAAO,EAAE,OAAO,YAAY,EAAE,GAAG,MAAM,wHAAA,CAAA,WAAQ,CAC1D,IAAI,CAAC,aACL,MAAM,CAAC,CAAC;;;;;;;;;;;;;;;;MAgBT,CAAC,EACA,EAAE,CAAC,cAAc,WACjB,MAAM;QAET,IAAI,gBAAgB,CAAC,SAAS;YAC5B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBACE,SAAS;gBACT,SAAS;gBACT,OAAO;YACT,GACA;gBAAE,QAAQ;YAAI;QAElB;QAEA,6DAA6D;QAC7D,MAAM,EAAE,MAAM,WAAW,EAAE,OAAO,UAAU,EAAE,GAAG,MAAM,wHAAA,CAAA,WAAQ,CAC5D,IAAI,CAAC,WACL,MAAM,CAAC,kBACP,EAAE,CAAC,cAAc,WACjB,EAAE,CAAC,UAAU,aAAa,wBAAwB;QAErD,8BAA8B;QAC9B,IAAI,mBAAmB;YACrB,eAAe;YACf,gBAAgB;YAChB,qBAAqB;gBACnB,GAAG;gBACH,GAAG;gBACH,GAAG;gBACH,GAAG;gBACH,GAAG;YACL;QACF;QAEA,IAAI,eAAe,YAAY,MAAM,GAAG,GAAG;YACzC,iBAAiB,aAAa,GAAG,YAAY,MAAM;YAEnD,2BAA2B;YAC3B,MAAM,cAAc,YAAY,MAAM,CAAC,CAAC,KAAK,SAAW,MAAM,OAAO,cAAc,EAAE;YACrF,iBAAiB,cAAc,GAAG,KAAK,KAAK,CAAC,AAAC,cAAc,YAAY,MAAM,GAAI,MAAM;YAExF,gCAAgC;YAChC,YAAY,OAAO,CAAC,CAAA;gBAClB,iBAAiB,mBAAmB,CAAC,OAAO,cAAc,CAAsD;YAClH;QACF;QAEA,wBAAwB;QACxB,MAAM,eAAe;YACnB,SAAS;gBACP,GAAG,OAAO;gBACV,sCAAsC;gBACtC,eAAe,iBAAiB,aAAa;gBAC7C,gBAAgB,iBAAiB,cAAc;gBAC/C,qBAAqB,iBAAiB,mBAAmB;YAC3D;QACF;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;QACR,GAAG;YACD,SAAS;gBACP,+CAA+C;gBAC/C,iBAAiB;gBACjB,QAAQ;gBACR,0BAA0B;gBAC1B,mBAAmB;YACrB;QACF;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8BAA8B;QAC5C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YACE,SAAS;YACT,SAAS;YACT,OAAO;QACT,GACA;YAAE,QAAQ;QAAI;IAElB;AACF;AAKO,eAAe;IACpB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;QAAE,SAAS;QAAO,SAAS;IAAqB,GAChD;QAAE,QAAQ;QAAK,SAAS;YAAE,SAAS;QAAM;IAAE;AAE/C;AAEO,eAAe;IACpB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;QAAE,SAAS;QAAO,SAAS;IAAqB,GAChD;QAAE,QAAQ;QAAK,SAAS;YAAE,SAAS;QAAM;IAAE;AAE/C;AAEO,eAAe;IACpB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;QAAE,SAAS;QAAO,SAAS;IAAqB,GAChD;QAAE,QAAQ;QAAK,SAAS;YAAE,SAAS;QAAM;IAAE;AAE/C;AAEO,eAAe;IACpB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;QAAE,SAAS;QAAO,SAAS;IAAqB,GAChD;QAAE,QAAQ;QAAK,SAAS;YAAE,SAAS;QAAM;IAAE;AAE/C", "debugId": null}}]}
(()=>{var e={};e.id=778,e.ids=[778],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19676:(e,r,s)=>{Promise.resolve().then(s.bind(s,47677))},27910:e=>{"use strict";e.exports=require("stream")},28863:(e,r,s)=>{"use strict";s.d(r,{n:()=>t});class t{static isValidEmail(e){return/^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/.test(e)&&e.length<=255}static isValidUsername(e){return/^[a-zA-Z0-9_]{3,50}$/.test(e)}static validatePassword(e){let r=[];return e.length<8&&r.push("Password must be at least 8 characters long"),e.length>128&&r.push("Password must be less than 128 characters"),/[a-z]/.test(e)||r.push("Password must contain at least one lowercase letter"),/[A-Z]/.test(e)||r.push("Password must contain at least one uppercase letter"),/[0-9]/.test(e)||r.push("Password must contain at least one number"),/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(e)||r.push("Password must contain at least one special character"),{isValid:0===r.length,errors:r}}static sanitizeInput(e){return e.trim().replace(/[<>]/g,"").substring(0,1e3)}}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},41214:(e,r,s)=>{"use strict";s.r(r),s.d(r,{GlobalError:()=>i.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>u,tree:()=>d});var t=s(65239),a=s(48088),n=s(88170),i=s.n(n),o=s(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);s.d(r,l);let d={children:["",{children:["auth",{children:["signup",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,94796)),"D:\\Haq website v1\\haq-frontend-nextjs\\src\\app\\auth\\signup\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,82893)),"D:\\Haq website v1\\haq-frontend-nextjs\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["D:\\Haq website v1\\haq-frontend-nextjs\\src\\app\\auth\\signup\\page.tsx"],m={require:s,loadChunk:()=>Promise.resolve()},u=new t.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/auth/signup/page",pathname:"/auth/signup",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},47677:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>c});var t=s(60687),a=s(43210),n=s(27605),i=s(16189),o=s(85814),l=s.n(o),d=s(28863);function c(){let e=(0,i.useRouter)(),[r,s]=(0,a.useState)(!1),[o,c]=(0,a.useState)(null),[m,u]=(0,a.useState)(null),{register:p,handleSubmit:x,watch:h,formState:{errors:f},setError:b,clearErrors:g}=(0,n.mN)({mode:"onBlur",criteriaMode:"all"}),y=h("password"),v=async r=>{s(!0),c(null),u(null),g();try{if(r.password!==r.confirmPassword){b("confirmPassword",{type:"manual",message:"Passwords do not match"}),s(!1);return}let t=await fetch("/api/auth/register",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({username:r.username,email:r.email,password:r.password})}),a=await t.json();a.success?(u("Account created successfully! Redirecting..."),setTimeout(()=>{e.push("/")},2e3)):(c(a.message||"Registration failed. Please try again."),a.message.includes("email")?b("email",{type:"manual",message:a.message}):a.message.includes("username")&&b("username",{type:"manual",message:a.message}))}catch(e){console.error("Registration error:",e),c("Network error. Please check your connection and try again.")}finally{s(!1)}};return(0,t.jsx)("div",{className:"min-h-screen bg-background-primary flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8",children:(0,t.jsxs)("div",{className:"max-w-md w-full space-y-8",children:[(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"flex justify-center mb-6",children:(0,t.jsx)("div",{className:"w-12 h-12 bg-gradient-to-br from-accent-primary to-accent-secondary rounded-xl flex items-center justify-center",children:(0,t.jsx)("span",{className:"text-white font-bold text-xl",children:"H"})})}),(0,t.jsx)("h2",{className:"text-3xl font-bold text-text-primary",children:"Create your account"}),(0,t.jsx)("p",{className:"mt-2 text-sm text-text-secondary",children:"Join HAQ to discover and review companies"})]}),m&&(0,t.jsx)("div",{className:"bg-green-50 border border-green-200 rounded-medium p-4",children:(0,t.jsxs)("div",{className:"flex",children:[(0,t.jsx)("div",{className:"flex-shrink-0",children:(0,t.jsx)("svg",{className:"h-5 w-5 text-green-400",viewBox:"0 0 20 20",fill:"currentColor",children:(0,t.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",clipRule:"evenodd"})})}),(0,t.jsx)("div",{className:"ml-3",children:(0,t.jsx)("p",{className:"text-sm font-medium text-green-800",children:m})})]})}),o&&(0,t.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-medium p-4",children:(0,t.jsxs)("div",{className:"flex",children:[(0,t.jsx)("div",{className:"flex-shrink-0",children:(0,t.jsx)("svg",{className:"h-5 w-5 text-red-400",viewBox:"0 0 20 20",fill:"currentColor",children:(0,t.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})})}),(0,t.jsx)("div",{className:"ml-3",children:(0,t.jsx)("p",{className:"text-sm font-medium text-red-800",children:o})})]})}),(0,t.jsxs)("form",{className:"mt-8 space-y-6",onSubmit:x(v),children:[(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"username",className:"block text-sm font-medium text-text-primary mb-2",children:"Username"}),(0,t.jsx)("input",{id:"username",type:"text",autoComplete:"username","aria-invalid":f.username?"true":"false",className:`
                  appearance-none relative block w-full px-3 py-3 border rounded-medium placeholder-text-secondary text-text-primary 
                  focus:outline-none focus:ring-2 focus:ring-accent-primary focus:border-accent-primary focus:z-10 sm:text-sm
                  ${f.username?"border-red-300 bg-red-50":"border-border-primary bg-surface-primary hover:border-border-secondary"}
                `,placeholder:"Enter your username",...p("username",{required:"Username is required",minLength:{value:3,message:"Username must be at least 3 characters long"},maxLength:{value:50,message:"Username must be less than 50 characters"},pattern:{value:/^[a-zA-Z0-9_]+$/,message:"Username can only contain letters, numbers, and underscores"},validate:e=>!!d.n.isValidUsername(e)||"Username must be 3-50 characters and contain only letters, numbers, and underscores"})}),f.username&&(0,t.jsx)("p",{className:"mt-1 text-sm text-red-600",role:"alert",children:f.username.message})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-text-primary mb-2",children:"Email address"}),(0,t.jsx)("input",{id:"email",type:"email",autoComplete:"email","aria-invalid":f.email?"true":"false",className:`
                  appearance-none relative block w-full px-3 py-3 border rounded-medium placeholder-text-secondary text-text-primary 
                  focus:outline-none focus:ring-2 focus:ring-accent-primary focus:border-accent-primary focus:z-10 sm:text-sm
                  ${f.email?"border-red-300 bg-red-50":"border-border-primary bg-surface-primary hover:border-border-secondary"}
                `,placeholder:"Enter your email",...p("email",{required:"Email is required",maxLength:{value:255,message:"Email must be less than 255 characters"},pattern:{value:/^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/,message:"Please enter a valid email address"},validate:e=>!!d.n.isValidEmail(e)||"Please enter a valid email address"})}),f.email&&(0,t.jsx)("p",{className:"mt-1 text-sm text-red-600",role:"alert",children:f.email.message})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-text-primary mb-2",children:"Password"}),(0,t.jsx)("input",{id:"password",type:"password",autoComplete:"new-password","aria-invalid":f.password?"true":"false",className:`
                  appearance-none relative block w-full px-3 py-3 border rounded-medium placeholder-text-secondary text-text-primary 
                  focus:outline-none focus:ring-2 focus:ring-accent-primary focus:border-accent-primary focus:z-10 sm:text-sm
                  ${f.password?"border-red-300 bg-red-50":"border-border-primary bg-surface-primary hover:border-border-secondary"}
                `,placeholder:"Create a strong password",...p("password",{required:"Password is required",minLength:{value:8,message:"Password must be at least 8 characters long"},maxLength:{value:128,message:"Password must be less than 128 characters"},validate:e=>{let r=d.n.validatePassword(e);return!!r.isValid||r.errors[0]}})}),f.password&&(0,t.jsx)("p",{className:"mt-1 text-sm text-red-600",role:"alert",children:f.password.message}),(0,t.jsx)("div",{className:"mt-2 text-xs text-text-secondary",children:"Password must contain at least 8 characters with uppercase, lowercase, number, and special character."})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"confirmPassword",className:"block text-sm font-medium text-text-primary mb-2",children:"Confirm Password"}),(0,t.jsx)("input",{id:"confirmPassword",type:"password",autoComplete:"new-password","aria-invalid":f.confirmPassword?"true":"false",className:`
                  appearance-none relative block w-full px-3 py-3 border rounded-medium placeholder-text-secondary text-text-primary 
                  focus:outline-none focus:ring-2 focus:ring-accent-primary focus:border-accent-primary focus:z-10 sm:text-sm
                  ${f.confirmPassword?"border-red-300 bg-red-50":"border-border-primary bg-surface-primary hover:border-border-secondary"}
                `,placeholder:"Confirm your password",...p("confirmPassword",{required:"Please confirm your password",validate:e=>e===y||"Passwords do not match"})}),f.confirmPassword&&(0,t.jsx)("p",{className:"mt-1 text-sm text-red-600",role:"alert",children:f.confirmPassword.message})]})]}),(0,t.jsx)("div",{children:(0,t.jsx)("button",{type:"submit",disabled:r,className:`
                group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-medium text-white 
                focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-accent-primary
                ${r?"bg-gray-400 cursor-not-allowed":"bg-accent-primary hover:bg-accent-secondary"}
              `,children:r?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)("svg",{className:"animate-spin -ml-1 mr-3 h-5 w-5 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,t.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,t.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"Creating account..."]}):"Create account"})}),(0,t.jsx)("div",{className:"text-center",children:(0,t.jsxs)("p",{className:"text-sm text-text-secondary",children:["Already have an account?"," ",(0,t.jsx)(l(),{href:"/auth/login",className:"font-medium text-accent-primary hover:text-accent-secondary transition-colors",children:"Sign in"})]})})]})]})})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},61532:(e,r,s)=>{Promise.resolve().then(s.bind(s,94796))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70440:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>a});var t=s(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,t.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},94796:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>t});let t=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\Haq website v1\\\\haq-frontend-nextjs\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Haq website v1\\haq-frontend-nextjs\\src\\app\\auth\\signup\\page.tsx","default")}};var r=require("../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[447,942,658,605,647],()=>s(41214));module.exports=t})();
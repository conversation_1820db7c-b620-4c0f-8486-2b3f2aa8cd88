(()=>{var e={};e.id=612,e.ids=[612],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},12909:(e,s,t)=>{"use strict";t.d(s,{DU:()=>c,Qi:()=>l,h8:()=>u,ne:()=>d});var r=t(85663),a=t(43205),n=t.n(a);let i=()=>{let e=process.env.JWT_SECRET;if(!e)throw Error("JWT_SECRET environment variable is required");return e},o=process.env.JWT_EXPIRES_IN||"7d";class u{static{this.SALT_ROUNDS=12}static async hashPassword(e){try{let s=await r.Ay.genSalt(this.SALT_ROUNDS);return await r.Ay.hash(e,s)}catch(e){throw Error("Failed to hash password")}}static async verifyPassword(e,s){try{return await r.Ay.compare(e,s)}catch(e){throw Error("Failed to verify password")}}}class c{static generateToken(e){try{return n().sign(e,i(),{expiresIn:o,algorithm:"HS256"})}catch(e){throw Error("Failed to generate JWT token")}}static verifyToken(e){try{return n().verify(e,i(),{algorithms:["HS256"]})}catch(e){if(e instanceof n().TokenExpiredError)throw Error("Token has expired");if(e instanceof n().JsonWebTokenError)throw Error("Invalid token");throw Error("Token verification failed")}}static extractTokenFromHeader(e){return e&&e.startsWith("Bearer ")?e.substring(7):null}}let l={NAME:"haq_auth_token",OPTIONS:{httpOnly:!0,secure:!0,sameSite:"strict",maxAge:604800,path:"/"}};class d{static isValidEmail(e){return/^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/.test(e)&&e.length<=255}static isValidUsername(e){return/^[a-zA-Z0-9_]{3,50}$/.test(e)}static validatePassword(e){let s=[];return e.length<8&&s.push("Password must be at least 8 characters long"),e.length>128&&s.push("Password must be less than 128 characters"),/[a-z]/.test(e)||s.push("Password must contain at least one lowercase letter"),/[A-Z]/.test(e)||s.push("Password must contain at least one uppercase letter"),/[0-9]/.test(e)||s.push("Password must contain at least one number"),/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(e)||s.push("Password must contain at least one special character"),{isValid:0===s.length,errors:s}}static sanitizeInput(e){return e.trim().replace(/[<>]/g,"").substring(0,1e3)}}},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},39727:()=>{},44378:(e,s,t)=>{"use strict";t.r(s),t.d(s,{patchFetch:()=>y,routeModule:()=>g,serverHooks:()=>f,workAsyncStorage:()=>x,workUnitAsyncStorage:()=>w});var r={};t.r(r),t.d(r,{DELETE:()=>m,GET:()=>p,POST:()=>d,PUT:()=>h});var a=t(96559),n=t(48088),i=t(37719),o=t(32190),u=t(12909),c=t(51641),l=t(56621);async function d(e){try{let{username:s,email:t,password:r}=await e.json();if(!s||!t||!r)return o.NextResponse.json({success:!1,message:"Username, email, and password are required"},{status:400});let a=u.ne.sanitizeInput(s),n=u.ne.sanitizeInput(t);if(!u.ne.isValidEmail(n))return o.NextResponse.json({success:!1,message:"Invalid email format"},{status:400});if(!u.ne.isValidUsername(a))return o.NextResponse.json({success:!1,message:"Username must be 3-50 characters long and contain only letters, numbers, and underscores"},{status:400});let i=u.ne.validatePassword(r);if(!i.isValid)return o.NextResponse.json({success:!1,message:`Password validation failed: ${i.errors.join(", ")}`},{status:400});let{data:d,error:p}=await l.ND.from("haq_users_db.users").select("user_id").or(`email.eq.${n},username.eq.${a}`);if(p)return console.error("Database error checking existing user:",p),o.NextResponse.json({success:!1,message:"Internal server error"},{status:500});if(d&&d.length>0)return o.NextResponse.json({success:!1,message:"User with this email or username already exists"},{status:409});let h=await u.h8.hashPassword(r),{data:m,error:g}=await l.ND.from("haq_users_db.users").insert({username:a,email:n,password_hash:h,role:"user"}).select("user_id, username, email, role, created_at").single();if(g){if(console.error("Database error creating user:",g),"23505"===g.code)return o.NextResponse.json({success:!1,message:"User with this email or username already exists"},{status:409});return o.NextResponse.json({success:!1,message:"Failed to create user account"},{status:500})}if(!m)return o.NextResponse.json({success:!1,message:"Failed to create user account"},{status:500});let x=u.DU.generateToken({user_id:m.user_id,role:m.role});return await c.L.setAuthCookie(x),o.NextResponse.json({success:!0,message:"User account created successfully",user:{user_id:m.user_id,username:m.username,email:m.email,role:m.role}},{status:201})}catch(e){if(console.error("Registration error:",e),e instanceof SyntaxError)return o.NextResponse.json({success:!1,message:"Invalid JSON in request body"},{status:400});if(e instanceof Error&&e.message.includes("JWT"))return o.NextResponse.json({success:!1,message:"Authentication setup failed"},{status:500});return o.NextResponse.json({success:!1,message:"Internal server error"},{status:500})}}async function p(){return o.NextResponse.json({success:!1,message:"Method not allowed"},{status:405})}async function h(){return o.NextResponse.json({success:!1,message:"Method not allowed"},{status:405})}async function m(){return o.NextResponse.json({success:!1,message:"Method not allowed"},{status:405})}let g=new a.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/auth/register/route",pathname:"/api/auth/register",filename:"route",bundlePath:"app/api/auth/register/route"},resolvedPagePath:"D:\\Haq website v1\\haq-frontend-nextjs\\src\\app\\api\\auth\\register\\route.ts",nextConfigOutput:"",userland:r}),{workAsyncStorage:x,workUnitAsyncStorage:w,serverHooks:f}=g;function y(){return(0,i.patchFetch)({workAsyncStorage:x,workUnitAsyncStorage:w})}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},51641:(e,s,t)=>{"use strict";t.d(s,{L:()=>i,b:()=>o});var r=t(44999),a=t(12909),n=t(56621);class i{static async setAuthCookie(e){(await (0,r.UL)()).set(a.Qi.NAME,e,a.Qi.OPTIONS)}static async getAuthToken(){try{let e=(await (0,r.UL)()).get(a.Qi.NAME);return e?.value||null}catch(e){return null}}static async removeAuthCookie(){(await (0,r.UL)()).delete(a.Qi.NAME)}}class o{static async getCurrentUser(){try{let e=await i.getAuthToken();if(!e)return null;let s=a.DU.verifyToken(e),{data:t,error:r}=await n.ND.from("users").select("user_id, username, email, role, created_at, updated_at").eq("user_id",s.user_id).limit(1);if(r||!t||0===t.length)return null;return t[0]}catch(e){return null}}static async isAuthenticated(){return null!==await this.getCurrentUser()}static async isAdmin(){try{let e=await i.getAuthToken();if(!e)return!1;let s=a.DU.verifyToken(e);return"admin"===s.role}catch(e){return!1}}static async verifyTokenAndGetUser(e){try{let s=a.DU.verifyToken(e),{data:t,error:r}=await n.ND.from("users").select("user_id, username, email, role, created_at, updated_at").eq("user_id",s.user_id).limit(1);if(r||!t||0===t.length)return null;return t[0]}catch(e){return null}}}},51906:e=>{function s(e){var s=Error("Cannot find module '"+e+"'");throw s.code="MODULE_NOT_FOUND",s}s.keys=()=>[],s.resolve=s,s.id=51906,e.exports=s},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56621:(e,s,t)=>{"use strict";t.d(s,{ND:()=>n});var r=t(39398);t(98766);let a=null,n=a=(0,r.createClient)("https://wqbuilazpyxpwyuwuqpi.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6IndxYnVpbGF6cHl4cHd5dXd1cXBpIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTA1NTYyNDMsImV4cCI6MjA2NjEzMjI0M30.GeRI54Rskwdbfm9_lRxy1-7YQ8vA74JWblNF2GRpzrI")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var s=require("../../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[447,580,398,766,358],()=>t(44378));module.exports=r})();